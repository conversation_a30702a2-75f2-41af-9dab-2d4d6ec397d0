{% extends "base/admin-base.html" %}
{% load static %}

{% block container %}
{% include 'includes/seller-nav.html' %}

<style>
.wizard-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.wizard-progress {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    position: relative;
}

.wizard-progress::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 0;
    right: 0;
    height: 2px;
    background: #e9ecef;
    z-index: 1;
}

.wizard-progress::after {
    content: '';
    position: absolute;
    top: 20px;
    left: 0;
    width: 0%; /* First step - no progress yet */
    height: 2px;
    background: #28a745;
    z-index: 2;
}

.wizard-step {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    position: relative;
    z-index: 3;
}

.wizard-step.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
}

.wizard-step-label {
    position: absolute;
    top: 50px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    white-space: nowrap;
}

.form-section {
    background: white;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-title {
    color: #495057;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
}

.form-control {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 10px 12px;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    width: 100%;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: 0;
}

.help-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

.required-field::after {
    content: ' *';
    color: #dc3545;
}

.wizard-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
}

.btn-primary {
    background: #007bff;
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 4px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    cursor: pointer;
}

.btn-outline-secondary {
    background: transparent;
    border: 1px solid #6c757d;
    color: #6c757d;
    padding: 12px 24px;
    border-radius: 4px;
    text-decoration: none;
    display: inline-block;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}
</style>

<div class="content-body">
    <div class="wizard-container">
        <!-- Progress Indicator -->
        <div class="wizard-progress">
            <div class="wizard-step active">
                <span>1</span>
                <div class="wizard-step-label">Product Details</div>
            </div>
            <div class="wizard-step">
                <span>2</span>
                <div class="wizard-step-label">Colors</div>
            </div>
            <div class="wizard-step">
                <span>3</span>
                <div class="wizard-step-label">Variants</div>
            </div>
            <div class="wizard-step">
                <span>4</span>
                <div class="wizard-step-label">Images</div>
            </div>
            <div class="wizard-step">
                <span>5</span>
                <div class="wizard-step-label">Specifications</div>
            </div>
        </div>

        <!-- Page Header -->
        <div class="text-center mb-4">
            <h2 class="mb-2">Add New Product</h2>
            <p class="text-muted">Start by entering the basic details of your product</p>
        </div>

        <!-- Messages -->
        {% if messages %}
            <div class="mb-4">
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <!-- Important Notice -->
        <div class="alert-warning">
            <strong><i class="fas fa-exclamation-triangle"></i> Important:</strong>
            Please ensure you have created the required <strong>brand</strong> and <strong>category</strong> before proceeding.
            <a href="{% url 'managers:brands' %}" target="_blank">Manage Brands</a> |
            <a href="{% url 'managers:categories' %}" target="_blank">Manage Categories</a>
        </div>
                        
                        <form method="post" enctype="multipart/form-data">
                            {% csrf_token %}
                            
                            <div class="form-row">
                                <!-- Product Model -->
                                <div class="form-group col-12 col-md-6">
                                    <label>Product Model</label>
                                    {{ form.product_model }}
                                </div>
                                <!-- SKU -->
                                <div class="form-group col-12 col-md-6">
                                    <label>SKU</label>
                                    {{ form.sku }}
                                </div>
                                <!-- Name -->
                                <div class="form-group col-12 col-md-6">
                                    <label>Product Name</label>
                                    {{ form.name }}
                                </div>
                                <!-- Description -->
                                <div class="form-group col-12">
                                    <label>Description</label>
                                    {{ form.description }}
                                </div>
                                <!-- Details -->
                                <div class="form-group col-12">
                                    <label>Details</label>
                                    {{ form.details }}
                                </div>
                                <!-- Main Image -->
                                <div class="form-group col-12 col-md-6">
                                    <label>Main Image</label>
                                    {{ form.mainimage }}
                                </div>
                                <!-- Regular Price -->
                                <div class="form-group col-12 col-md-6">
                                    <label>Regular Price</label>
                                    {{ form.regular_price }}
                                    <small class="form-text text-danger">Enter price including VAT</small>
                                </div>
                                <!-- Sale Price -->
                                <div class="form-group col-12 col-md-6">
                                    <label>Sale Price</label>
                                    {{ form.sale_price }}
                                    <small class="form-text text-danger">Enter price including VAT</small>
                                </div>
                                <!-- Offer Percentage -->
                                <!-- <div class="form-group col-12 col-md-6">
                                    <label>Offer Percentage</label>
                                    {{ form.offer_percentage }}
                                </div> -->
                                <!-- Video URL -->
                                <div class="form-group col-12 col-md-6">
                                    <label>Product Video URL</label>
                                    {{ form.video }}
                                </div>
                                <div class="form-group col-12 col-md-6">
                                    <label>Specifications</label>
                                    {{ form.specifications }}
                                </div>
                                <!-- Category -->
                                <div class="form-group col-12 col-md-6">
                                    <label>Category</label>
                                    {{ form.category }}
                                </div>
                                <!-- Brand -->
                                <div class="form-group col-12 col-md-6">
                                    <label>Brand</label>
                                    {{ form.brand }}
                                </div>
                                <!-- Stock -->
                                <div class="form-group col-12 col-md-6">
                                    <label>Stock</label>
                                    {{ form.stock }}
                                </div>
                                <div class="form-group col-12"> <label>Example Frontend Display for Delivery/Guarantee:</label>
                                    <div>
                                        <img src="{% static './images/admin-img3.png' %}"
                                             alt="Example of Delivery and Guarantee display on product page"
                                             class="img-fluid"
                                             style="max-width: 450px; border: 1px solid #ccc; padding: 5px; background-color: #fff; margin-top: 5px; display: block;">
                                             </div>
                                     <small class="form-text text-muted">This image shows how the following Delivery and Guarantee information might appear to customers.</small>
                                </div>
                                <!-- Delivery Title -->
                                <div class="form-group col-12 col-md-6">
                                    <label>Delivery Title</label>
                                    {{ form.delivery_title }}
                                </div>
                                <!-- Delivery Duration -->
                                <div class="form-group col-12 col-md-6">
                                    <label>Delivery Duration</label>
                                    {{ form.delivery_duration }}
                                </div>
                                <!-- Guarantee Title -->
                                <div class="form-group col-12 col-md-6">
                                    <label>Guarantee Title</label>
                                    {{ form.garantee_title }}
                                </div>
                                <!-- Guarantee Time -->
                                <div class="form-group col-12 col-md-6">
                                    <label>Guarantee Time</label>
                                    {{ form.garantee_time }}
                                </div>
                            </div>
                            <!-- RAM Selection Disclaimer -->
                            <!-- <div class="alert alert-warning" role="alert" style="font-weight: bold;">
                                ⚠️ If your product has variants and you want to show RAM options on the product page, please check this box.
                                <br>
                                If your product has no variants, you can ignore this option.
                                </div>
                            </div>

                            <div class="form-group col-12 col-md-6">
                                <label style="margin-right: 30px;">Enable ram selection</label>
                                {{ form.rem_section }}
                            </div> -->
                            
                            <div class="form-group text-right">
                                <button type="submit" class="btn btn-primary">Save and Add Colors</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- #/ container -->
</div>
<!--**********************************
    Content body end
***********************************-->

{% endblock %} 