import React, { useEffect, useState } from "react";
import { useParams, useSearchParams } from "react-router-dom";
import ProductImages from "./ProductImages";
import ProductDetails from "./ProductDetails";
import RelatedProducts from "./RelatedProducts";
import Specifications from "./Specifications";
import Reviews from "./Reviews";
import Footer from "../includes/Footer";
import Header2 from "../includes/Header2";
import LoadingSpinner from "../includes/LoadingSpinner"; // Ensure this exists
import { BASE_URL } from "../../utils/config";

export default function Product() {
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const [productData, setProductData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [wishlistItems, setWishlistItems] = useState([]);
  const [isInWishlist, setIsInWishlist] = useState(false);

  useEffect(() => {
    const fetchProductData = async () => {
      setLoading(true); // Ensure loading is true at the start
      setError(null);   // Reset error
      try {
        const token = localStorage.getItem("access_token");
        const url = `${BASE_URL}/product/${id}/?${searchParams.toString()}`;

        const response = await fetch(url, {
          method: "GET",
          headers: {
            "Authorization": token ? `Bearer ${token}` : "",
            "Content-Type": "application/json",
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log("API Response Data:", data); // <-- Add console log here
        if (data.title) {
          document.title = data.title;
        }
        setProductData(data);
        // Optional Chaining (?.) is safer here in case customer is null/undefined initially
        setWishlistItems(data?.customer?.wishlist_items || []);
        setIsInWishlist(data?.customer?.is_in_wishlist ?? false);
        setError(null);
      } catch (err) {
        console.error("Error fetching product data:", err.message);
        setError(err.message);
        setProductData(null); // Clear data on error
      } finally {
        setLoading(false);
      }
    };

    fetchProductData();
  }, [id, searchParams.toString()]);

  // This useEffect might be redundant if setIsInWishlist is set correctly in fetchProductData
  // useEffect(() => {
  //   if (productData?.product?.id) { // Check if product and its id exist
  //     setIsInWishlist(wishlistItems.includes(productData.product.id));
  //   }
  // }, [wishlistItems, productData]);

  if (loading) return <LoadingSpinner />;
  if (error) return <p>Error loading product data: {error}</p>;
  if (!productData) return <p>No product data available.</p>;

  // --- CORRECTED DESTRUCTURING ---
  const {
    product = {}, // Default to empty object if product is missing
    customer = {}, // Default to empty object
    options,      // Get the 'options' object/null directly
    stock_available: stockAvailable = false,
    pricing = { selected_price: 0, regular_price: 0 }, // Default pricing
    images: variantImages = [], // Default images from top level
    related_products: relatedProducts = [], // Default related products
    reviews = [], // Default reviews
    rating = { avg_rating: 0, review_count: 0 }, // Default rating
  } = productData;

  // Now, safely extract option details *if* options exist
  const uniqueColors = options?.unique_colors || [];
  const groupedOptions = options?.grouped_options || {};
  const uniqueRams = options?.unique_rams || [];
  const uniqueStorages = options?.unique_storages || [];
  // --- END OF CORRECTION ---

  // Image logic - Consider if backend sends base product images when options are null
  // Check the API response logged above for a product without options.
  // Does it send images at the top level (`productData.images`) or within `productData.product.images`?
  // The backend code suggests it sends `image_list` at the top level in the `if not options.exists():` block.
  const images = variantImages.length ? variantImages : (product?.images || []);

  const isInCart = customer?.is_in_cart ?? false;
  const hasOrdered = customer?.has_ordered || false; // Get hasOrdered for Reviews


  // --- Ensure Product Details receives correct props ---
  // Check if product has an id before rendering details
  if (!product.id) {
      return <p>Product details seem incomplete.</p>;
  }

  return (
    <div>
      <Header2 />
      <div className="product-page flex items-center flex-wrap lg:pt-[90px]">
        <div className="w-full lg:w-1/2 mt-[90px] lg:mt-0">
          <ProductImages
            selectedImages={images} // Pass the correctly determined images
            // wishlistItems={wishlistItems}
            // setWishlistItems={setWishlistItems}
            product={product} // Pass the base product object
            isInWishlist={isInWishlist}
            setIsInWishlist={setIsInWishlist}
          />
        </div>

        <div className="w-full lg:w-1/2">
          {/* Pass the safely extracted options */}
          <ProductDetails
            product={product} // Pass the base product data (including product.options if needed)
            uniqueColors={uniqueColors}
            groupedOptions={groupedOptions}
            uniqueRams={uniqueRams}
            uniqueStorages={uniqueStorages}
            isInCart={isInCart}
            isInWishlist={isInWishlist}
            stockAvailable={stockAvailable}
            selectedPrice={pricing.selected_price} // Pass pricing directly
            regularPrice={pricing.regular_price}
            // onAddToCart might be needed if you want ProductDetails to trigger a cart count update in Header2
          />
        </div>
      </div>

      <div className="product-page">
        {/* Pass related products */}
        <RelatedProducts relatedProducts={relatedProducts} />
        <Specifications
          specifications={product.custom_specs || []} // Use product.specifications
          details={product.details || ""} // Use product.details
        />
        <Reviews
          reviews={reviews} // Pass reviews
          avgRating={rating.avg_rating} // Pass rating details
          reviewCount={rating.review_count}
          hasOrdered={hasOrdered} // Pass hasOrdered status
          productId={product.id} // Pass product ID
        />
      </div>
      <Footer />
    </div>
  );
}