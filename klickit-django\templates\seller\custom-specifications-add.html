{% extends "base/admin-base.html" %}
{% block container %}
{% load static %}

{% include 'includes/seller-nav.html' %}

<!--**********************************
    Content body start
***********************************-->
<div class="content-body">
    <div class="row page-titles mx-0">
        <div class="col p-md-0">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="javascript:void(0)">{{sub_title}}</a></li>
                <li class="breadcrumb-item active"><a href="javascript:void(0)">{{name}}</a></li>
            </ol>
        </div>
    </div>
    <!-- row -->

    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card" style="background-color: #b5c8eb;">
                    <div class="card-body">
                        <h4 class="card-title">{{name}}</h4>
                        <div class="basic-form">
                            <form action="" method="post">
                                {% csrf_token %}
                                <div class="form-row">
                                    <div class="form-group col-12 col-md-4">
                                        <label>Product (Optional)</label>
                                        {{ form.product }}
                                    </div>
                                    <div class="form-group col-12 col-md-4">
                                        <label>Key</label>
                                        {{ form.key }}
                                    </div>
                                    <div class="form-group col-12 col-md-4">
                                        <label>Value</label>
                                        {{ form.value }}
                                    </div>

                                    <div class="form-group col-12 mt-3">
                                        <button class="btn btn-primary" type="submit">
                                            {% if instance %}Update{% else %}Create{% endif %} Specification
                                        </button>
                                        <a href="{% url 'seller:custom_specifications' %}" class="btn btn-secondary ml-2">Cancel</a>
                                    </div>
                                </div>
                                
                                {% if form.errors %}
                                    <div class="alert alert-danger mt-3">
                                        <strong>Error:</strong> Please correct the errors below.
                                        {{ form.errors }}
                                    </div>
                                {% endif %}
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--**********************************
    Content body end
***********************************-->

{% endblock %}
