import React from 'react';
import { use<PERSON>ara<PERSON>, <PERSON> } from 'react-router-dom';

const OrderSuccess = () => {
  const { orderId } = useParams(); // To optionally display the order ID

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-green-500 p-6">
      <div className="bg-white rounded-lg shadow-xl p-8 sm:p-12 text-center max-w-md w-full">
        <div className="mb-6">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-5 ring-4 ring-green-200 shadow-lg">
            <svg className="w-12 h-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Order Successful!</h1>
          <p className="text-gray-600 text-lg">Thank you for your purchase.</p>
          {orderId && (
            <p className="text-sm text-gray-500 mt-3">
              Your Order ID: <span className="font-semibold">{orderId}</span>
            </p>
          )}
        </div>

        <div className="mt-10 space-y-4">
          <Link
            to="/orders"
            className="block w-full py-3 px-4 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50 transition-colors text-lg shadow-md"
          >
            View Orders
          </Link>
          <Link
            to="/"
            className="block w-full py-3 px-4 bg-gray-200 text-gray-800 font-semibold rounded-lg hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 transition-colors text-lg shadow-md"
          >
            Continue Shopping
          </Link>
        </div>
      </div>
    </div>
  );
};

export default OrderSuccess; 