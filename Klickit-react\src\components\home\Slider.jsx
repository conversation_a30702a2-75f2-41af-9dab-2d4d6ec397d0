import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Swiper from 'swiper';
import { Autoplay, Pagination } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/pagination';
import LoadingSpinner from '../includes/LoadingSpinner'; // Ensure this exists
import AttractiveSectionLoader from '../includes/AttractiveSectionLoader';
import { BASE_URL } from "../../utils/config";

const Slider = () => {
  const [sliders, setSliders] = useState([]);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchSliders = async () => {
      try {
        const response = await fetch(`${BASE_URL}/`);
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const data = await response.json();
        console.log("API Response:", data);
        if (data.title) {
          document.title = data.title;
        }
        setSliders(data.slider || data);
      } catch (error) {
        console.error('Error fetching slider data:', error);
        setError('Failed to load slider data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchSliders();
  }, []);

  useEffect(() => {
    let swiperInstance = null;
    if (sliders.length) {
      swiperInstance = new Swiper('.mySwiper', {
        modules: [Autoplay, Pagination],
        centeredSlides: false,
        slidesPerView: 1,
        autoplay: {
          delay: 2000,
          disableOnInteraction: false,
        },
        pagination: {
          el: '.swiper-pagination',
          clickable: true,
        },
      });
    }

    return () => {
      if (swiperInstance) {
        swiperInstance.destroy();
      }
    };
  }, [sliders]);

  if (loading) return <AttractiveSectionLoader />;

  if (error) {
    return <p className="text-red-500 text-center py-10">{error}</p>;
  }

  if (!sliders.length) {
    return <p className="text-center text-gray-500 py-10">No sliders available.</p>;
  }

  return (
    <section className="w-[95%] m-auto pt-5 md:pb-10 pb-2 md:mt-[70px] mt-[130px]">
      <section className="swiper mySwiper">
        <div className="swiper-wrapper">
          {sliders.map((slider, index) => (
            <div className="swiper-slide" key={index}>
              <a onClick={() => navigate(slider.url)} className="cursor-pointer w-full block">
                <img
                  src={slider.image}
                  alt={`Slider ${index + 1}: ${slider.name}`}
                  className="w-full block rounded-lg object-cover"
                />
              </a>
            </div>
          ))}
        </div>
        <div className="swiper-pagination"></div>
      </section>
    </section>
  );
};

export default Slider;
