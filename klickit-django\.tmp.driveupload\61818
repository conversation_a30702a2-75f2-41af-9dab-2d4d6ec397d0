{% extends "base/admin-base.html" %}
{% load static %}
{% block container %}
{% include 'includes/seller-nav.html' %}

<style>
.wizard-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.wizard-progress {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    position: relative;
}

.wizard-progress::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 0;
    right: 0;
    height: 2px;
    background: #e9ecef;
    z-index: 1;
}

.wizard-progress::after {
    content: '';
    position: absolute;
    top: 20px;
    left: 0;
    width: 60%; /* 3 out of 5 steps completed */
    height: 2px;
    background: #28a745;
    z-index: 2;
}

.wizard-step {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    position: relative;
    z-index: 3;
}

.wizard-step.completed {
    background: #28a745;
    border-color: #28a745;
    color: white;
}

.wizard-step.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
}

.wizard-step-label {
    position: absolute;
    top: 50px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    white-space: nowrap;
}

.variant-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    background: #f8f9fa;
    position: relative;
}

.variant-card.new-variant {
    border-color: #007bff;
    background: #f0f8ff;
}

.variant-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.variant-number {
    background: #007bff;
    color: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}

.remove-variant {
    background: #dc3545;
    border: none;
    color: white;
    border-radius: 4px;
    padding: 5px 10px;
    cursor: pointer;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 0;
}

.form-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
    display: block;
}

.form-control {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: 0;
}

.attributes-section {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin-top: 15px;
}

.attribute-row {
    display: grid;
    grid-template-columns: 1fr 2fr auto;
    gap: 10px;
    align-items: end;
    margin-bottom: 10px;
}

.btn-add-attribute {
    background: #28a745;
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.btn-remove-attribute {
    background: #dc3545;
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.wizard-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
}

.btn-primary, .btn-success {
    padding: 10px 20px;
    border-radius: 4px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-outline-secondary {
    background: transparent;
    border: 1px solid #6c757d;
    color: #6c757d;
    padding: 10px 20px;
    border-radius: 4px;
    text-decoration: none;
    display: inline-block;
}

.help-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

.existing-variants {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.variant-list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    margin-bottom: 10px;
    background: #f8f9fa;
}

.variant-info {
    flex: 1;
}

.variant-name {
    font-weight: 600;
    color: #495057;
}

.variant-details {
    font-size: 12px;
    color: #6c757d;
    margin-top: 2px;
}

.price-badge {
    background: #28a745;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.stock-badge {
    background: #17a2b8;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    margin-left: 5px;
}
</style>

<div class="content-body">
    <div class="wizard-container">
        <!-- Progress Indicator -->
        <div class="wizard-progress">
            <div class="wizard-step completed">
                <span>1</span>
                <div class="wizard-step-label">Product Details</div>
            </div>
            <div class="wizard-step completed">
                <span>2</span>
                <div class="wizard-step-label">Colors</div>
            </div>
            <div class="wizard-step active">
                <span>3</span>
                <div class="wizard-step-label">Variants</div>
            </div>
            <div class="wizard-step">
                <span>4</span>
                <div class="wizard-step-label">Images</div>
            </div>
            <div class="wizard-step">
                <span>5</span>
                <div class="wizard-step-label">Specifications</div>
            </div>
        </div>

        <!-- Page Header -->
        <div class="text-center mb-4">
            <h2 class="mb-2">Add Product Variants</h2>
            <p class="text-muted">Create different variations of your product (e.g., different sizes, colors, storage options)</p>
            <div class="alert alert-info">
                <strong>Product:</strong> {{ product.name }}
            </div>
        </div>
        <!-- Messages -->
        {% if messages %}
            <div class="mb-4">
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <!-- Existing Variants -->
        {% if variants_added %}
            <div class="existing-variants">
                <h4 class="mb-3">
                    <i class="fas fa-check-circle text-success"></i>
                    Existing Variants ({{ variants_added.count }})
                </h4>
                {% for variant in variants_added %}
                    <div class="variant-list-item">
                        <div class="variant-info">
                            <div class="variant-name">{{ variant.name }}</div>
                            <div class="variant-details">
                                {% if variant.color %}Color: {{ variant.color.name }} | {% endif %}
                                SKU: {{ variant.sku|default:"Auto-generated" }}
                                {% if variant.attribute_values.all %}
                                    | {% for attr_val in variant.attribute_values.all %}{{ attr_val.attribute.name }}: {{ attr_val.value }}{% if not forloop.last %}, {% endif %}{% endfor %}
                                {% endif %}
                            </div>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="price-badge">AED {{ variant.sale_price }}</span>
                            <span class="stock-badge">{{ variant.stock }} in stock</span>
                            <a href="{% url 'managers:delete_wizard_variant' product_id=product.id pk=variant.pk %}"
                               class="btn btn-sm btn-outline-danger ml-2"
                               onclick="return confirm('Are you sure you want to delete this variant?');">
                                <i class="fas fa-trash"></i>
                            </a>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
        <!-- Add New Variants Form -->
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-plus-circle text-primary"></i>
                    Add New Variants
                </h4>
                <small class="text-muted">Add different variations of your product with unique attributes</small>
            </div>
            <div class="card-body">
                <form method="post" id="variants-form">
                    {% csrf_token %}
                    <div id="variant-rows-container">
                        <div class="variant-card new-variant variant-row">
                            <div class="variant-header">
                                <div class="variant-number">1</div>
                                <button type="button" class="remove-variant" style="display: none;">
                                    <i class="fas fa-times"></i> Remove
                                </button>
                            </div>

                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="name">Variant Name *</label>
                                    <input type="text" name="name" class="form-control" placeholder="e.g., iPhone 16 Pro Max 256GB" required>
                                    <div class="help-text">Give this variant a descriptive name</div>
                                </div>

                                <div class="form-group">
                                    <label for="color">Color</label>
                                    <select name="color" class="form-control">
                                        <option value="">No specific color</option>
                                        {% for color in available_colors %}
                                            <option value="{{ color.id }}">{{ color.name }}</option>
                                        {% endfor %}
                                    </select>
                                    <div class="help-text">Select a color if applicable</div>
                                </div>

                                <div class="form-group">
                                    <label for="regular_price">Regular Price (AED)</label>
                                    <input type="number" step="0.01" name="regular_price" class="form-control" placeholder="0.00">
                                    <div class="help-text">Original price (optional)</div>
                                </div>

                                <div class="form-group">
                                    <label for="sale_price">Sale Price (AED) *</label>
                                    <input type="number" step="0.01" name="sale_price" class="form-control" placeholder="0.00" required>
                                    <div class="help-text">Current selling price</div>
                                </div>

                                <div class="form-group">
                                    <label for="stock">Stock Quantity *</label>
                                    <input type="number" name="stock" class="form-control" placeholder="0" required min="0">
                                    <div class="help-text">Available quantity</div>
                                </div>
                            </div>

                            <!-- Attributes Section -->
                            <div class="attributes-section">
                                <h6 class="mb-3">
                                    <i class="fas fa-tags text-info"></i>
                                    Product Attributes
                                </h6>
                                <p class="text-muted small mb-3">Add specific attributes like RAM, Storage, Size, etc. to help customers understand this variant.</p>

                                <div class="attribute-container">
                                    <div class="attribute-row">
                                        <div class="form-group">
                                            <label>Attribute Type</label>
                                            <select name="attribute_name" class="form-control">
                                                <option value="">Select attribute type</option>
                                                <option value="RAM">RAM</option>
                                                <option value="Storage">Storage</option>
                                                <option value="Processor">Processor</option>
                                                <option value="Display">Display Size</option>
                                                <option value="Battery">Battery</option>
                                                <option value="Camera">Camera</option>
                                                <option value="Size">Size</option>
                                                <option value="Weight">Weight</option>
                                                <option value="Material">Material</option>
                                                <option value="Connectivity">Connectivity</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>Value</label>
                                            <input type="text" name="attribute_value" class="form-control" placeholder="e.g., 16GB, 512GB, A18 Pro, 6.7 inch">
                                        </div>
                                        <div class="form-group">
                                            <button type="button" class="btn-add-attribute">
                                                <i class="fas fa-plus"></i> Add
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="added-attributes" style="margin-top: 15px;"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Add Another Variant Button -->
                    <div class="text-center mt-4">
                        <button type="button" id="add-variant-row" class="btn btn-outline-secondary">
                            <i class="fas fa-plus"></i> Add Another Variant
                        </button>
                    </div>

                    <!-- Form Actions -->
                    <div class="wizard-actions">
                        <a href="{% url 'managers:seller_add_product_wizard_step2_colors' product_id=product.id %}" class="btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Previous: Colors
                        </a>

                        <div>
                            <button type="submit" class="btn-success mr-3">
                                <i class="fas fa-save"></i> Save Variants
                            </button>
                            <a href="{% url 'managers:seller_add_product_wizard_step4_images' product_id=product.id %}" class="btn-primary">
                                Next: Add Images <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let variantCounter = 1;

    // Add new variant
    document.getElementById('add-variant-row').addEventListener('click', function() {
        variantCounter++;
        const container = document.getElementById('variant-rows-container');
        const firstVariant = container.querySelector('.variant-row');
        const newVariant = firstVariant.cloneNode(true);

        // Update variant number
        newVariant.querySelector('.variant-number').textContent = variantCounter;

        // Clear all inputs
        newVariant.querySelectorAll('input, select').forEach(input => {
            if (input.type !== 'checkbox' && input.type !== 'radio') {
                input.value = '';
            }
            if (input.tagName === 'SELECT') {
                input.selectedIndex = 0;
            }
        });

        // Clear added attributes
        newVariant.querySelector('.added-attributes').innerHTML = '';

        // Show remove button
        newVariant.querySelector('.remove-variant').style.display = 'block';

        container.appendChild(newVariant);
        updateRemoveButtons();
    });

    // Remove variant
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-variant')) {
            const container = document.getElementById('variant-rows-container');
            if (container.children.length > 1) {
                e.target.closest('.variant-row').remove();
                updateVariantNumbers();
                updateRemoveButtons();
            }
        }
    });

    // Add attribute
    document.addEventListener('click', function(e) {
        if (e.target.closest('.btn-add-attribute')) {
            const variantCard = e.target.closest('.variant-card');
            const attributeRow = e.target.closest('.attribute-row');
            const attributeName = attributeRow.querySelector('select[name="attribute_name"]').value;
            const attributeValue = attributeRow.querySelector('input[name="attribute_value"]').value;

            if (attributeName && attributeValue) {
                const addedAttributesContainer = variantCard.querySelector('.added-attributes');

                // Check if attribute already exists
                const existingAttribute = addedAttributesContainer.querySelector(`[data-attribute="${attributeName}"]`);
                if (existingAttribute) {
                    alert('This attribute type already exists for this variant. Please remove it first or choose a different attribute type.');
                    return;
                }

                // Create attribute badge
                const attributeBadge = document.createElement('div');
                attributeBadge.className = 'badge badge-secondary mr-2 mb-2 d-inline-flex align-items-center';
                attributeBadge.setAttribute('data-attribute', attributeName);
                attributeBadge.innerHTML = `
                    <span>${attributeName}: ${attributeValue}</span>
                    <button type="button" class="btn-remove-attribute ml-2" style="background: none; border: none; color: white; font-size: 12px;">
                        <i class="fas fa-times"></i>
                    </button>
                    <input type="hidden" name="attribute_name" value="${attributeName}">
                    <input type="hidden" name="attribute_value" value="${attributeValue}">
                `;

                addedAttributesContainer.appendChild(attributeBadge);

                // Clear inputs
                attributeRow.querySelector('select[name="attribute_name"]').selectedIndex = 0;
                attributeRow.querySelector('input[name="attribute_value"]').value = '';
            } else {
                alert('Please select an attribute type and enter a value.');
            }
        }
    });

    // Remove attribute
    document.addEventListener('click', function(e) {
        if (e.target.closest('.btn-remove-attribute')) {
            e.target.closest('.badge').remove();
        }
    });

    // Update variant numbers
    function updateVariantNumbers() {
        const variants = document.querySelectorAll('.variant-row');
        variants.forEach((variant, index) => {
            variant.querySelector('.variant-number').textContent = index + 1;
        });
        variantCounter = variants.length;
    }

    // Update remove button visibility
    function updateRemoveButtons() {
        const variants = document.querySelectorAll('.variant-row');
        variants.forEach((variant, index) => {
            const removeBtn = variant.querySelector('.remove-variant');
            removeBtn.style.display = variants.length > 1 ? 'block' : 'none';
        });
    }

    // Form validation
    document.getElementById('variants-form').addEventListener('submit', function(e) {
        const variants = document.querySelectorAll('.variant-row');
        let hasValidVariant = false;

        variants.forEach(variant => {
            const name = variant.querySelector('input[name="name"]').value.trim();
            const salePrice = variant.querySelector('input[name="sale_price"]').value.trim();
            const stock = variant.querySelector('input[name="stock"]').value.trim();

            if (name && salePrice && stock) {
                hasValidVariant = true;
            }
        });

        if (!hasValidVariant) {
            e.preventDefault();
            alert('Please fill in at least one complete variant with name, sale price, and stock quantity.');
        }
    });

    // Initialize
    updateRemoveButtons();
});
</script>

{% endblock %}
