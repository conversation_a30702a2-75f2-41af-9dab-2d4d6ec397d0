{% extends "base/admin-base.html" %}
{% block container %}
{% load static %}

{% include 'includes/admin-nav.html' %}

<div class="content-body">
    <div class="row page-titles mx-0">
        <div class="col p-md-0">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="javascript:void(0)">{{ sub_title }}</a></li>
                <li class="breadcrumb-item active"><a href="javascript:void(0)">{{ name }}</a></li>
            </ol>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="heading-line">
                            <h4 class="card-title">{{ name }}</h4>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered zero-configuration">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Image 1</th>
                                        <th>Image 2</th>
                                        <th>Image 3</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for instance in instances %}
                                    <tr>
                                        <td>{{ instance.id }}</td>
                                        <td>{{ instance.name }}</td>
                                        <td><img src="{{ instance.image1.url }}" alt="image" style="width: 80px; border-radius: 8px;"></td>
                                        <td><img src="{{ instance.image2.url }}" alt="image" style="width: 80px; border-radius: 8px;"></td>
                                        <td><img src="{{ instance.image3.url }}" alt="image" style="width: 80px; border-radius: 8px;"></td>
                                        <td>
                                            <a href="{% url 'managers:offers_edit' instance.pk %}" class="btn btn-sm btn-info">Edit</a>
                                            <a href="{% url 'managers:offers_delete' instance.pk %}" class="btn btn-sm btn-danger">Delete</a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
