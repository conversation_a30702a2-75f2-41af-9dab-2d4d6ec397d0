from django.db import models
from main.models import CommonModel
from users.models import User
from items.models import Product, ProductVariant
from managers.models import Seller

ADDRESS_TYPE_CHOICE = (
    ("HM", "Home"),
    ("WO", "Work"),
    ("OT", "Other")
)

ORDER_STATUS = (
    ('IN', 'Initiated'),
    ('PL', 'Placed'),
    ('IP', 'In progress'),
    ('DI', 'Dispatched'),
    ('CO', 'Completed'),
    ('CA', 'Cancelled'),
    ('PA', 'Payment Pending'),
    ('PF', 'Payment Failed'),
    ('PS', 'Payment Successful'),
)

PAYMENT_METHOD_CHOICES = [
    ('COD', 'Cash on Delivery'),
    ('ONLINE', 'Online Payment'),
]



class Customer(CommonModel):
    customer_id = models.CharField(max_length=100)
    user = models.ForeignKey(User, on_delete=models.CASCADE)

    class Meta:
        db_table = 'customers_customer'
        verbose_name = 'customer'
        verbose_name_plural = 'customers'
        ordering = ["-id"]

    def __str__(self):

        return f'{self.user.phone_number}-{self.user.email}'



class CartItem(CommonModel):
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE)
    seller = models.ForeignKey(Seller, on_delete=models.CASCADE, related_name="cart_items", null=True, blank=True)
    variant = models.ForeignKey('items.ProductVariant', on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField(default=1)
    price = models.FloatField()  

    class Meta:
        db_table = 'cart_item'
        verbose_name = 'cart item'
        verbose_name_plural = 'cart items'
        ordering = ('-id',)

    def __str__(self):
        return f"{self.variant.name} (x{self.quantity})"

    def total_price(self):
        return self.price * self.quantity
    


class Wishlist(CommonModel):
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE)
    variant = models.ForeignKey('items.ProductVariant', on_delete=models.CASCADE)

    class Meta:
        db_table = 'customers_wishlist'
        verbose_name = 'wishlist'
        verbose_name_plural = 'wishlists'
        ordering = ["-id"]

    def __str__(self):

        return self.customer.user.email
    



class Service(CommonModel):
    title = models.CharField(max_length=255)
    description = models.TextField()
    image = models.ImageField(upload_to='services/')
    
    def __str__(self):
        return self.title

class ServiceRequest(CommonModel):
    name = models.CharField(max_length=255)
    email = models.EmailField(blank=True, null=True)
    phone = models.CharField(max_length=15)
    service = models.ForeignKey(Service, on_delete=models.CASCADE)
    details = models.TextField()

    def __str__(self):
        return f"Request for {self.service.title} by {self.name}"
    


class Coupon(CommonModel):
    code = models.CharField(max_length=50, unique=True)
    description = models.CharField(max_length=100)
    is_Percentage = models.BooleanField(default=True)
    discount_value = models.DecimalField(max_digits=10, decimal_places=2)
    active = models.BooleanField(default=True)
    valid_from = models.DateTimeField()
    valid_until = models.DateTimeField()
    is_onece_peruser = models.BooleanField(default=False)

    def is_valid(self):
        from django.utils.timezone import now
        return self.active and self.valid_from <= now() <= self.valid_until

    def __str__(self):
        return self.code
    


class Address(CommonModel):
    
    customer = models.ForeignKey(Customer, on_delete=models.SET_NULL, null=True, blank=True)
    address1 = models.CharField(max_length=100)
    address2 = models.CharField(max_length=100)
    city = models.CharField(max_length=25)
    state = models.CharField(max_length=25)
    pincode = models.CharField(max_length=25)
    address_type = models.CharField(max_length=255, choices=ADDRESS_TYPE_CHOICE,blank=True, null=True)
    is_default = models.BooleanField(default=False)
    class Meta:
        db_table = 'customers_address'
        verbose_name = 'address'
        verbose_name_plural = 'addresses'
        ordering = ["-id"]

    def __str__(self):

        return self.address1


class CartTotal(CommonModel):
    item_total = models.FloatField(default=0)
    total = models.FloatField(default=0)
    offer = models.FloatField(default=0)
    delivery = models.FloatField(default=0)
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE)


    class Meta:
        db_table = 'Customer_CartTotal'
        verbose_name = 'carttotal'
        verbose_name_plural = 'carttotals'
        ordering = ['-id']


    def __str__(self):
        return self.customer.user.email
    


class OrderItem(CommonModel):
    customer = models.ForeignKey(Customer, on_delete=models.SET_NULL, null=True, blank=True)
    seller = models.ForeignKey(Seller, on_delete=models.CASCADE, related_name="order_items", null=True, blank=True)
    quantity = models.PositiveIntegerField(default=1)
    variant = models.ForeignKey('items.ProductVariant', on_delete=models.SET_NULL, null=True, blank=True)
    amount = models.FloatField(default=0)
    class Meta:
        db_table = 'customers_order_item'
        verbose_name = 'order item'
        verbose_name_plural = 'order items'
        ordering = ["-id"]

    def __str__(self):
        return self.variant.name if self.variant else "Deleted Variant"
    

class Order(CommonModel):
    customer = models.ForeignKey(Customer, on_delete=models.SET_NULL, null=True, blank=True)
    sellers = models.ManyToManyField(Seller, related_name="orders", null=True, blank=True)
    address = models.ForeignKey(Address, on_delete=models.SET_NULL, null=True, blank=True)
    order_id = models.CharField(max_length=35)
    items = models.ManyToManyField(OrderItem)
    sub_total = models.FloatField(default=0)
    delivery_charge = models.FloatField(default=0)
    offer = models.FloatField(default=0)
    total = models.FloatField(default=0)
    first_name = models.CharField(max_length=255)
    last_name = models.CharField(max_length=255)
    email = models.CharField(max_length=255)
    phone_number = models.BigIntegerField()
    order_status = models.CharField(max_length=25, choices=ORDER_STATUS ,default='IN')
    address1 = models.CharField(max_length=100,blank=True, null=True)
    address2 = models.CharField(max_length=100,blank=True, null=True)
    city = models.CharField(max_length=25,blank=True, null=True)
    state = models.CharField(max_length=25,blank=True, null=True)
    pincode = models.CharField(max_length=25,blank=True, null=True)
    address_type = models.CharField(max_length=255, choices=ADDRESS_TYPE_CHOICE,blank=True, null=True)
    payment_status = models.CharField(max_length=2, choices=ORDER_STATUS, default='PA')
    payment = models.ForeignKey('main.Payment', on_delete=models.SET_NULL, null=True, blank=True)
    payment_method = models.CharField(max_length=10, choices=PAYMENT_METHOD_CHOICES, default='ONLINE')


    class Meta:
        db_table = 'customers_order'
        verbose_name = 'order'
        verbose_name_plural = 'orders'
        ordering = ["-id"]

    def __str__(self):
        return f"Order {self.order_id} - {self.customer.user.username}"
    



class Review(CommonModel):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name="reviews")
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    rating = models.PositiveSmallIntegerField()  # 1 to 5 stars
    comment = models.TextField()


    class Meta:
        db_table = 'customers_review'
        verbose_name = 'review'
        verbose_name_plural = 'reviews'
        ordering = ["-id"]

    def __str__(self):
        return f"Review for {self.product.name} by {self.user.username}"