import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import Header2 from "../includes/Header2";
import Footer from "../includes/Footer";
import emptycart from "../../assets/images/empty_cart.png";
import LoadingSpinner from "../includes/LoadingSpinner";
import { BASE_URL } from "../../utils/config";
import useCartStore from "../../store/cartStore";

export default function Cart() {
    const [outOfStockItems, setOutOfStockItems] = useState([]);
    const [couponCode, setCouponCode] = useState("");
    const [couponError, setCouponError] = useState("");
    const [bestOffer, setBestOffer] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState("");
    const navigate = useNavigate();
    
    // Get cart data and actions from store
    const { 
        items: cartItems, 
        subtotal, 
        total, 
        discount, 
        delivery: deliveryCharge, 
        increaseQuantity, 
        decreaseQuantity, 
        removeItem, 
        clearCart, 
        setInitialCart 
    } = useCartStore();

    useEffect(() => {
        const fetchCartData = async () => {
            setLoading(true);
            try {
                const token = localStorage.getItem("access_token");
                const response = await fetch(`${BASE_URL}/cart/`, {
                    headers: { "Authorization": `Bearer ${token}` }
                });
                if (response.status === 401) {
                    navigate('/login');
                    return;
                }
                if (!response.ok) throw new Error("Failed to load cart data.");
                const data = await response.json();
                setInitialCart(data);
                setOutOfStockItems(data.out_of_stock_items || []);
                setBestOffer(data.best_offer || null);
            } catch (err) {
                setError(err.message);
            } finally {
                setLoading(false);
            }
        };
        fetchCartData();
    }, [navigate, setInitialCart]);

    const updateQuantity = async (id, action) => {
        // Reset coupon display
        setBestOffer(null);
        setCouponError("");
        
        try {
            const token = localStorage.getItem("access_token");
            const response = await fetch(`${BASE_URL}/update/${id}/`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${token}`,
                },
                body: JSON.stringify({ action }),
            });

            const data = await response.json();
            if (!response.ok) {
                throw new Error(data.error || "Failed to update quantity");
            }
            // This is the correct way: update state from the server's response
            setInitialCart(data);
        } catch (err) {
            console.error("Error updating cart quantity:", err.message);
        }
    };


    const removeFromCart = async (id) => {
        // Reset coupon display
        setBestOffer(null);
        setCouponError("");
        
        try {
            const token = localStorage.getItem("access_token");
            const itemToRemove = cartItems.find(item => item.id === id);
            
            if (!itemToRemove) return;

            const response = await fetch(`${BASE_URL}/remove/${id}/`, {
                method: "DELETE",
                headers: {
                    "Authorization": `Bearer ${token}`,
                },
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || "Failed to remove item");
            }

            // Update entire cart state from server response
            const data = await response.json();
            setInitialCart(data);
            setOutOfStockItems(data.out_of_stock_items || []);
            setBestOffer(data.best_offer || null);
        } catch (err) {
            console.error("Error removing item from cart:", err.message);
        }
    };

    const applyCoupon = async (e) => {
        e.preventDefault();
        if (!couponCode.trim()) return;

        try {
            const token = localStorage.getItem("access_token");
            const response = await fetch(`${BASE_URL}/cart/`, {
                method: "POST",
                headers: {
                    Authorization: `Bearer ${token}`,
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ code: couponCode }),
            });

            const data = await response.json();
            
            if (!response.ok) {
                // Handle error case
                setCouponError(data.error || data.message || "Invalid coupon code");
                return;
            }

            // Update cart with the full cart data from the response
            setInitialCart(data);
            
            // Update best offer from the applied coupon details
            if (data.applied_coupon) {
                setBestOffer({
                    code: data.applied_coupon.code,
                    discount_value: data.applied_coupon.discount_value
                });
            } else {
                // Fallback to using discount amount if applied_coupon is not available
                setBestOffer(data.discount_amount > 0 ? { 
                    code: couponCode, 
                    discount_value: data.discount_amount 
                } : null);
            }
            
            // Clear the coupon input and any previous errors
            setCouponError("");
            setCouponCode("");
        } catch (err) {
            setCouponError(err.message || "An error occurred while applying the coupon");
            console.error("Coupon application error:", err);
        }
    };

    if (loading) {
        return (
            <div>
                <LoadingSpinner />
            </div>
        );
    }

    return (
        <div>
            <Header2 />
            <div className="container mx-auto mt-[70px] p-6">
                {cartItems.length > 0 ? (
                    <>
                        <p className="text-[18px] font-semibold mb-7 flex items-center">
                            <button onClick={() => navigate(`/`)} className="hover:text-purple-500 font-semibold text-[18px] flex items-center">
                                <i className="text-[20px] md:text-[20px] mr-2 font-bold bx bx-arrow-back"></i>
                            </button>
                            My Cart
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                            <div className="col-span-1 md:col-span-3">
                                {cartItems.map((item) => (
                                    <div key={item.id} className="p-5 border rounded-lg mb-4 shadow-sm">
                                        <div className="flex gap-4 items-start">
                                        <img
                                            src={item.option?.images?.length > 0
                                                ? `${BASE_URL}${item.option.images[0]}`
                                                : item.product?.mainImage
                                                ? `${BASE_URL}${item.product.mainImage}`
                                                : "/fallback-image.jpg"}
                                            alt={item.product?.name || "Product Image"}
                                            className="w-24 h-24 object-contain rounded-lg"
                                        />
                                            <div className="flex-1">
                                                <h3 className="text-lg font-semibold product-name-truncate"
                                                    title={item.product.name}>{item.product.name}</h3>
                                                {item.option && (
                                                    <p className="text-sm text-gray-600 mt-1">
                                                        Variant:
                                                        {item.option.storage && ` ${item.option.storage}, `}
                                                        {item.option.color?.name && `${item.option.color.name} `}
                                                    </p>
                                                )}
                                                <p className="text-lg font-bold mt-2">{(parseFloat(item.price) || 0).toFixed(2)} AED</p>
                                                <div className="flex items-center mt-3">
                                                    <button
                                                         className="px-3 py-1 border cursor-pointer rounded hover:bg-gray-100"
                                                         onClick={() => updateQuantity(item.id, 'minus')}
                                                     >
                                                         -
                                                     </button>

                                                    <span className="mx-4 text-lg">{item.quantity}</span>
                                                    <button
                                                         className="px-3 py-1 border cursor-pointer rounded bg-blue-500 text-white hover:bg-blue-600"
                                                         onClick={() => updateQuantity(item.id, 'plus')}
                                                     >
                                                         +
                                                     </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                            


                            <div className="col-span-1 p-6 border rounded-lg shadow-sm bg-gray-50">
                                <h3 className="text-xl font-semibold mb-4">Order Summary</h3>

                                <form onSubmit={applyCoupon} className="mb-6">
                                    <div className="gap-2">
                                        <input
                                            type="text"
                                            placeholder="Enter coupon code"
                                            className="flex-1 mb-1 border w-full rounded-lg px-5 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            value={couponCode}
                                            onChange={(e) => {
                                                setCouponCode(e.target.value);
                                                setCouponError("");
                                            }}
                                        />
                                        <button
                                            type="submit"
                                            className="py-2 w-full cursor-pointer m-auto bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                                        >
                                            Apply
                                        </button>
                                    </div>
                                    {couponError && (
                                        <p className="text-red-500 text-sm mt-2">{couponError}</p>
                                    )}
                                    {bestOffer && (
                                        <div className="mt-2 text-green-600 text-sm">
                                            Applied coupon: {bestOffer.code} (-{bestOffer.discount_value} AED)
                                        </div>
                                    )}
                                </form>

                                <div className="space-y-3">
                                    <div className="flex justify-between">
                                        <span>Subtotal:</span>
                                        <span>{subtotal.toFixed(2)} AED</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>Discount:</span>
                                        <span className="text-red-500">-{(parseFloat(discount) || 0).toFixed(2)} AED</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>Delivery:</span>
                                        <span>{(parseFloat(deliveryCharge) || 0).toFixed(2)} AED</span>
                                    </div>
                                    <div className="pt-3 border-t">
                                        <div className="flex justify-between font-bold">
                                            <span>Total:</span>
                                            <span>{(parseFloat(total) || 0).toFixed(2)} AED</span>
                                        </div>
                                    </div>
                                </div>

                                {outOfStockItems.length > 0 ? (
                                    <div className="mt-4 p-3 bg-red-50 rounded-lg">
                                        <p className="text-red-500 text-sm">
                                            Remove out-of-stock items to proceed
                                        </p>
                                    </div>
                                ) : (
                                    <button
                                        className="w-full mt-6 cursor-pointer py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                                        onClick={() => navigate("/checkout")}
                                    >
                                        Proceed to Checkout
                                    </button>

                                )}
                            </div>
                        </div>
                    </>
                ) : (
                    <div className="text-center mt-20">
                        <img src={emptycart} alt="Empty Cart" className="w-40 mx-auto mb-6" />
                        <h2 className="text-2xl font-semibold mb-2">Your Cart is Empty</h2>
                        <p className="text-gray-600 mb-6">Looks like you haven't added anything to your cart yet.</p>
                        <button
                            onClick={() => navigate("/")}
                            className="px-8 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                        >
                            Continue Shopping
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
}
