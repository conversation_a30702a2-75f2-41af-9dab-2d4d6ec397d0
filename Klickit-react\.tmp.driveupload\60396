import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import PaymentForm from './PaymentForm';
import LoadingSpinner from '../includes/LoadingSpinner';
import { useStripe, useElements, PaymentRequestButtonElement } from '@stripe/react-stripe-js';

// Configure axios
const api = axios.create({
    baseURL: import.meta.env.VITE_API_URL,
    headers: {
        'Content-Type': 'application/json',
    },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

const PaymentCheckout = ({ orderData, onBack }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [paymentRequest, setPaymentRequest] = useState(null);
  const navigate = useNavigate();
  
  const stripe = useStripe();
  const elements = useElements();

  // Using useCallback to memoize functions referenced in dependency arrays
  const handlePaymentSuccess = useCallback(async (paymentIntent) => {
    try {
      const response = await api.post('/payment/success/', {
        payment_intent_id: paymentIntent.id,
        order_id: orderData.order_id
      });

      if (response.data.success) {
        navigate(`/order-success/${response.data.order_id}`);
      } else {
        setError('Failed to process order');
      }
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to process order');
    }
  }, [api, orderData, navigate, setError]);

  const handlePaymentError = useCallback((error) => {
    setError(error.message || 'Payment failed. Please try again.');
  }, [setError]);

  useEffect(() => {
    // The component has mounted and data is expected to be passed via props,
    // so we can set loading to false immediately.
    setLoading(false);
  }, []); // The empty dependency array ensures this effect runs only once after the initial render.
  
  useEffect(() => {
    if (stripe && orderData && orderData.total && orderData.order_id && orderData.client_secret) {
      const pr = stripe.paymentRequest({
        country: 'AE', // United Arab Emirates
        currency: 'aed',
        total: {
          label: `Total for Order ${orderData.order_id}`,
          amount: Math.round(parseFloat(orderData.total) * 100), // Amount in cents
        },
        requestPayerName: true,
        requestPayerEmail: true,
      });

      pr.canMakePayment().then(result => {
        if (result && result.googlePay) { // Check specifically for Google Pay
          setPaymentRequest(pr);
          console.log("Google Pay is available", result);
        } else if (result) {
          // Optionally handle other browser payment methods
          console.log("Google Pay not available, but other browser payment might be:", result);
        } else {
          console.log("No browser payment method available.");
        }
      });

      pr.on('paymentmethod', async (ev) => {
        console.log("PaymentRequest 'paymentmethod' event triggered", ev);
        try {
          const { paymentIntent, error: confirmError } = await stripe.confirmCardPayment(
            orderData.client_secret,
            {
              payment_method: ev.paymentMethod.id,
            },
            {
              handleActions: false, // Important for Payment Request API
            }
          );

          if (confirmError) {
            console.error('Google Pay confirmation error:', confirmError);
            ev.complete('fail');
            handlePaymentError(confirmError); // Use existing error handler
            return;
          }

          console.log("Google Pay paymentIntent status:", paymentIntent.status);
          if (paymentIntent.status === 'succeeded' || paymentIntent.status === 'requires_capture') {
            ev.complete('success');
            handlePaymentSuccess(paymentIntent); // Use existing success handler
          } else if (paymentIntent.status === 'requires_payment_method' || paymentIntent.status === 'requires_confirmation') {
            ev.complete('fail');
            setError('Payment failed with Google Pay. Please try another payment method or card.');
          } else {
            ev.complete('fail');
            setError(`Payment with Google Pay was not successful: ${paymentIntent.status}. Please try again.`);
          }
        } catch (error) {
          console.error('Exception during Google Pay confirmation:', error);
          ev.complete('fail');
          handlePaymentError(error);
        }
      });
    }
  }, [stripe, orderData, handlePaymentSuccess, handlePaymentError]);  // Include dependencies

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <p className="text-red-600">{error}</p>
        <button
          onClick={onBack}
          className="mt-4 text-sm text-red-600 hover:text-red-700"
        >
          Go back to checkout
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <h2 className="text-2xl font-semibold mb-6">Payment Details</h2>
          
          {paymentRequest && (
            <div className="mb-6">
              <div className="mb-4">
                <p className="text-base font-medium mb-3">Pay with Google Pay</p>
                <PaymentRequestButtonElement options={{ paymentRequest }} />
              </div>
              <div className="flex items-center my-4">
                <div className="flex-grow border-t border-gray-300"></div>
                <span className="mx-4 text-gray-500 text-sm font-medium">OR</span>
                <div className="flex-grow border-t border-gray-300"></div>
              </div>
            </div>
          )}
          
          <PaymentForm
            clientSecret={orderData.client_secret}
            amount={orderData.total}
            onSuccess={handlePaymentSuccess}
            onError={handlePaymentError}
          />
        </div>
        <div>
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-2xl font-semibold mb-6">Order Summary</h2>
            <div className="space-y-4">
              <div className="border-b pb-4">
                <h3 className="font-medium mb-2">Shipping Address</h3>
                <p className="text-gray-600">
                  {orderData.address?.address1 || 'No address provided'}
                  {orderData.address?.address2 && <>, {orderData.address.address2}</>}
                </p>
                <p className="text-gray-600">
                  {orderData.address?.city || ''}
                  {orderData.address?.state && `, ${orderData.address.state}`}
                  {orderData.address?.pincode && `, ${orderData.address.pincode}`}
                </p>
                {orderData.address?.landmark && (
                  <p className="text-gray-600">
                    Landmark: {orderData.address.landmark}
                  </p>
                )}
              </div>

              <div className="border-b pb-4">
                <h3 className="font-medium mb-2">Contact Information</h3>
                <p className="text-gray-600">
                  {orderData.first_name} {orderData.last_name}
                </p>
                <p className="text-gray-600">
                  {orderData.email || 'No email provided'}
                </p>
                <p className="text-gray-600">
                  {orderData.phone_number || 'No phone number provided'}
                </p>
              </div>

              <div className="border-b pb-4">
                <h3 className="font-medium mb-2">Order Items</h3>
                <div className="space-y-2">
                  {orderData.cart_items.map((item) => (
                    <div key={item.id} className="flex justify-between text-sm">
                      <div>
                        <p className="font-medium">{item.product.name}</p>
                        {item.variant?.name && (
                          <p className="text-gray-500 text-xs">Variant: {item.variant.name}</p>
                        )}
                        <p className="text-gray-500">Qty: {item.quantity}</p>
                      </div>
                      <div className="text-right">
                        <p>AED {(parseFloat(item.price) * item.quantity).toFixed(2)}</p>
                        <p className="text-xs text-gray-500">AED {parseFloat(item.price).toFixed(2)} each</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                {orderData.discount_amount && (
                  <div className="flex justify-between">
                    <span>Discount</span>
                    <span>AED {(parseFloat(orderData.discount_amount) || 0).toFixed(2)}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span>Subtotal</span>
                  <span>AED {(parseFloat(orderData.subtotal) || 0).toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Delivery</span>
                  <span>AED {(parseFloat(orderData.delivery_charge || orderData.delivery) || 0).toFixed(2)}</span>
                </div>
                <div className="flex justify-between font-semibold text-lg pt-2 border-t">
                  <span>Total</span>
                  <span>AED {(parseFloat(orderData.total_amount_to_pay || orderData.total) || 0).toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentCheckout; 