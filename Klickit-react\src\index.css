@import "tailwindcss";


body{
  font-family: "Poppins", sans-serif;
}

.wrapper{
  width: 90%;
  margin: 0 auto;
}

.product-bg {
  border: 1px solid #FFF;
  background: rgba(255, 255, 255, 0.20);
  box-shadow: 20px 10px 50px 0px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(15px);
}

.rotate-negative {
  transform: rotate(-90deg);
}

.smooth-scroll::-webkit-scrollbar {
  display: none;
}

.smooth-scroll::-webkit-scrollbar-thumb {
  display: none;
}

.smooth-scroll::-webkit-scrollbar-track {
  display: none;
}







.rotate-180 {
  transform: rotate(180deg);
  transition: transform 0.3s ease; /* Smooth rotation */
}

/* Product name truncation styles */
.product-name-truncate {
  max-width: 100%;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Review text truncation styles */
.review-text-truncate {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}

.review-text-expanded {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: unset;
  overflow: hidden;
}

/* Details text truncation styles */
.details-text-truncate {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 5;
  overflow: hidden;
}

.details-text-expanded {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: unset;
  overflow: hidden;
}

/* Brand slider styles */
.brand-slider-container {
  padding: 10px 0;
}

.brand-slider .slick-track {
  display: flex;
  align-items: center;
}

.brand-item {
  transition: all 0.3s ease;
}

.brand-item:hover .brand-image-container {
  transform: translateY(-3px);
}

.brand-image-container {
  transition: all 0.3s ease;
  width: 70px;
  height: 70px;
}

@media (max-width: 768px) {
  .brand-image-container {
    width: 55px;
    height: 55px;
  }
}

@media (max-width: 480px) {
  .brand-image-container {
    width: 45px;
    height: 45px;
    padding: 4px !important;
  }

  .brand-item .text-xs {
    font-size: 0.65rem;
  }

  .brand-slider-container {
    padding: 5px 0;
  }

  .brand-item {
    padding-left: 2px !important;
    padding-right: 2px !important;
  }
}


.smooth-scroll::-webkit-scrollbar {
  width: 2px;
  border-radius: 2px; /* Set the width of the scrollbar */
}

/* Customize the scrollbar thumb (the draggable part) */
.smooth-scroll::-webkit-scrollbar-thumb {
  background-color: #5D5D5D; /* Set the color of the thumb */
  border-radius: 24px;
}

/* Customize the scrollbar track (the non-draggable part) */
.smooth-scroll::-webkit-scrollbar-track {
  background-color: #D9D9D9; /* Set the color of the track */
  border-radius: 24px;
}



@keyframes fadeIn {
  from {
      opacity: 0;
      transform: translateY(-20px); /* Starts from the left */
    }
    to {
      opacity: 1;
      transform: translateY(0); /* Ends in the correct position */
  }
}

@keyframes fadeOut {
  0% { opacity: 1; }
  100% { opacity: 0; }
}

.fade-in {
  animation: fadeIn 0.7s ease forwards; /* Fade in over 0.5 seconds */
}

.fade-out {
  animation: fadeOut 0.7s ease forwards; /* Fade out over 0.5 seconds */
}


.overflow-hidden {
  overflow: hidden;
}
#thumbnail-container .thumbnail-item {
  width: 60px;
  height: 66px;
}

#thumbnail-container .thumbnail {
  width: 100%;
  height: 100%;
}
.swip-container::-webkit-scrollbar {
  width: 2px;
  border-radius: 2px; /* Set the width of the scrollbar */
}

/* Customize the scrollbar thumb (the draggable part) */
.swip-container::-webkit-scrollbar-thumb {
  background-color: #5d5d5d00; /* Set the color of the thumb */
  border-radius: 24px;
}

/* Customize the scrollbar track (the non-draggable part) */
.swip-container::-webkit-scrollbar-track {
  background-color: #d9d9d900; /* Set the color of the track */
  border-radius: 24px;
}

.color-swatch {
  transition: all 0.2s ease;
  min-width: 32px;
  min-height: 32px;
}

.color-swatch.selected {
  box-shadow: 0 0 0 2px white, 0 0 0 4px black;
}

.option-button {
  transition: all 0.2s ease;
}

.option-button.selected {
  background-color: #f3f4f6;
}

.hkefiqehf {
  transition: transform 0.3s ease-in-out;
}
.hdqjwdhqwo {
  transform: translateY(-100%);
}



.responsive-image {
  width: 100%;
  height: 200px;
  object-fit: contain;
  border-radius: 8px 8px 0 0;
}


.loalo {
  width: 200px; /* Ensure all cards are the same width */
  height: 300px; /* Prevent uneven heights */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden; /* Prevent extra scrolling */
}


/* Mobile adjustments */
@media (max-width: 765px) {
  .responsive-image {
    height: 200px; /* Adjust height for smaller screens */
    object-fit: contain; /* Ensure full image is visible */
  }
  .loalo {
    height: auto; /* Allow height to adjust naturally */
  }
}



.product-image-container {
  width: 100%;
  max-width: 420px; /* Limit width */
  height: 300px; /* Ensure consistent height */
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5; /* Light background for better visibility */
  border-radius: 8px; /* Soft rounded corners */
  overflow: hidden;
  position: relative;
}

.product-image-container img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain; /* Prevent stretching */
}

.custom-box {
  max-width: 100%;
  max-height: 100%;
  border-radius: 1rem; /* rounded-2xl */
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
              0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Override at lg breakpoint */
@media (min-width: 1024px) {
  .custom-box {
    max-width: none;   /* override max-w-fit */
    width: 100%;       /* lg:w-full */
  }
}

