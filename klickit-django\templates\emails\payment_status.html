<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Status</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            width: 80%;
            margin: 20px auto;
            padding: 20px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .header {
            background-color: #007bff; /* Header color - you can change this */
            color: #ffffff;
            padding: 10px;
            text-align: center;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
        }
        .content {
            padding: 20px;
        }
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 0.9em;
            color: #777;
        }
        .status-succeeded {
            color: green;
            font-weight: bold;
        }
        .status-failed {
            color: red;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>NeuMoon - Payment Notification</h2> </div>
        <div class="content">
            <p>Dear {{ payment.customer.first_name|default:"Customer" }},</p>

            {% if status == "succeeded" %}
                <p>Your payment for order <strong class="status-succeeded">was successful</strong>.</p>
                <p>Your payment has been completed successfully. Your order will be processed shortly.</p>
            {% elif status == "failed" %}
                <p>The payment for your order <strong class="status-failed">has failed</strong>.</p>
                <p>Please try the payment again or contact us for further assistance.</p>
            {% else %}
                <p>The status of your payment is: <span style="font-weight: bold;">{{ status|capfirst }}</span>.</p>
            {% endif %}

            <h3>Payment Details:</h3>
            <table>
                <tr>
                    <th>Order ID</th>
                    <td>{{ order_id }}</td>
                </tr>
                <tr>
                    <th>Payment Intent ID</th>
                    <td>{{ payment.payment_intent_id }}</td>
                </tr>
                <tr>
                    <th>Order Amount (VAT Inclusive)</th>
                    <td>AED {{ payment.total_amount|floatformat:2 }}</td>
                </tr>
                <tr>
                    <th>Date</th>
                    <td>{{ payment.created_datetime|date:"d M Y, H:i A" }}</td>
                </tr>
                <tr>
                    <th>Current Status</th>
                    {% if status == "succeeded" %}
                        <td class="status-succeeded">{{ status|capfirst }}</td>
                    {% elif status == "failed" %}
                         <td class="status-failed">{{ status|capfirst }}</td>
                    {% else %}
                        <td>{{ status|capfirst }}</td>
                    {% endif %}
                </tr>
            </table>

            <p>If you require further information, please contact us.</p>
            <p>Thank you,<br>The NeuMoon Team</p> </div>
        <div class="footer">
            <p>&copy; {% now "Y" %} NeuMoon. All rights reserved.</p>
        </div>
    </div>
</body>
</html>