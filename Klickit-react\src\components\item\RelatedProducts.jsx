import React, { useRef } from "react";
import { useNavigate } from "react-router-dom";
import '../../index.css';

const RelatedProducts = ({ relatedProducts = [] }) => {
  const sliderRef = useRef(null);
  const navigate = useNavigate();

  const scrollSlider = (direction) => {
    if (sliderRef.current) {
      const scrollAmount = direction === "left" ? -200 : 200;
      sliderRef.current.scrollBy({ left: scrollAmount, behavior: "smooth" });
    }
  };


  const placeholderImage = "/static/images/product-placeholder.jpg";

  return (
    <section className="lg:px-[60px] sm:px-[40px] px-[16px] my-[28px]">
      <h3 className="mb-1 md:mb-3 text-[18px] md:text-[24px] font-semibold text-gray-700">
        Related Products
      </h3>
      <div className="relative flex items-center">
        {/* Left Slide Button */}
        <button
          onClick={() => scrollSlider("left")}

          className="absolute left-[-10px] z-10 bg-gray-200 p-1 md:p-2 rounded-full shadow-lg hover:bg-gray-300 transition-colors focus:outline-none"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 md:h-6 w-4 md:w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>

        {/* Scrollable Product Cards - snap ക്ലാസ്സുകൾ ചേർത്തു */}
        <div
          ref={sliderRef}
          id="related-slider" //
          className="flex gap-5 overflow-x-auto scrollbar-hide pb-2 snap-x snap-mandatory"
        >
          {relatedProducts && relatedProducts.length > 0 ? (
            relatedProducts.map((product) => (
              <div
                key={product.id}

                className="min-w-[150px] md:min-w-[200px] loalo px-[2px] max-w-[150px] md:max-w-[200px] shadow-lg rounded-xl relative snap-center bg-white"
              >
                {/* Product Image - responsive-image ക്ലാസ്സ് ഉപയോഗിക്കുന്നു */}
                <a
                  // onClick={() => navigate(`/product/${product.id}`)}
                  href={`/product/${product.id}`}
                  className="cursor-pointer block"
                >
                  <img

                    src={product.mainimage_url || placeholderImage}
                    alt={product.name}

                    className="responsive-image"
                    onError={(e) => { e.target.src = placeholderImage; }}
                  />
                </a>

                {/* Product Details - NewArrival-ലെ ഘടന ഉപയോഗിക്കുന്നു */}
                <div className="p-3">
                  <h3 className="text-[15px] font-normal mb-1 product-name-truncate">
                    <a
                      // onClick={() => navigate(`/product/${product.id}`)}
                      href={`/product/${product.id}`}
                      className="cursor-pointer hover:text-blue-600"
                      title={product.name}
                    >
                      {product.name}
                    </a>
                  </h3>
                  <div className="flex items-center gap-2">
                    {/* regular_price ഉണ്ടെങ്കിൽ മാത്രം sale_price-നോടൊപ്പം കാണിക്കുന്നു */}
                    {product.regular_price && product.sale_price && (
                      <span className="text-[10px] line-through text-gray-500">
                        {product.regular_price} AED
                      </span>
                    )}
                    <span className="text-[14px] font-semibold">
                      {/* sale_price ഇല്ലെങ്കിൽ regular_price കാണിക്കാം */}
                      {product.sale_price || product.regular_price} AED
                    </span>
                  </div>
                </div>
              </div>
            ))
          ) : (
             <p className="text-gray-500 text-sm p-4">No related products found.</p>
          )}
        </div>

        {/* Right Slide Button */}
        <button
          onClick={() => scrollSlider("right")}

           className="absolute right-[-10px] z-10 bg-gray-200 p-1 md:p-2 rounded-full shadow-lg hover:bg-gray-300 transition-colors focus:outline-none"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 md:h-6 w-4 md:w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </section>
  );
};

export default RelatedProducts;