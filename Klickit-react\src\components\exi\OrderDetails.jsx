import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import Header2 from "../includes/Header2";
import LoadingSpinner from "../includes/LoadingSpinner";
import { BASE_URL } from "../../utils/config";

export default function OrderDetails() {
    const { id } = useParams();
    const navigate = useNavigate();
    const [order, setOrder] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState("");

    useEffect(() => {
        const fetchOrderDetails = async () => {
            try {
                const token = localStorage.getItem("access_token");
                const response = await fetch(`${BASE_URL}/order/${id}/`, {
                    headers: {
                        "Authorization": `Bearer ${token}`,
                        "Content-Type": "application/json",
                    },
                });

                if (!response.ok) {
                    throw new Error("Failed to fetch order");
                }

                const data = await response.json();
                if (data.title) {
                    document.title = data.title;
                }
                setOrder(data);
                setLoading(false);
            } catch (err) {
                console.error("Error fetching order:", err.message);
                setError("Failed to load order details. Please try again.");
                setLoading(false);
            }
        };

        fetchOrderDetails();
    }, [id]);

    const statusMapping = {
        IN: "Initiated",
        PL: "Placed",
        IP: "In Progress",
        DI: "Dispatched",
        CO: "Completed",
        CA: "Cancelled",
    };

    const getStatusFullForm = (orderStatus) => statusMapping[orderStatus] || "Unknown Status";

    if (loading) return <LoadingSpinner />;

    return (
        <div>
            <Header2 />
            <section className="py-10 md:py-14 mt-[60px]">
                <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                    {/* Back Button */}
                    <div className="mb-6">
                        <button
                            onClick={() => window.history.back()}
                            className="text-gray-700 hover:text-purple-600 font-medium flex items-center"
                        >
                            <i className="bx bx-arrow-back text-2xl"></i>
                            <span className="ml-2 text-lg">Back</span>
                        </button>
                    </div>

                    {error ? (
                        <p className="text-center text-red-600">{error}</p>
                    ) : order ? (
                        <div className="bg-white shadow-lg rounded-lg p-6 border border-gray-300">
                            {/* Order Details */}
                            <h2 className="text-xl font-semibold border-b pb-2 mb-4">Order #{order.order_id}</h2>

                            <div className="text-gray-700 space-y-2">
                                <p><strong>Status:</strong> <span className="text-blue-700 font-semibold">{getStatusFullForm(order.order_status)}</span></p>
                                <p><strong>Customer:</strong> {order.first_name} {order.last_name}</p>
                                <p><strong>Email:</strong> {order.email}</p>
                                <p><strong>Phone:</strong> {order.phone_number}</p>
                                <p><strong>Address:</strong> {order.address1}, {order.city}, {order.state} - {order.pincode}</p>
                                <p><strong>Payment Method:</strong> {order.payment_method || "Not specified"}</p>
                            </div>

                            {/* Order Items */}
                            <h3 className="text-lg font-semibold mt-6 border-b pb-2">Ordered Items</h3>
                            <div className="overflow-x-auto">
                                <table className="w-full mt-3 border border-gray-200 rounded-lg">
                                    <thead className="bg-gray-100">
                                        <tr className="text-left">
                                            <th className="p-3 border-b">Product</th>
                                            <th className="p-3 border-b">Variant</th>
                                            <th className="p-3 border-b">Quantity</th>
                                            <th className="p-3 border-b">Amount</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {order.items?.map((item, index) => (
                                            <tr key={index} className="border-b last:border-b-0">
                                                <td className="p-3 font-semibold">{item.product.name}</td>
                                                <td className="p-3">
                                                    {item.variant?.name || 'Standard'}
                                                    {item.variant?.color?.name && ` (${item.variant.color.name})`}
                                                </td>
                                                <td className="p-3">{item.quantity}</td>
                                                <td className="p-3">{item.amount.toFixed(2)} AED</td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>

                            {/* Order Summary */}
                            <h3 className="text-lg font-semibold mt-6 border-b pb-2">Order Summary</h3>
                            <div className="space-y-3 mt-3">
                                <div className="flex justify-between">
                                    <span>Subtotal:</span>
                                    <span>{order.sub_total?.toFixed(2) || 0} AED</span>
                                </div>
                                <div className="flex justify-between">
                                    <span>Discount:</span>
                                    <span className="text-red-500">-{order.offer?.toFixed(2) || 0} AED</span>
                                </div>
                                <div className="flex justify-between">
                                    <span>Delivery Charge:</span>
                                    <span>{order.delivery_charge?.toFixed(2) || 0} AED</span>
                                </div>
                                <div className="flex justify-between font-bold border-t pt-2">
                                    <span>Total Amount:</span>
                                    <span>{order.total?.toFixed(2)} AED</span>
                                </div>
                            </div>

                            {/* Payment Information */}
                            <div className="mt-6">
                                <h3 className="text-lg font-semibold border-b pb-2">Payment Information</h3>
                                <div className="space-y-3 mt-3">
                                    {/* Payment Method */}
                                    <p>
                                        <strong>Payment Method:</strong>
                                        <span className="text-blue-700 font-semibold">
                                            {String(order.payment_method).toLowerCase() === 'cod'
                                                ? 'Cash on Delivery'
                                                : (order.payment_method === 'CARD' ? 'Card' : order.payment_method === 'GPAY' ? 'Google Pay' : 'Online Payment')
                                            }
                                        </span>
                                    </p>
                                    {/* Payment Status */}
                                    <p>
                                        <strong>Payment Status:</strong>
                                        <span className="text-blue-700 font-semibold">
                                            {String(order.payment_method).toLowerCase() === 'cod'
                                                ? 'To be Paid upon Delivery'
                                                : (order.payment_status_display || 'Payment Status Unknown')
                                            }
                                        </span>
                                    </p>
                                    {/* Order Date (always show) */}
                                    <p>
                                        <strong>Order Date:</strong>
                                        {order.created_datetime ? new Date(order.created_datetime).toLocaleString() : 'N/A'}
                                    </p>
                                    {/* Payment Date for online payments, if available */}
                                    {String(order.payment_method).toLowerCase() !== 'cod' && order.payment && (order.payment.paid_on || order.payment.created_datetime) && (
                                        <p>
                                            <strong>Payment Date:</strong>
                                            {new Date(order.payment.paid_on || order.payment.created_datetime).toLocaleString()}
                                        </p>
                                    )}
                                </div>
                                {/* Always show invoice button */}
                                <div className="mt-4">
                                    <button 
                                        className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition cursor-pointer"
                                        onClick={async () => {
                                            const token = localStorage.getItem("access_token");
                                            const orderId = order.order_id;
                                            const url = `${BASE_URL}/order/${orderId}/invoice/`;

                                            if (!token) {
                                                alert("You need to be logged in to download the invoice.");
                                                return;
                                            }

                                            try {
                                                const response = await fetch(url, {
                                                    method: 'GET',
                                                    headers: {
                                                        'Authorization': `Bearer ${token}`,
                                                    },
                                                });

                                                if (!response.ok) {
                                                    if (response.status === 401) {
                                                        alert("Authentication failed. Please log in again.");
                                                    } else {
                                                        alert(`Failed to download invoice: ${response.statusText}`);
                                                    }
                                                    return;
                                                }

                                                const blob = await response.blob();
                                                const objectUrl = window.URL.createObjectURL(blob);
                                                const link = document.createElement('a');
                                                link.href = objectUrl;
                                                link.download = `invoice_${orderId}.pdf`;

                                                document.body.appendChild(link);
                                                link.click();
                                                document.body.removeChild(link);
                                                window.URL.revokeObjectURL(objectUrl);
                                            } catch (error) {
                                                console.error("Error downloading invoice:", error);
                                                alert("An error occurred during the download.");
                                            }
                                        }}
                                    >
                                        <i className="bx bx-download mr-2"></i>
                                        Download Invoice
                                    </button>
                                </div>
                            </div>

                            {/* Actions */}
                            <div className="mt-6 flex justify-between items-center">
                                <button 
                                    className="bg-purple-600 text-white text-[12px] sm:text-[20px] px-2 sm:px-4 py-2 rounded-lg hover:bg-purple-700 transition"
                                    onClick={() => navigate(`/product/${order.items?.[0]?.product?.id}/reviews/`)}
                                >
                                    Add Review
                                </button>
                                <h3 className="text-[16px] sm:text-lg font-semibold">
                                    Grand Total: <span className="text-green-700 ml-1 sm:ml-2">{order.total?.toFixed(2)} AED</span>
                                </h3>
                            </div>
                        </div>
                    ) : (
                        <p className="text-center text-gray-600">Order not found.</p>
                    )}
                </div>
            </section>
        </div>
    );
}
