{% extends "base/admin-base.html" %}
{% block container %}
{% load static %}

{% include 'includes/seller-nav.html' %}

<div class="content-body">
    <div class="row page-titles mx-0">
        <div class="col p-md-0">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="javascript:void(0)">{{ sub_title }}</a></li>
                <li class="breadcrumb-item active"><a href="javascript:void(0)">{{ name }}</a></li>
            </ol>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <!-- Disclaimer Warning -->
                        <!-- <div class="alert alert-warning flex justify-between align-items-center" role="alert" style="font-weight: bold;">
                            ⚠️ Before adding a new product, please make sure specifications added <span class="ml-5"> click here to add specifications :  <a href="{% url 'managers:custom_specifications' %}">add</a></span>                          
                        </div> -->
                        <div class="heading-line">
                            <h4 class="card-title m-0 p-0">{{ name }}</h4>
                            <div>
                                <a href="{% url 'managers:seller_add_product_wizard_step1_details' %}" id="custom-btn">ADD NEW</a>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered zero-configuration">
                                <thead>
                                    <tr>
                                        <th>NAME</th>
                                        <th>MODEL</th>
                                        <th>BRAND</th>
                                        <th>PRICE</th>
                                        <th>CATEGORY</th>
                                        <th>STOCK</th>
                                        <th>EDIT</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for instance in instances %}
                                    <tr>
                                        <td>{{ instance.name }}</td>
                                        <td>{{ instance.product_model|default:"-" }}</td>
                                        <td>{{ instance.brand }}</td>
                                        <td>{{ instance.sale_price }}</td>
                                        <td>{{ instance.category }}</td>
                                        <td>{{ instance.stock }}</td>
                                        <td>
                                            <a href="{% url 'seller:products_edit' id=instance.id %}" id="custom-btn">EDIT</a>
                                        </td>x                                        
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
