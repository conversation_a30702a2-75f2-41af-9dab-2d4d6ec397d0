from rest_framework import serializers
from .models import *

# Helper Serializers ----------------------------------------------------------
class ColorSerializer(serializers.ModelSerializer):
    class Meta:
        model = Color
        fields = ['id', 'color', 'name']

class CustomSpecificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomSpecification
        fields = ['id', 'name', 'key', 'value', 'product']

class RamSerializer(serializers.ModelSerializer):
    class Meta:
        model = Ram
        fields = ['id', 'value']

class StorageSerializer(serializers.ModelSerializer):
    class Meta:
        model = Storage
        fields = ['id', 'value']

class IconImageSerializer(serializers.ModelSerializer):
    image = serializers.SerializerMethodField()

    class Meta:
        model = IconImage
        fields = ['id', 'image', 'name']

    def get_image(self, obj):
        request = self.context.get('request')
        return request.build_absolute_uri(obj.image.url) if obj.image else None

# Main Serializers ------------------------------------------------------------
class CategorySerializer(serializers.ModelSerializer):
    image = serializers.SerializerMethodField()
    descendant_ids = serializers.SerializerMethodField()  # Add this

    class Meta:
        model = Category
        fields = ['id', 'name', 'image', 'descendant_ids']  # Include descendant_ids

    def get_image(self, obj):
        if obj.image and hasattr(obj.image, 'url'):
            return obj.image.url  
        return None

    def get_descendant_ids(self, obj):
        """ Recursively fetch all descendant category IDs. """
        def get_descendants(category):
            descendants = []
            for sub in category.subcategories.all():  # Assuming a related_name='subcategories' in your model
                descendants.append(sub.id)
                descendants.extend(get_descendants(sub))
            return descendants

        return get_descendants(obj)



class BrandSerializer(serializers.ModelSerializer):
    image = serializers.SerializerMethodField()

    class Meta:
        model = Brand
        fields = ['id', 'name', 'image']

    def get_image(self, obj):
        if obj.image and hasattr(obj.image, 'url'):
            return obj.image.url  
        return None

class ProductImageSerializer(serializers.ModelSerializer):
    image = serializers.SerializerMethodField()
    variants = serializers.StringRelatedField(many=True, source='variant')

    class Meta:
        model = ProductImage
        fields = ['id', 'image', 'alt_text', 'variants']

    def get_image(self, obj):
        if obj.image and hasattr(obj.image, 'url'):
            return obj.image.url  
        return None

class OptionSerializer(serializers.ModelSerializer):
    color = ColorSerializer(read_only=True)
    ram = serializers.CharField(read_only=True)
    storage = serializers.CharField(read_only=True)

    class Meta:
        model = Option
        fields = [
            'id', 'name', 'product', 'color', 
            'ram', 'storage', 'regular_price',
            'sale_price', 'stock'
        ]

class ProductSerializer(serializers.ModelSerializer):
    mainImage = serializers.SerializerMethodField()
    category = serializers.SerializerMethodField()  # 👈 Custom handling

    def get_category(self, obj):
        if obj.category:
            return CategorySerializer(obj.category, context=self.context).data
        return None
    brand = BrandSerializer(read_only=True)
    # specifications field removed as per requirements
    images = ProductImageSerializer(many=True, read_only=True)
    options = OptionSerializer(many=True, read_only=True, source='option_set')

    class Meta:
        model = Product
        fields = [
            'id', 'sku', 'name', 'description', 'details', 'mainImage',
            'regular_price', 'sale_price', 'offer_percentage', 'video',
            'category', 'brand', 'rating', 'stock',
            'delivery_title', 'delivery_duration', 'garantee_title',
            'garantee_time', 'sales_count', 'images', 'options'
        ]

    def get_mainImage(self, obj):
        if obj.mainimage and hasattr(obj.mainimage, 'url'):
            return obj.mainimage.url  
        return None
class SpecSerializer(serializers.ModelSerializer):
    image = IconImageSerializer(read_only=True)

    class Meta:
        model = Spec
        fields = ['id', 'product', 'image', 'detail']

# Extended Serializers for Detailed Views -------------------------------------
class ProductDetailSerializer(ProductSerializer):
    variants = OptionSerializer(many=True, read_only=True, source='option_set')
    specs = serializers.SerializerMethodField()
    custom_specs = CustomSpecificationSerializer(many=True, read_only=True)
    
    class Meta(ProductSerializer.Meta):
        fields = ProductSerializer.Meta.fields + ['variants', 'specs', 'custom_specs']

    def get_specs(self, obj):
        request = self.context.get('request')  # Get the request object from context
        specs_qs = obj.spec_set.order_by('-id')[:6]
        return SpecSerializer(specs_qs, many=True, context={'request': request}).data  # Pass context properly
