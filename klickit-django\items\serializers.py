from rest_framework import serializers
from .models import *

# Helper Serializers ----------------------------------------------------------
class ColorSerializer(serializers.ModelSerializer):
    class Meta:
        model = Color
        fields = ['id', 'color', 'name']

class CustomSpecificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomSpecification
        fields = ['id', 'name', 'key', 'value', 'variant']

class AttributeSerializer(serializers.ModelSerializer):
    class Meta:
        model = Attribute
        fields = ['id', 'name']

class ProductAttributeValueSerializer(serializers.ModelSerializer):
    attribute = AttributeSerializer(read_only=True)
    attribute_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = ProductAttributeValue
        fields = ['id', 'attribute', 'attribute_id', 'value']

class IconImageSerializer(serializers.ModelSerializer):
    image = serializers.SerializerMethodField()

    class Meta:
        model = IconImage
        fields = ['id', 'image', 'name']

    def get_image(self, obj):
        request = self.context.get('request')
        return request.build_absolute_uri(obj.image.url) if obj.image else None

# Main Serializers ------------------------------------------------------------
class CategorySerializer(serializers.ModelSerializer):
    image = serializers.SerializerMethodField()
    descendant_ids = serializers.SerializerMethodField()

    class Meta:
        model = Category
        fields = ['id', 'name', 'image', 'descendant_ids']

    def get_image(self, obj):
        if obj.image and hasattr(obj.image, 'url'):
            return obj.image.url
        return None

    def get_descendant_ids(self, obj):
        """ Recursively fetch all descendant category IDs. """
        def get_descendants(category):
            descendants = []
            for sub in category.subcategories.all():
                descendants.append(sub.id)
                descendants.extend(get_descendants(sub))
            return descendants

        return get_descendants(obj)

class BrandSerializer(serializers.ModelSerializer):
    image = serializers.SerializerMethodField()

    class Meta:
        model = Brand
        fields = ['id', 'name', 'image']

    def get_image(self, obj):
        if obj.image and hasattr(obj.image, 'url'):
            return obj.image.url
        return None

class ProductImageSerializer(serializers.ModelSerializer):
    image = serializers.SerializerMethodField()

    class Meta:
        model = ProductImage
        fields = ['id', 'image', 'alt_text', 'variant']

    def get_image(self, obj):
        if obj.image and hasattr(obj.image, 'url'):
            return obj.image.url
        return None

class SpecSerializer(serializers.ModelSerializer):
    image = IconImageSerializer(read_only=True)

    class Meta:
        model = Spec
        fields = ['id', 'variant', 'image', 'detail']

class ProductVariantSerializer(serializers.ModelSerializer):
    color = ColorSerializer(read_only=True)
    attribute_values = ProductAttributeValueSerializer(many=True, read_only=True)
    attributes_data = serializers.ListField(write_only=True, required=False)
    images = ProductImageSerializer(many=True, read_only=True)
    custom_specs = CustomSpecificationSerializer(many=True, read_only=True)
    specs = SpecSerializer(many=True, read_only=True)
    mainimage = serializers.SerializerMethodField()

    class Meta:
        model = ProductVariant
        fields = [
            'id', 'name', 'color', 'attribute_values', 'attributes_data',
            'sku', 'product_model', 'description', 'details', 'mainimage', 'video',
            'regular_price', 'sale_price', 'stock', 'sales_count',
            'delivery_title', 'delivery_duration', 'garantee_title', 'garantee_time',
            'images', 'custom_specs', 'specs', 'product'
        ]

    def get_mainimage(self, obj):
        if obj.mainimage and hasattr(obj.mainimage, 'url'):
            return obj.mainimage.url
        return None

    def create(self, validated_data):
        attributes_data = validated_data.pop('attributes_data', [])
        variant = ProductVariant.objects.create(**validated_data)

        for attr_data in attributes_data:
            ProductAttributeValue.objects.create(
                variant=variant,
                attribute_id=attr_data['attribute'],
                value=attr_data['value']
            )

        return variant

    def update(self, instance, validated_data):
        attributes_data = validated_data.pop('attributes_data', [])

        # Update variant fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Update attributes
        if attributes_data:
            # Clear existing attribute values
            instance.attribute_values.all().delete()

            # Create new attribute values
            for attr_data in attributes_data:
                ProductAttributeValue.objects.create(
                    variant=instance,
                    attribute_id=attr_data['attribute'],
                    value=attr_data['value']
                )

        return instance

class ProductSerializer(serializers.ModelSerializer):
    category = serializers.SerializerMethodField()
    brand = BrandSerializer(read_only=True)
    variants = ProductVariantSerializer(many=True, read_only=True)

    def get_category(self, obj):
        if obj.category:
            return CategorySerializer(obj.category, context=self.context).data
        return None

    class Meta:
        model = Product
        fields = [
            'id', 'name', 'category', 'brand', 'variants'
        ]

# Extended Serializers for Detailed Views -------------------------------------
class ProductDetailSerializer(ProductSerializer):
    variants = ProductVariantSerializer(many=True, read_only=True)

    class Meta(ProductSerializer.Meta):
        fields = ProductSerializer.Meta.fields
