from django.urls import  path

from managers import views

app_name = 'seller'


urlpatterns = [

    # seller

    path("dashboard/", views.seller_index, name="index"),
    path('logout/', views.seller_logout, name='logout'),
    path('orders/', views.seller_orders, name="orders"),
    path('order/<int:id>/', views.seller_order_detail, name="order"),

    path('order/<int:id>/accept/', views.seller_order_accept, name='order_accept'),
    path('order/<int:id>/reject/', views.seller_order_reject, name='order_reject'),
    path('order/<int:id>/dispatched/', views.seller_order_dispatched, name='order_dispatched'),
    path('order/<int:id>/completed/', views.seller_order_completed, name='order_completed'),

    path('products/', views.seller_products, name="products"),
    path('products/add/', views.seller_products_add, name="products_add"),
    path('products/edit/<int:id>/', views.seller_products_edit, name="products_edit"),
    path('products/delete/<int:id>/', views.seller_products_delete, name="products_delete"),

    path('product-images/', views.seller_product_images, name='seller_product_images'),
    path('product-images/add/', views.seller_product_images_add, name='seller_product_images_add'),
    path('product-images/edit/<int:id>/', views.seller_product_images_edit, name='seller_product_images_edit'),
    path('product-images/delete/<int:id>/', views.seller_product_images_delete, name='seller_product_images_delete'),

    path("colors/", views.seller_colors, name="colors"),
    path("colors/add/", views.seller_colors_add, name="colors_add"),
    path("colors/edit/<int:id>/", views.seller_colors_edit, name="colors_edit"),
    path("colors/delete/<int:id>/", views.seller_colors_delete, name="colors_delete"),

    path('variants/', views.seller_variants, name='variants'),
    path('variants/add/', views.seller_variants_add, name='variants_add'),
    path('variants/edit/<int:id>/', views.seller_variants_edit, name='variants_edit'),
    path('variants/delete/<int:id>/', views.seller_variants_delete, name='variants_delete'),

        # Custom Specification
    path('custom-specifications/', views.seller_custom_specifications, name='custom_specifications'),
    path('custom-specifications/add/', views.seller_custom_specifications_add, name='custom_specifications_add'),
    path('custom-specifications/edit/<int:id>/', views.seller_custom_specifications_edit, name='custom_specifications_edit'),
    path('custom-specifications/delete/<int:id>/', views.seller_custom_specifications_delete, name='custom_specifications_delete'),

    # Spec
    path('specs/', views.seller_specs, name='specs'),
    path('specs/add/', views.seller_specs_add, name='specs_add'),
    path('specs/edit/<int:id>/', views.seller_specs_edit, name='specs_edit'),
    path('specs/delete/<int:id>/', views.seller_specs_delete, name='specs_delete'),

]