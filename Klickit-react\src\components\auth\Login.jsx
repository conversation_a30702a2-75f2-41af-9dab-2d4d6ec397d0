import { useState } from "react";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import logo from "../../assets/images/logooo.png";
import LoadingSpinner from "../includes/LoadingSpinner";
import { BASE_URL } from "../../utils/config";

const Login = ({ setIsLoggedIn }) => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      localStorage.removeItem("access_token");
      localStorage.removeItem("refresh_token");
      delete axios.defaults.headers.common["Authorization"];

      const response = await axios.post(
        `${BASE_URL}/login/`,
        {
          email: email.toLowerCase().trim(),
          password: password.trim(),
        },
        {
          headers: {
            Authorization: undefined,
          },
        }
      );

      localStorage.setItem("access_token", response.data.access_token);
      localStorage.setItem("refresh_token", response.data.refresh_token);
      axios.defaults.headers.common["Authorization"] = `Bearer ${response.data.access_token}`;

      setIsLoggedIn(true);
      navigate("/", { replace: true });
    } catch (error) {
      console.error("Login error:", error.response?.data);
      alert(error.response?.data?.error || "Login failed. Check credentials.");
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <LoadingSpinner />;

  return (
    <section className="min-h-screen flex justify-center items-center bg-gray-200 px-4 py-12">
      <div className="bg-white shadow-md rounded-lg w-full max-w-md p-8 border border-gray-200">
        <div className="text-center mb-8">
          <img src={logo} alt="neumoon Logo" className="mx-auto w-20 h-20 object-contain mb-4" />
          <h2 className="text-2xl font-bold text-gray-800">neumoon</h2>
          <p className="text-sm text-gray-500 mt-1">Login to your account</p>
        </div>
        
        <form onSubmit={handleLogin} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <input
              id="email"
              type="email"
              placeholder="Your email address"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full border border-gray-300 rounded-lg py-3 px-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              Password
            </label>
            <input
              id="password"
              type="password"
              placeholder="Your password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="w-full border border-gray-300 rounded-lg py-3 px-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <button
            type="submit"
            className="w-full py-3 px-4 text-white bg-blue-600 hover:bg-blue-700 rounded-lg font-medium transition-colors duration-200"
          >
            LOG IN
          </button>
        </form>
        
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            Don't have an account?{" "}
            <button 
              onClick={() => navigate("/register")} 
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              Signup
            </button>
          </p>
        </div>
        
        <div className="text-center mt-4">
          <button 
            onClick={() => navigate("/forgot-password")} 
            className="text-sm text-blue-600 hover:text-blue-800 font-medium"
          >
            Forgot Password?
          </button>
          <span className="mx-2 text-gray-400">|</span>
          <button 
            onClick={() => navigate("/")} 
            className="text-sm text-blue-600 hover:text-blue-800 font-medium"
          >
            Return to Home
          </button>
        </div>
      </div>
    </section>
  );
};

export default Login;