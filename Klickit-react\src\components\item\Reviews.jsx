import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import maiin from "../../assets/images/man.png";

const Reviews = ({
  reviews = [],
  avgRating = 0,
  reviewCount = 0,
  hasOrdered,
  productId,
}) => {
  const navigate = useNavigate();
  const [expandedReviews, setExpandedReviews] = useState({});

  const renderStars = (rating) => {
    return [...Array(5)].map((_, index) => (
      <i
        key={index}
        className={`fas fa-star ${index < rating ? "text-gold" : "text-gray-300"}`}
      />
    ));
  };

  const toggleExpand = (reviewId) => {
    setExpandedReviews((prev) => ({
      ...prev,
      [reviewId]: !prev[reviewId],
    }));
  };

  return (
    <section className="lg:px-[60px] sm:px-[40px] px-[16px] my-[88px]">
      <div>
        <h1 className="text-[24px] font-[500] leading-[32px] mb-[48px]">Reviews</h1>

        {/* Average Rating */}
        <div className="md:min-w-[174px] py-[16px] rounded-[25px] bg-[#FAFAFA] px-[32px] text-center md:mr-[48px] mb-5">
          <p className="text-[56px] font-[500]">{avgRating.toFixed(1)}</p>
          <p className="text-[15px] text-[#CDCDCD] mb-[16px]">of {reviewCount} reviews</p>
        </div>

        {/* Add Review Button */}
        {hasOrdered && (
          <button
            onClick={() => navigate(`/product/${productId}/reviews/`)}
            className="inline-block w-full mb-3 text-center bg-[#2a2a2a] hover:bg-[#000000] text-white font-semibold py-2 px-4 rounded mt-4"
          >
            Add Review
          </button>
        )}

        {/* List of Reviews */}
        {reviews.length > 0 ? (
          reviews.map((review) => {
            const reviewRef = useRef(null);
            const [needsMore, setNeedsMore] = useState(false);

            useEffect(() => {
              if (reviewRef.current) {
                const lineHeight = 24; // Matches leading-[24px]
                const maxHeight = lineHeight * 3; // 3 lines max
                setNeedsMore(reviewRef.current.scrollHeight > maxHeight);
              }
            }, [review.comment]);

            return (
              <div
                key={review.id}
                className="flex bg-[#FAFAFA] py-[24px] rounded-[10px] mb-[24px] cursor-pointer"
                onClick={() => toggleExpand(review.id)}
              >
                {/* User Image */}
                <div className="mr-[16px] pl-[16px] min-w-[56px]">
                  <img
                    src={maiin}
                    alt="user"
                    className="max-w-[36px] min-w-[36px] md:max-w-[56px] md:min-w-[56px] h-[36px] md:h-[56px] rounded-full"
                  />
                </div>

                {/* Review Content */}
                <div className="pr-[28px]">
                  {/* User Name */}
                  <h4 className="text-[17px] font-[600]">
                    {review.user.first_name} {review.user.last_name || ""}
                  </h4>

                  {/* Star Rating */}
                  <div className="flex pb-[12px] rating">{renderStars(review.rating)}</div>

                  {/* User Comment */}
                  <p
                    ref={reviewRef}
                    className={`sm:text-[15px] text-[12px] font-[500] sm:leading-[24px] text-[#7E7E7E] ${
                      expandedReviews[review.id] ? 'review-text-expanded' : 'review-text-truncate'
                    }`}
                  >
                    {review.comment}
                  </p>
                </div>
              </div>
            );
          })
        ) : (
          <p>No reviews yet. Be the first to leave a review!</p>
        )}
      </div>
    </section>
  );
};

export default Reviews;
