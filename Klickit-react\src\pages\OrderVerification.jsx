import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { BASE_URL } from '../utils/config';

const OrderVerification = () => {
  const [message, setMessage] = useState('Verifying your payment, please wait...');
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    const sessionId = new URLSearchParams(location.search).get('session_id');

    if (!sessionId) {
      setMessage('Invalid session. Redirecting to home...');
      setTimeout(() => navigate('/'), 3000);
      return;
    }

    const verifyPayment = async () => {
      try {
        const token = localStorage.getItem("access_token");
        const response = await fetch(`${BASE_URL}/verify-payment/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ session_id: sessionId })
        });

        const data = await response.json();

        if (response.ok && data.success) {
          setMessage('Payment verified! Redirecting to your order confirmation...');
          navigate(`/order-success/${data.order_id}`);
        } else {
          throw new Error(data.message || 'Payment verification failed.');
        }
      } catch (error) {
        setMessage(error.message || 'An error occurred during verification.');
        setTimeout(() => navigate('/'), 5000);
      }
    };

    verifyPayment();
  }, [location, navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="text-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
        <p className="text-lg text-gray-700">{message}</p>
      </div>
    </div>
  );
};

export default OrderVerification;
