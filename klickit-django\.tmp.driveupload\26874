from rest_framework import serializers
from .models import *
from users.models import User
from main.serializers import PaymentSerializer
from items.models import Product, ProductVariant, Color, Attribute, ProductAttributeValue

# Helper Serializers ----------------------------------------------------------
class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'email', 'phone_number', 'first_name', 'last_name']

class ProductSerializer(serializers.ModelSerializer):
    class Meta:
        model = Product
        fields = ['id', 'name']

class ColorSerializer(serializers.ModelSerializer):
    class Meta:
        model = Color
        fields = ["id", "name", "color"]

class AttributeSerializer(serializers.ModelSerializer):
    class Meta:
        model = Attribute
        fields = ['id', 'name']

class ProductAttributeValueSerializer(serializers.ModelSerializer):
    attribute = AttributeSerializer(read_only=True)

    class Meta:
        model = ProductAttributeValue
        fields = ['id', 'attribute', 'value']

class ProductVariantSerializer(serializers.ModelSerializer):
    product = ProductSerializer(read_only=True)
    color = ColorSerializer(read_only=True)
    attribute_values = ProductAttributeValueSerializer(many=True, read_only=True)
    mainimage = serializers.SerializerMethodField()
    images = serializers.SerializerMethodField()

    class Meta:
        model = ProductVariant
        fields = ['id', 'name', 'product', 'color', 'attribute_values', 'sku', 'product_model',
                 'regular_price', 'sale_price', 'stock', 'mainimage', 'images']

    def get_mainimage(self, obj):
        if obj.mainimage and hasattr(obj.mainimage, 'url'):
            return obj.mainimage.url
        return None

    def get_images(self, obj):
        variant_images = obj.images.all()
        image_urls = []
        for img_instance in variant_images:
            if img_instance.image and hasattr(img_instance.image, 'url'):
                image_urls.append(img_instance.image.url)
        return image_urls

# Main Serializers ------------------------------------------------------------
class CustomerSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)

    class Meta:
        model = Customer
        fields = ['id', 'customer_id', 'user']

class CartItemSerializer(serializers.ModelSerializer):
    variant = ProductVariantSerializer(read_only=True)
    total_price = serializers.SerializerMethodField()

    class Meta:
        model = CartItem
        fields = ['id', 'variant', 'quantity', 'price', 'total_price']

    def get_total_price(self, obj):
        return round(obj.price * obj.quantity, 2)  # Ensures proper formatting

class WishlistSerializer(serializers.ModelSerializer):
    variant = ProductVariantSerializer(read_only=True)

    class Meta:
        model = Wishlist
        fields = ['id', 'customer', 'variant', 'created_datetime']

class ServiceSerializer(serializers.ModelSerializer):
    image = serializers.SerializerMethodField()

    class Meta:
        model = Service
        fields = ['id', 'title', 'description', 'image']

    def get_image(self, obj):
        if obj.image and hasattr(obj.image, 'url'):
            return obj.image.url  
        return None

class ServiceRequestSerializer(serializers.ModelSerializer):
    service_id = serializers.PrimaryKeyRelatedField(
        queryset=Service.objects.all(), source="service"
    )

    class Meta:
        model = ServiceRequest
        fields = ['name', 'email', 'phone', 'service_id', 'details']

class CouponSerializer(serializers.ModelSerializer):
    is_valid = serializers.BooleanField(read_only=True)

    class Meta:
        model = Coupon
        fields = ['id', 'code', 'description', 'is_Percentage', 'discount_value', 
                 'active', 'valid_from', 'valid_until', 'is_onece_peruser', 'is_valid']
        
        

class AddressSerializer(serializers.ModelSerializer):
    class Meta:
        model = Address
        fields = ['id', 'customer', 'address1', 'address2', 'city', 
                'state', 'pincode', 'address_type', 'is_default']

class CartTotalSerializer(serializers.ModelSerializer):
    class Meta:
        model = CartTotal
        fields = ['id', 'item_total', 'total', 'offer', 'delivery', 'customer']

class OrderItemSerializer(serializers.ModelSerializer):
    variant = ProductVariantSerializer(read_only=True)

    class Meta:
        model = OrderItem
        fields = ['id', 'customer', 'variant', 'quantity', 'amount']

class OrderSerializer(serializers.ModelSerializer):
    items = OrderItemSerializer(many=True, read_only=True)
    order_status_display = serializers.CharField(source='get_order_status_display', read_only=True)
    payment_status_display = serializers.CharField(source='get_payment_status_display', read_only=True)
    address = AddressSerializer(read_only=True)
    payment = PaymentSerializer(read_only=True)
    payment_method = serializers.CharField(read_only=True)

    class Meta:
        model = Order
        fields = ['id', 'customer', 'address', 'order_id', 'items', 'sub_total',
                'delivery_charge', 'offer', 'total', 'first_name', 'last_name',
                'email', 'phone_number', 'order_status', 'order_status_display',
                'payment_status', 'payment_status_display', 'payment',
                'address1', 'address2', 'city', 'state', 'pincode', 'address_type',
                'payment_method', 'created_datetime']

class ReviewSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    product = ProductSerializer(read_only=True)

    class Meta:
        model = Review
        fields = ['id', 'product', 'user', 'rating', 'comment', 'created_at']
        read_only_fields = ['created_at']