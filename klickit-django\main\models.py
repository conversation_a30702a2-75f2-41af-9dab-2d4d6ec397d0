from django.db import models
from users.models import User
from decimal import Decimal
from django.core.exceptions import ValidationError


class CommonModel(models.Model):

    """
    A common abstract class for inheriting some common fields
    """

    created_datetime = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_datetime = models.DateTimeField(auto_now=True, blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(User, related_name='+', 
                                        blank=True, null=True, on_delete=models.SET_NULL)
    updated_by = models.ForeignKey(User, related_name='+',
                                        blank=True, null=True, on_delete=models.SET_NULL)

    class Meta:
        abstract = True


class Payment(CommonModel):
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    vat_amount = models.DecimalField(max_digits=10, decimal_places=2)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.Char<PERSON>ield(max_length=3, default='AED')
    payment_intent_id = models.CharField(max_length=255)
    status = models.CharField(max_length=50)
    customer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='payments')
    description = models.TextField(blank=True, null=True)
    metadata = models.JSONField(default=dict, blank=True)
    retry_count = models.PositiveIntegerField(default=0)
    max_retries = models.PositiveIntegerField(default=3)

    
    # VAT_RATE = Decimal('0.05')  # 5% VAT rate

    def clean(self):
        if self.amount <= Decimal('0.00'): 
            raise ValidationError('Amount (total amount including VAT) must be greater than zero.')
        if self.retry_count > self.max_retries:
            raise ValidationError('Maximum retry attempts exceeded.')
    def save(self, *args, **kwargs):
        self.total_amount = self.amount
        self.vat_amount = Decimal('0.00')
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"Payment {self.payment_intent_id} - {self.status}"

    def can_retry(self):
        return self.retry_count < self.max_retries

    def increment_retry(self):
        if self.can_retry():
            self.retry_count += 1
            self.save()
        else:
            pass
