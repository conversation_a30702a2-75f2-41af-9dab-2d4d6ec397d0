{% extends "base/admin-base.html" %}
{% block container %}
{% include 'includes/seller-nav.html' %}
<div class="content-body">
    <div class="row page-titles mx-0"><div class="col p-md-0"><ol class="breadcrumb"><li class="breadcrumb-item"><a href="javascript:void(0)">{{ sub_title }}</a></li><li class="breadcrumb-item active"><a href="javascript:void(0)">{{ name }}</a></li></ol></div></div>
    <div class="container-fluid"><div class="row"><div class="col-12"><div class="card"><div class="card-body">
        <div class="heading-line">
            <h4 class="card-title m-0 p-0">{{ name }} - Step 3</h4>
            <div><span class="badge badge-info">Product: {{ product.name }}</span></div>
        </div>
        {% if messages %}<div class="mt-3">{% for message in messages %}<div class="alert {% if message.tags %}alert-{{ message.tags }}{% else %}alert-info{% endif %}" role="alert">{{ message }}</div>{% endfor %}</div>{% endif %}
        <div class="mt-4"><h5>Variants Added:</h5>{% if options_added %}<div class="table-responsive"><table class="table table-sm table-bordered"><thead><tr><th>Name</th><th>Color</th><th>RAM</th><th>Storage</th><th>Price</th><th>Stock</th><th>Action</th></tr></thead><tbody>{% for opt in options_added %}<tr><td>{{ opt.name|default:"-" }}</td><td>{{ opt.color.name|default:"-" }}</td><td>{{ opt.ram|default:"-" }}</td><td>{{ opt.storage|default:"-" }}</td><td>{{ opt.sale_price }}</td><td>{{ opt.stock }}</td><td><a href="{% url 'managers:delete_wizard_option' product_id=product.id pk=opt.pk %}" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure?');">Delete</a></td></tr>{% endfor %}</tbody></table></div>{% else %}<p>No variants added yet.</p>{% endif %}</div><hr>
        <h5>Add / Update Variants:</h5>
        <form method="post" class="mt-3">
            {% csrf_token %}
            <div id="variant-rows-container">
                <div class="form-row align-items-center mb-3 p-3 border rounded variant-row">
                    <div class="form-group col-md-4"><label>Name</label><input type="text" name="name" class="form-control" placeholder="e.g., Purple, 128GB"></div>
                    <div class="form-group col-md-4"><label>Color</label><select name="color" class="form-control"><option value="">---------</option>{% for color in available_colors %}<option value="{{ color.id }}">{{ color.name }}</option>{% endfor %}</select></div>
                    <div class="form-group col-md-4"><label>RAM</label><input type="text" name="ram" class="form-control" placeholder="e.g., 8GB"></div>
                    <div class="form-group col-md-4"><label>Storage</label><input type="text" name="storage" class="form-control" placeholder="e.g., 128GB"></div>
                    <div class="form-group col-md-2"><label>Regular Price</label><input type="number" step="0.01" name="regular_price" class="form-control"></div>
                    <div class="form-group col-md-2"><label>Sale Price</label><input type="number" step="0.01" name="sale_price" class="form-control" required></div>
                    <div class="form-group col-md-2"><label>Stock</label><input type="number" name="stock" class="form-control" required></div>
                    <div class="form-group col-md-2 d-flex align-items-end"><button type="button" class="btn btn-danger btn-sm remove-variant-row">Remove</button></div>
                </div>
            </div>
            <button type="button" id="add-variant-row" class="btn btn-info mt-2"><i class="fa fa-plus"></i> Add Another Variant</button>
            <hr>
            <div class="d-flex justify-content-between mt-4">
                <button type="submit" class="btn btn-success">Save All Variants</button>
                <a href="{% url 'managers:seller_add_product_wizard_step4_images' product_id=product.id %}" class="btn btn-primary">Continue to Add Images</a>
            </div>
        </form>
    </div></div></div></div></div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    function cloneRow(containerId, rowClass) {
        const container = document.getElementById(containerId);
        const firstRow = container.querySelector(rowClass);
        if (!firstRow) return;
        const newRow = firstRow.cloneNode(true);
        newRow.querySelectorAll('input, select').forEach(input => {
            if (input.type !== 'checkbox' && input.type !== 'radio') input.value = '';
            if (input.tagName === 'SELECT') input.selectedIndex = 0;
        });
        container.appendChild(newRow);
    }
    document.getElementById('add-variant-row').addEventListener('click', () => cloneRow('variant-rows-container', '.variant-row'));
    document.getElementById('variant-rows-container').addEventListener('click', function(e) {
        if (e.target && e.target.classList.contains('remove-variant-row')) {
            if (document.querySelectorAll('.variant-row').length > 1) {
                e.target.closest('.variant-row').remove();
            } else {
                alert("You can't remove the last row.");
            }
        }
    });
});
</script>
{% endblock %}