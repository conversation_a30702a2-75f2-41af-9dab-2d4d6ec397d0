import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import LoadingSpinner from "../includes/LoadingSpinner";
import { BASE_URL } from "../../utils/config";
import { loadStripe } from '@stripe/stripe-js';
import useCartStore from "../../store/cartStore";

const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);

export default function Checkout() {
    const [orderPlaced, setOrderPlaced] = useState(false);
    const [addresses, setAddresses] = useState([]);
    const [selectedAddress, setSelectedAddress] = useState("");
    const [paymentMethod, setPaymentMethod] = useState("COD");
    const [formData, setFormData] = useState({
        first_name: "",
        last_name: "",
        email: "",
        phone_number: ""
    });
    const [error, setError] = useState(null);
    const [loading, setLoading] = useState(true);
    const [isPlacingOrder, setIsPlacingOrder] = useState(false);
    const navigate = useNavigate();
    
    // Get cart data and actions from store
    const { 
        items: cartItems, 
        subtotal, 
        total, 
        delivery, 
        discount,
        setCartItems,
        setCartTotal 
    } = useCartStore();



    const handleInputChange = (e) => {
        setFormData({ ...formData, [e.target.name]: e.target.value });
    };
    
    useEffect(() => {
        const fetchCheckoutSpecificData = async () => {
            try {
                const token = localStorage.getItem("access_token");
                const response = await fetch(`${BASE_URL}/checkout/`, {
                    headers: { "Authorization": `Bearer ${token}` }
                });

                if (!response.ok) {
                    throw new Error("Failed to load checkout data.");
                }

                const data = await response.json();
                setAddresses(data.addresses || []);
                
                if (data.user) {
                    setFormData({
                        first_name: data.user.first_name || "",
                        last_name: data.user.last_name || "",
                        email: data.user.email || "",
                        phone_number: data.user.phone_number || ""
                    });
                }
            } catch (err) {
                setError(err.message);
            } finally {
                // This ensures the loading spinner is always removed.
                setLoading(false);
            }
        };

        fetchCheckoutSpecificData();
    }, [navigate]);

    const handlePlaceOrder = async (e) => {
        e.preventDefault();
        console.log("Current paymentMethod in handlePlaceOrder:", paymentMethod); // Debug log
        if (!selectedAddress) {
            setError("Please select an address.");
            return;
        }
        
        // Prevent COD for orders over 1000 AED
        if (paymentMethod === 'COD' && total > 1000) {
            setError("Cash On Delivery is only available for orders up to 1000 AED.");
            return;
        }

        setIsPlacingOrder(true);
        setError(null);

        const orderDetails = {
            address_id: selectedAddress,
            ...formData
        };

        try {
            const token = localStorage.getItem("access_token");
            const endpoint = `${BASE_URL}/checkout/`;
            const response = await fetch(endpoint, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${token}`
                },
                body: JSON.stringify({ ...orderDetails, payment_method: paymentMethod })
            });
    
            const data = await response.json();
            if (!response.ok) throw new Error(data.message || "Something went wrong");
            
            if (paymentMethod === 'ONLINE') {
                console.log('Online payment selected, redirecting to Stripe Checkout');
                const stripe = await stripePromise;
                const { sessionId } = data;
                
                if (!sessionId) {
                    throw new Error('No session ID received from server');
                }
                
                // Redirect to Stripe Hosted Checkout
                const { error } = await stripe.redirectToCheckout({ sessionId });
                
                if (error) {
                    throw error;
                }
            } else if (paymentMethod === 'COD') {
                console.log("Response from server for COD:", data);
                if (data.success && data.order_id) {
                    navigate(`/order-success/${data.order_id}`);
                } else {
                    setError(data.message || "Failed to place Cash on Delivery order. Please try again.");
                }
            }
        } catch (err) {
            console.error("Checkout Error:", err);
            setError(err.message);
        } finally {
            setIsPlacingOrder(false);
        }
    };

    if (loading || isPlacingOrder) {
        return (
            <div>
                <LoadingSpinner />
            </div>
        );
    }

    return (
        <section className="py-10 mt-[7px]">
            <div className="wrapper">
                        <>
                            {orderPlaced && (
                                <div id="submit-animation" className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
                                    <div className="bg-green-500 text-white p-5 rounded-xl text-center">
                                        <div className="success-icon bg-white p-4 rounded-full inline-block">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="w-10 h-10 text-green-500">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                            </svg>
                                        </div>
                                        <h2 className="mt-3 text-lg font-semibold">Order Successfully Placed!</h2>
                                        <button onClick={() => navigate('/orders')} className="mt-3 px-4 py-2 bg-white text-green-500 rounded">View Details</button>
                                    </div>
                                </div>
                            )}

                            <p className="text-[18px] font-semibold mb-7 flex items-center">
                                <button onClick={() => navigate(`/cart`)} className="hover:text-purple-500 font-semibold text-[18px] flex items-center">
                                    <i className='bx bx-arrow-back text-[20px] mr-2'></i>
                                </button>
                                Checkout
                            </p>

                            <form onSubmit={handlePlaceOrder} className="grid grid-cols-1 md:grid-cols-5 gap-10">
                                {/* Contact Information Section */}
                                <div className="col-span-1 md:col-span-3">
                                    <h3 className="text-[18px] font-semibold mb-5">Contact Information</h3>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-5 mb-10">
                                        <div className="p-1 border rounded-xl px-5 col-span-1">
                                            <input
                                                type="text"
                                                className="w-full py-3 bg-white text-gray-500 placeholder:text-gray-400 placeholder:text-[13px] text-[14px] outline-none"
                                                name="first_name"
                                                placeholder="First Name"
                                                value={formData.first_name}
                                                onChange={handleInputChange}
                                                required
                                            />
                                        </div>
                                        <div className="p-1 border rounded-xl px-5 col-span-1">
                                            <input
                                                type="text"
                                                className="w-full py-3 bg-white text-gray-500 placeholder:text-gray-400 placeholder:text-[13px] text-[14px] outline-none"
                                                name="last_name"
                                                placeholder="Last Name"
                                                value={formData.last_name}
                                                onChange={handleInputChange}
                                                required
                                            />
                                        </div>
                                        <div className="p-1 border rounded-xl px-5 col-span-1">
                                            <input
                                                type="email"
                                                className="w-full py-3 bg-white text-gray-500 placeholder:text-gray-400 placeholder:text-[13px] text-[14px] outline-none"
                                                name="email"
                                                placeholder="Email"
                                                value={formData.email}
                                                onChange={handleInputChange}
                                                required
                                            />
                                        </div>
                                        <div className="p-1 border rounded-xl px-5 col-span-1">
                                            <input
                                                type="text"
                                                className="w-full py-3 bg-white text-gray-500 placeholder:text-gray-400 placeholder:text-[13px] text-[14px] outline-none"
                                                name="phone_number"
                                                placeholder="Phone Number"
                                                value={formData.phone_number}
                                                onChange={handleInputChange}
                                                required
                                            />
                                        </div>
                                    </div>
                                </div>

                                {/* Address Selection Section */}
                                <div className="col-span-1 md:col-span-3">
                                    <div className="flex items-center justify-between">
                                        <h3 className="text-[18px] font-semibold mb-5">Select Address</h3>
                                        <a href="/addresses/add" className="text-green-600 hover:text-green-800 flex items-center space-x-1">
                                            <i className="bx bx-plus-circle text-lg"></i>
                                            <span className="text-sm">Add Address</span>
                                        </a>
                                    </div>
                                    <div className="relative max-h-[210px] overflow-y-auto swip-container">
                                        <div className="space-y-4 pr-1">
                                            {addresses.map((address) => (
                                                <div key={address.id} className="p-4 border rounded-lg flex items-center justify-between bg-gray-50 hover:bg-gray-100 transition">
                                                    <div>
                                                        <p className="text-[14px] font-semibold">{address.address1}, {address.address2}</p>
                                                        <p className="text-[13px] text-gray-500">{address.city}, {address.state}, {address.pincode}</p>
                                                        <p className="text-[13px] text-gray-500">Type: {address.address_type}</p>
                                                    </div>
                                                    <div className="flex justify-end items-center space-x-3">
                                                        <a href={`/addresses/${address.id}/edit`} className="text-blue-600 hover:text-blue-800 flex items-center space-x-1">
                                                            <i className="bx bx-edit text-lg"></i>
                                                            <span className="text-sm">Edit</span>
                                                        </a>
                                                    </div>
                                                    <div>
                                                        <input
                                                            type="radio"
                                                            name="address_id"
                                                            value={address.id}
                                                            onChange={() => setSelectedAddress(address.id)}
                                                            className="h-5 w-5 accent-[#A41E11]"
                                                            required
                                                        />
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </div>

                                {/* Payment Method Section */}
                                <div className="col-span-1 md:col-span-3 mt-10">
                                    <h3 className="text-[18px] font-semibold mb-5">Payment Method</h3>
                                    <div className="space-y-3">
                                        {/* Cash on Delivery Option */}
                                        <label 
                                            className={`flex items-start space-x-3 p-4 border rounded-lg transition-all ${total > 1000 ? 'opacity-60 cursor-not-allowed bg-gray-50' : 'cursor-pointer hover:bg-gray-50'} ${
                                                paymentMethod === 'COD' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                                            }`}
                                        >
                                            <div className="flex items-center h-5 mt-0.5">
                                                <input
                                                    type="radio"
                                                    name="payment_method"
                                                    value="COD"
                                                    checked={paymentMethod === "COD"}
                                                    onChange={(e) => total <= 1000 && setPaymentMethod(e.target.value)}
                                                    disabled={total > 1000}
                                                    className="h-5 w-5 border-2 border-gray-300 rounded-full checked:bg-blue-600 checked:border-blue-600 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
                                                />
                                            </div>
                                            <div className="flex-1">
                                                <div className="flex items-center justify-between">
                                                    <span className={`text-gray-800 text-[15px] font-medium ${total > 1000 ? 'text-gray-500' : ''}`}>
                                                        Cash On Delivery
                                                    </span>
                                                    {paymentMethod === 'COD' && (
                                                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                            Selected
                                                        </span>
                                                    )}
                                                </div>
                                                <p className="mt-1 text-sm text-gray-500">
                                                    Pay with cash upon delivery
                                                </p>
                                                {total > 1000 ? (
                                                    <div className="mt-2 p-2 bg-yellow-50 rounded-md">
                                                        <p className="text-xs text-yellow-700">
                                                            <i className="bx bxs-info-circle mr-1"></i>
                                                            Cash on Delivery is not available for orders above 1000 AED. Please use card payment.
                                                        </p>
                                                    </div>
                                                ) : null}
                                            </div>
                                        </label>

                                        {/* Pay Online Option */}
                                        <label 
                                            className={`flex items-start space-x-3 p-4 border rounded-lg transition-all cursor-pointer hover:bg-gray-50 ${
                                                paymentMethod === 'ONLINE' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                                            }`}
                                        >
                                            <div className="flex items-center h-5 mt-0.5">
                                                <input
                                                    type="radio"
                                                    name="payment_method"
                                                    value="ONLINE"
                                                    checked={paymentMethod === "ONLINE"}
                                                    onChange={(e) => setPaymentMethod(e.target.value)}
                                                    className="h-5 w-5 border-2 border-gray-300 rounded-full checked:bg-blue-600 checked:border-blue-600 focus:ring-blue-500 focus:ring-offset-2"
                                                />
                                            </div>
                                            <div className="flex-1">
                                                <div className="flex items-center justify-between">
                                                    <span className="text-gray-800 text-[15px] font-medium">
                                                        Pay Online
                                                    </span>
                                                    {paymentMethod === 'ONLINE' && (
                                                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                            Selected
                                                        </span>
                                                    )}
                                                </div>
                                                <p className="mt-1 text-sm text-gray-500">
                                                    Pay with card, Google Pay, or Apple Pay
                                                </p>
                                                <div className="mt-2 flex space-x-2 items-center">
                                                    <div className="h-6 w-8 bg-gray-100 rounded flex items-center justify-center">
                                                        <i className="bx bxs-credit-card text-gray-500"></i>
                                                    </div>
                                                    <div className="h-6 w-8 bg-gray-100 rounded flex items-center justify-center">
                                                        <i className="bx bxl-visa text-blue-800"></i>
                                                    </div>
                                                    <div className="h-6 w-8 bg-gray-100 rounded flex items-center justify-center">
                                                        <i className="bx bxl-mastercard text-red-600"></i>
                                                    </div>
                                                    <div className="h-6 w-8 bg-gray-100 rounded flex items-center justify-center">
                                                        <i className="bx bxl-google text-gray-700"></i>
                                                    </div>
                                                    <div className="h-6 w-8 bg-gray-100 rounded flex items-center justify-center">
                                                        <i className="bx bxl-apple text-gray-800"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>

                                {/* Order Summary Section */}
                                <div className="col-span-1 md:col-span-2 md:fixed md:right-[30px] lg:right-[100px] md:top-[150px] md:w-[300px]">
                                    <div className="p-5 sm:p-7 md:p-10 rounded-2xl shadow-2xl mb-4">
                                        <div className="flex justify-between items-center mb-2">
                                            <p className="text-[14px] font-thin opacity-80">SubTotal</p>
                                            <p className="text-[14px] flex items-start">
                                                {subtotal.toFixed(2)} AED 
                                            </p>
                                        </div>
                                        <div className="flex justify-between items-center mb-2">
                                            <p className="text-[14px] font-thin opacity-80">Discount</p>
                                            <p className="text-[14px] flex items-start">                                    
                                                {discount.toFixed(2)} AED 
                                            </p>
                                        </div>
                                        <div className="flex justify-between items-center mb-4">
                                            <p className="text-[14px] font-thin opacity-80">Shipping</p>
                                            <p className="text-[14px] flex items-start">
                                                {delivery.toFixed(2)} AED 
                                            </p>
                                        </div>
                                        <div className="mb-5 border-b"></div>
                                        <div className="flex justify-between items-center mb-7">
                                            <p className="text-[16px] font-semibold opacity-80">TOTAL</p>
                                            <p className="text-[16px] font-semibold flex items-start">
                                                {total.toFixed(2)} AED 
                                            </p>
                                        </div>
                                        <button
                                        type="submit"
                                        className={`w-full text-white text-[16px] font-semibold rounded-full py-3 px-5 text-center block ${isPlacingOrder ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#A41E11] hover:bg-[#8c180d] cursor-pointer'}`}
                                        disabled={isPlacingOrder}
                                    >
                                        {isPlacingOrder ? 'Processing...' : paymentMethod === 'COD' ? 'Place Order' : 'Proceed to Pay'}
                                    </button>
                                </div>
                            </div>
                        </form>
                    </>
                </div>
            </section>
    );
}
