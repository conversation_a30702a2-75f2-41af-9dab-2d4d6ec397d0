# Generated by Django 5.2 on 2025-06-10 11:52

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('items', '0007_product_seller'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='product',
            name='specifications',
        ),
        migrations.AddField(
            model_name='customspecification',
            name='product',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='custom_specs', to='items.product'),
        ),
        migrations.AlterField(
            model_name='customspecification',
            name='name',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
    ]
