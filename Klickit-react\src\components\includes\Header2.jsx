import React, { useState, useEffect } from "react";
import logo from "../../assets/images/logooo.png";
import serviceIcon from "../../assets/images/service-icon.png";
import menuIcon from "../../assets/images/menu.png";
import userIcon from "../../assets/images/user.png";
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import '../../index.css';
import { BASE_URL } from "../../utils/config";

const Header2 = ({ user }) => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [profileMenuOpen, setProfileMenuOpen] = useState(false);
  const navigate = useNavigate();
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [cartCount, setCartCount] = useState(0);


  // Initialize Axios headers and fetch cart count on app load
  useEffect(() => {
    const token = localStorage.getItem("access_token");
    if (token) {
      axios.defaults.headers.common["Authorization"] = `Bearer ${token}`;
      setIsLoggedIn(true);
      fetchCartCount(); // Fetch cart count when the component mounts
    }
  }, []);

  // Fetch cart count whenever the login state changes
  useEffect(() => {
    if (isLoggedIn) {
      fetchCartCount();
    } else {
      setCartCount(0); // Reset cart count if user logs out
    }
  }, [isLoggedIn]);

  // Function to fetch cart count from the backend
  const fetchCartCount = async () => {
    try {
      const response = await axios.get(`${BASE_URL}/`);
      setCartCount(response.data.count || 0);
    } catch (error) {
      console.error("Error fetching cart count:", error);
    }
  };

  useEffect(() => {
    const handleScroll = () => {
      setProfileMenuOpen(false); // Close menu on scroll
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const handleLogout = () => {
        localStorage.removeItem("access_token");
        localStorage.removeItem("refresh_token");
        window.location.href = "/login";
    };
    

  return (
    <header className="fixed top-0 left-0 w-full shadow-md bg-white z-1000">
      {/* Desktop Header */}
      <section className="wrapper flex justify-between items-center h-[80px]">
        {/* Logo */}
        <h1>
          <a onClick={() => navigate(`/`)} className="cursor-pointer">
            <img src={logo} alt="logo" className="w-[120px] md:w-[150px] h-auto" />
          </a>
        </h1>


        {/* Header Icons */}
        <div>
          <ul className="flex justify-end items-center">
            {/* Service */}
            <li className="mr-1 md:mr-7 hidden md:flex">
              <a
                onClick={() => navigate(`/service`)}
                className="text-[16px] cursor-pointer font-semibold flex items-center hover:text-purple-500"
              >
                <img
                  className="h-[35px] md:h-[16px] mr-[5px]"
                  src={serviceIcon}
                  alt="service"
                />
                <p className="hidden md:block">Service</p>
              </a>
            </li>

            {/* Wishlist */}
            <li className="mr-1 md:mr-7">
              <a onClick={() => navigate(`/wishlist`)} className="text-[16px] cursor-pointer font-semibold flex items-center hover:text-purple-500">
                <i className="text-[35px] md:text-[16px] bx bx-heart"></i>
                <p className="hidden md:block">Wishlist</p>
              </a>
            </li>

            {/* Cart */}
            <li className="mr-7 px-3 border-l border-r flex items-center justify-center">
              <a onClick={() => navigate(`/cart`)} className="relative cursor-pointer hover:text-purple-500">
                <i className="text-[35px] bx bx-cart"></i>
                <span className="absolute w-[16px] h-[16px] bg-gray-600 rounded-full text-white text-[9px] flex items-center justify-center top-[9px] right-0 transform translate-x-[50%] translate-y-[-50%]">
                  {cartCount}
                </span>
              </a>
            </li>

            {/* Profile Menu */}
            <li
              className="mr-7 hidden md:block cursor-pointer"
              onClick={() => setProfileMenuOpen(!profileMenuOpen)}
            >
              <p className="text-[16px] font-semibold flex items-center hover:text-purple-500">
                <i className="font-bold mr-[5px] bx bx-user"></i>
                <span>Account</span>
              </p>
              {profileMenuOpen && (
                <div className="absolute z-50 top-[80px] bg-gray-100 right-[5%] border shadow rounded-b-xl p-5 w-[240px]">
                  <p className="font-semibold border-b cursor-default pb-2">
                    My Account
                  </p>
                  <p className="text-[16px] py-2 hover:text-purple-500">
                    <a onClick={() => navigate(`/account`)} className="cursor-pointer">Manage Profile</a>
                  </p>
                  <p className="text-[16px] py-2 hover:text-purple-500">
                    <a onClick={() => navigate(`/orders`)} className="cursor-pointer">View Orders</a>
                  </p>
                  <p className="text-[16px] py-2 hover:text-purple-500 border-b">
                    <a onClick={() => navigate(`/wishlist`)} className="cursor-pointer">Manage Wishlist</a>
                  </p>
                  <p className="text-[16px] py-2 hover:text-purple-500 border-b">
                    <a onClick={() => navigate(`/become-seller`)} className="cursor-pointer">Become a Seller</a>
                  </p>
                  {isLoggedIn ? (
                    <p className="text-[16px] py-2 hover:text-purple-500 border-b">
                      <a onClick={handleLogout} className="cursor-pointer">Logout</a>
                    </p>
                  ) : (
                    <p className="text-[16px] py-2 hover:text-purple-500">
                      <a onClick={() => navigate(`/login`)} className="cursor-pointer">Login</a>
                    </p>
                  )}
                </div>
              )}
            </li>

            {/* Mobile Menu Button */}
            <li className="md:hidden">
              <img
                src={menuIcon}
                alt="menu"
                className="w-[28px] cursor-pointer"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              />
            </li>
          </ul>
        </div>
      </section>

      {/* Mobile Menu */}
      {mobileMenuOpen && (
        <div className="fixed top-0 left-0 h-full z-50 w-[70%] bg-gray-200">
          <div className="h-[100px] bg-slate-600 p-4 flex justify-start items-center text-white">
            <img src={userIcon} alt="user" className="w-[30px]" />
            <div className="ml-3 hover:text-purple-500">
              <p className="text-[14px] font-semibold">
                {user?.firstName || "User"} {user?.lastName || ""}
              </p>
              <a onClick={() => navigate(`/account`)} className="text-[14px] font-normal hover:text-purple-500 cursor-pointer">
                My account
              </a>
            </div>
          </div>
          <div className="p-5">
            {/* <p className="pb-3 mb-3 border-b border-gray-300">
              <a onClick={() => navigate(`/`)} className="text-[16px] font-semibold hover:text-purple-500 cursor-pointer">
                All Categories
              </a>
            </p> */}
            <p className="pb-3 mb-3 border-b border-gray-300">
              <a onClick={() => navigate(`/service`)} className="text-[16px] font-semibold hover:text-purple-500 cursor-pointer">
                Service
              </a>
            </p>
            <p className="pb-3 mb-3 border-b border-gray-300">
              <a onClick={() => navigate(`/cart`)} className="text-[16px] font-semibold hover:text-purple-500 cursor-pointer">
                Cart
              </a>
            </p>
            <p className="pb-3 mb-3 border-b border-gray-300">
              <a onClick={() => navigate(`/orders`)} className="text-[16px] font-semibold hover:text-purple-500 cursor-pointer">
                View Orders
              </a>
            </p>
            <p className="pb-3 mb-3 border-b border-gray-300">
              <a onClick={() => navigate(`/wishlist`)} className="text-[16px] font-semibold hover:text-purple-500 cursor-pointer">
                Wishlist
              </a>
            </p>
            <p className="pb-3 mb-3 border-b border-gray-300">
              <a onClick={() => navigate(`/become-seller`)} className="text-[16px] font-semibold hover:text-purple-500 cursor-pointer">
                Become a Seller
              </a>
            </p>
            {isLoggedIn ? (
              <p className="pb-3 mb-3 border-b border-gray-300">
                <a onClick={handleLogout} className="text-[16px] font-semibold hover:text-purple-500 cursor-pointer">
                  Logout
                </a>
              </p>
            ) : (
              <p className="pb-3 mb-3 border-b border-gray-300">
                <a onClick={() => navigate(`/login`)} className="text-[16px] font-semibold hover:text-purple-500 cursor-pointer">
                  Login
                </a>
              </p>
            )}
          </div>
        </div>
      )}    
    </header>
  );
};

export default Header2;