import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import warrantyIcon from "../../assets/images/verify.png";
import stockIcon from "../../assets/images/stock.png";
import deliveryIcon from "../../assets/images/delivery.png";
import '../../index.css';
import axios from 'axios';
import { BASE_URL } from "../../utils/config";

const ProductDetails = ({
    product,
    uniqueColors = [],
    groupedOptions = {},
    uniqueRams = [],
    uniqueStorages = [],
    isInCart,
    stockAvailable,
    selectedPrice,
    regularPrice,
    onAddToCart
}) => {
    const location = useLocation();
    const navigate = useNavigate();

    const [selectedColor, setSelectedColor] = useState(() => {
        const params = new URLSearchParams(window.location.search);
        const colorParam = params.get('color_id');
        return colorParam ? parseInt(colorParam, 10) : null;
    });

    const [selectedStorage, setSelectedStorage] = useState(() => {
        const params = new URLSearchParams(window.location.search);
        const storageParam = params.get('storage_value');
        return storageParam ?? '';
    });

    const [selectedRam, setSelectedRam] = useState(() => {
        const params = new URLSearchParams(window.location.search);
        const ramParam = params.get('ram_value');
        return ramParam ?? '';
    });
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [cartUpdated, setCartUpdated] = useState(false);
    const [isExpanded, setIsExpanded] = useState(false);
    const [needsMore, setNeedsMore] = useState(false);
    const descriptionRef = useRef(null);



  // URL param ഇല്ലെങ്കിൽ default സെറ്റ് ചെയ്യാനുള്ള Fallback Effect
  useEffect(() => {
    if (product?.id && selectedColor === null && selectedStorage === '' && selectedRam === '') {
         const params = new URLSearchParams(location.search);
         if (!params.has('color_id') && !params.has('storage_value') && !params.has('ram_value')) {
             const defaultOption = product?.options?.[0];
             if (defaultOption) {
                 console.log("Effect (Default Fallback): Setting state FROM DEFAULT");
                 setSelectedColor(defaultOption.color?.id || null);
                 setSelectedStorage(defaultOption.storage || '');
                 setSelectedRam(defaultOption.ram || '');
             }
         }
     }
 }, [product?.id, location.search, selectedColor, selectedStorage, selectedRam]); // state values കൂടി ചേർത്തു


    useEffect(() => {
        if (descriptionRef.current) {
            const lineHeight = 24;
            const maxHeight = lineHeight * 3;
            setNeedsMore(descriptionRef.current.scrollHeight > maxHeight);
        }
    }, [product?.description]);



    useEffect(() => {
      const params = new URLSearchParams();
      if (selectedColor !== null) params.set('color_id', selectedColor.toString());
      if (selectedStorage) params.set('storage_value', selectedStorage);
      if (selectedRam) params.set('ram_value', selectedRam);

      const queryString = params.toString();
      const currentPath = location.pathname;
      const currentSearch = location.search.substring(1);

      if (queryString !== currentSearch) {
          if (queryString) {
              navigate(`${currentPath}?${queryString}`, { replace: true, preventScrollReset: true });
          } else {
              navigate(currentPath, { replace: true, preventScrollReset: true });
          }
      }

    }, [selectedColor, selectedStorage, selectedRam, navigate, location.pathname, location.search]);



    const findSelectedOptionId = () => {

        if (!product || !product.options || product.options.length === 0) {

            return null;
        }


        const selectedOption = product.options.find(opt => {

            const colorMatch = opt.color ? opt.color.id === selectedColor : selectedColor === null;


            const storageMatch = (opt.storage === selectedStorage) || (opt.storage === null && (selectedStorage === '' || selectedStorage === null));


            const ramMatch = (opt.ram === selectedRam) || (opt.ram === null && (selectedRam === '' || selectedRam === null));

            return colorMatch && storageMatch && ramMatch;
        });

        return selectedOption ? selectedOption.id : null;
    };



    const handleAddToCart = async () => {
        const userToken = localStorage.getItem("access_token");
        if (!userToken) {
            navigate('/login');
            return;
        }


        if (!product || !product.id) {
            setError("Product data is not available.");
            alert("Product data is not available.");
            return;
        }

        setLoading(true);
        setError(null);


        const optionId = findSelectedOptionId();


        const hasOptions = product.options && product.options.length > 0;
        if (hasOptions && optionId === null) {
             setError("Please select a valid combination of options.");
             alert("Please select a valid combination of options.");
             setLoading(false);
             return;
        }
         if (!hasOptions && optionId !== null) {

             setError("Options are not available for this product.");
             alert("Options are not available for this product.");
             setLoading(false);
             return;
         }



        const payload = {
            product_id: product.id,
            quantity: 1,
        };


        if (optionId !== null) {
            payload.option_id = optionId;
        }

        try {

            const response = await axios.post(
                `${BASE_URL}/add/`,
                payload,
                {
                    headers: {
                        Authorization: `Bearer ${userToken}`,
                        "Content-Type": "application/json",
                    },
                }
            );


            if (response.status === 200 || response.status === 201) {
                setCartUpdated(true);

                if (typeof onAddToCart === 'function') {
                    onAddToCart();
                }

                 console.log("Cart response:", response.data.message);
            }
        } catch (err) {

            const errorMessage = err.response?.data?.message ||
                                 err.message ||
                                 "Failed to add item to cart";
            setError(errorMessage);

            alert(`Error: ${errorMessage}`);

            if (err.response?.status === 401) {

                navigate('/login');
            }

            console.error("Add to cart error:", err.response || err);
        } finally {

            setLoading(false);
        }
    };





    const ramOptionsForSelectedStorage = (selectedStorage && groupedOptions[selectedStorage])
        ? Object.keys(groupedOptions[selectedStorage])
        : uniqueRams;

    const availableColorsForSelection = (selectedStorage && selectedRam && groupedOptions[selectedStorage]?.[selectedRam])
        ? groupedOptions[selectedStorage][selectedRam]
        : uniqueColors;


     const selectedColorObj = uniqueColors.find(c => c.id === selectedColor);




     if (!product) {
         return <div>Loading product details...</div>;
     }

    return (
        <div className="product-details p-4 md:p-8">
            {/* Product Name & Selected Variant Display */}
            <h1 className="text-3xl font-normal mb-2">
                {product.name}
                {(selectedStorage || selectedColorObj) && (
                    <span className="text-gray-600 text-xl ml-2">
                         ({selectedStorage || ''}
                         {selectedStorage && selectedRam ? ` / ${selectedRam}` : selectedRam || ''}
                         {selectedColorObj ? `, ${selectedColorObj.name}` : ''})
                    </span>
                )}
            </h1>
            <div className='md:flex md:items-center'>
                {product.product_model && (
                    <div className="flex items-center mb-2 mr-3">
                        <h4 className='font-bold text-[#000000c8] mr-1'>Product Model:</h4>
                        <span className='text-gray-600'>{product.product_model}</span>                         
                    </div>
                )}
                {product.sku && (
                    <div className="flex items-center mb-2">
                        <h4 className='font-bold text-[#000000c8] mr-1'>SKU:</h4>
                        <span className='text-gray-600'>{product.sku}</span>
                         
                    </div>
                )}
            </div>
            
            {/* Price Section */}
            <div className="price-section mb-6">
                <div className="flex flex-col">
                    <div className="flex items-center flex-wrap">
                        {/* Display selectedPrice if available, otherwise fallback */}
                        <span className="text-2xl font-medium mr-4">{selectedPrice || product.sale_price} AED</span>
                        {/* Display regularPrice if available */}
                        {regularPrice && (
                            <>
                                <span className="text-lg text-gray-400 line-through mr-3">{regularPrice} AED</span>
                                {/* Calculate and display discount percentage */}
                                {regularPrice && selectedPrice && (
                                    <span className="bg-red-500 text-white text-sm font-medium px-2 py-1 rounded-md">
                                        {Math.round(((regularPrice - (selectedPrice || product.sale_price)) / regularPrice) * 100)}% OFF
                                    </span>
                                )}
                            </>
                        )}
                    </div>
                    {/* VAT notice */}
                    <span className="text-xs text-gray-500 mt-1">Price includes VAT</span>
                </div>
            </div>

             {/* Color Options - Ensure availableColorsForSelection is correct */}
            {uniqueColors && uniqueColors.length > 0 && (
                 <div className="color-options mb-6 flex items-center">
                    <h3 className="text-base font-medium mr-[8px]">Select color:</h3>
                     <div className="flex flex-wrap gap-2 items-center">
                         {/* Map over colors that are actually available for the current storage/ram selection */}
                         {/* This might require filtering uniqueColors based on available combinations */}
                         {uniqueColors.map(color => (
                             <button
                                key={color.id}
                                onClick={() => setSelectedColor(color.id)}
                                className={`w-8 h-8 rounded-full border-2 transition-all duration-200 color-swatch ${
                                    selectedColor === color.id ? 'border-black ring-2 ring-offset-1 ring-black' : 'border-gray-300'
                                }`}
                                data-color={color.color || color.name.toLowerCase()}
                                aria-label={`Select ${color.name} color`}
                            />
                        ))}
                    </div>
                </div>
            )}


            {/* Storage and RAM Options */}
            <div className='flex flex-wrap justify-start gap-4 mb-6'>
                {/* Storage Options */}
                 {uniqueStorages && uniqueStorages.length > 0 && uniqueStorages[0] !== null && (
                    <div className="storage-options">
                         <h3 className="text-base font-medium mb-2">Storage:</h3>
                        <div className="flex flex-wrap gap-2">
                             {uniqueStorages.map((storage, index) => (
                                <button
                                    key={storage || `storage-${index}`}
                                    onClick={() => setSelectedStorage(storage)}
                                    className={`p-3 px-4 text-sm font-medium border rounded-lg transition-colors duration-200 ${
                                        selectedStorage === storage ? 'border-2 border-black bg-gray-100' : 'border-gray-300 hover:border-gray-500'
                                    }`}
                                >
                                     {storage || 'N/A'} {/* Display N/A or similar for null */}
                                </button>
                            ))}
                        </div>
                    </div>
                 )}

                {/* RAM Options - Conditionally render based on product.rem_section */}
                {uniqueRams && uniqueRams.length > 0 && uniqueRams[0] !== null && product.rem_section && (
                    <div className="ram-options">
                        <h3 className="text-base font-medium mb-2">RAM:</h3>
                        <div className="flex flex-wrap gap-2">
                            {/* If RAM options are few, buttons might be better than select */}
                             {uniqueRams.map((ram, index) => (
                                <button
                                    key={ram || `ram-${index}`}
                                    onClick={() => setSelectedRam(ram)}
                                    className={`p-3 px-4 text-sm font-medium border rounded-lg transition-colors duration-200 ${
                                        selectedRam === ram ? 'border-2 border-black bg-gray-100' : 'border-gray-300 hover:border-gray-500'
                                    }`}
                                >
                                    {ram || 'N/A'} {/* Display N/A or similar for null */}
                                </button>
                            ))}
                        </div>
                        {/* Or use a Select dropdown if many options */}
                         <select
                             id="ram-selector"
                             value={selectedRam}
                             onChange={(e) => setSelectedRam(e.target.value)}
                             className="p-2 text-sm font-medium border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:outline-none"
                         >
                             <option value="">Select RAM</option>
                             {uniqueRams.map((ram) => (
                                 <option key={ram || 'ram-null'} value={ram || ''}>
                                     {ram || 'N/A'}
                                 </option>
                             ))}
                         </select>
                    </div>
                )}
            </div>


            {/* Specifications Grid (Assuming product.specs is passed correctly) */}
             {product.specs && product.specs.length > 0 && (
                 <div className="specifications-grid grid grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                    {product.specs.map(spec => (
                        <div key={spec.id} className="flex items-center bg-gray-100 rounded-lg p-3">
                             {spec.image?.image && (
                                <img
                                    src={spec.image.image}
                                    alt={spec.image.name || 'Spec icon'}
                                    className="w-6 h-6 mr-2 object-contain"
                                />
                             )}
                             <div>
                                <p className="text-sm text-gray-600">{spec.image?.name || 'Specification'}</p>
                                <span className="text-sm font-medium">{spec.detail}</span>
                            </div>
                        </div>
                    ))}
                </div>
            )}


            {/* Description Section */}
             {product.description && (
                 <div
                     className="w-full mb-[24px] cursor-pointer"
                     onClick={() => needsMore && setIsExpanded(!isExpanded)}
                 >
                    <p
                        ref={descriptionRef}
                        className={`text-[14px] font-[400] text-[#6C6C6C] leading-[24px] whitespace-pre-wrap ${
                             !isExpanded && needsMore ? "line-clamp-3" : ""
                         }`}
                     >
                         {product.description}
                     </p>
                    {needsMore && (
                        <button className="text-sm text-blue-600 hover:underline mt-1">
                             {isExpanded ? "Show Less" : "Show More"}
                         </button>
                     )}
                 </div>
             )}


            {/* Action Button (Add to Cart / Go to Cart / Out of Stock) */}
            <div className="action-section mb-6">
                {!stockAvailable ? (
                    <button className="w-full py-4 bg-gray-400 text-white rounded-lg cursor-not-allowed" disabled>
                        Out of Stock
                    </button>
                ) : (isInCart || cartUpdated) ? (
                    <button className="w-full py-4 cursor-pointer bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors" onClick={() => navigate('/cart')}>
                        Go to Cart
                    </button>
                ) : (
                    <button
                        className="w-full py-4 cursor-pointer bg-black text-white rounded-lg hover:bg-gray-800 transition-colors disabled:opacity-50"
                        onClick={handleAddToCart}
                        disabled={loading || !stockAvailable}
                    >
                        {loading ? "Adding..." : "Add to Cart"}
                    </button>
                )}
                {/* Display API errors here if needed */}
                {/* {error && <p className="text-red-500 text-sm mt-2">{error}</p>} */}
            </div>

            {/* Product Info Icons (Delivery, Stock, Warranty) */}
            <div className="product-info grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
                 {/* Delivery Info */}
                 {product.delivery_title && (
                     <div className="flex items-center p-2 bg-gray-50 rounded-lg">
                        <div className="bg-gray-100 p-3 rounded-xl mr-3 flex-shrink-0">
                             <img src={deliveryIcon} alt="Delivery" className="w-6 h-6" />
                         </div>
                         <div>
                            <p className="text-[12px] md:text-sm font-medium text-gray-700">{product.delivery_title}</p>
                            <span className="text-[12px] md:text-sm">{product.delivery_duration}</span>
                        </div>
                    </div>
                 )}
                 {/* Stock Info */}
                 <div className="flex items-center p-2 bg-gray-50 rounded-lg">
                     <div className="bg-gray-100 p-3 rounded-xl mr-3 flex-shrink-0">
                         <img src={stockIcon} alt="Stock" className="w-6 h-6" />
                     </div>
                     <div>
                         <p className={`text-[12px] md:text-sm font-medium ${stockAvailable ? 'text-green-600' : 'text-red-500'}`}>
                             {stockAvailable ? 'In Stock' : 'Out of Stock'}
                         </p>
                         {stockAvailable && <span className="text-[12px] md:text-sm">Available</span>}
                     </div>
                 </div>
                 {/* Warranty Info */}
                 {product.garantee_title && (
                     <div className="flex items-center p-2 bg-gray-50 rounded-lg">
                        <div className="bg-gray-100 p-3 rounded-xl mr-3 flex-shrink-0">
                            <img src={warrantyIcon} alt="Warranty" className="w-6 h-6" />
                         </div>
                        <div>
                             <p className="text-[12px] md:text-sm font-medium text-gray-700">{product.garantee_title}</p>
                             <span className="text-[12px] md:text-sm">{product.garantee_time}</span>
                         </div>
                    </div>
                 )}
            </div>
        </div>
    );
};

export default ProductDetails;