{% extends "base/admin-base.html" %}
{% load static %}

{% block container %}
{% include 'includes/seller-nav.html' %}

<div class="content-body">
    <div class="row page-titles mx-0">
        <div class="col p-md-0">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="javascript:void(0)">{{ sub_title }}</a></li>
                <li class="breadcrumb-item active"><a href="javascript:void(0)">{{ name }}</a></li>
            </ol>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="heading-line">
                            <h4 class="card-title m-0 p-0">{{ name }} - Step 5</h4>
                            <div><span class="badge badge-info">Product: {{ product.name }}</span></div>
                        </div>

                        {# Display messages #}
                        {% if messages %}
                            <div class="mt-3">
                            {% for message in messages %}
                                <div class="alert {% if message.tags %}alert-{{ message.tags }}{% else %}alert-info{% endif %}" role="alert">{{ message }}</div>
                            {% endfor %}
                            </div>
                        {% endif %}

                        {# --- List Already Added Specs --- #}
                        <div class="mt-4">
                            <h5>Top Features Added So Far:</h5>
                            <p class="mt-2 mb-0 text-danger" style="font-size: 14px; font-weight: bold;">
                                ⚠️ Note: Only the latest 6 Top Features will be shown per product.
                            </p>
                            {% if specs_added %}
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Icon</th>
                                            <th>Detail</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    {% for spec in specs_added %}
                                        <tr>
                                            <td>{% if spec.image %}<img src="{{ spec.image.image.url }}" alt="{{ spec.image.name }}" height="25">{% else %}-{% endif %}</td>
                                            <td>{{ spec.detail|default:"-" }}</td>
                                            <td>
                                                <a href="{% url 'managers:delete_wizard_spec' product_id=product.id pk=spec.pk %}"
                                                   class="btn btn-danger btn-sm"
                                                   onclick="return confirm('Are you sure you want to delete this feature?');">
                                                   Delete
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <p>No top features added yet.</p>
                            {% endif %}
                        </div>
                        <hr>

                        {# --- Form to Add ONE New Spec --- #}
                        <h5>Add New Top Feature:</h5>
                        {# enctype might be needed if IconImage is uploaded here, check SpecForm #}
                        <form method="post" enctype="multipart/form-data" class="mt-3">
                            {% csrf_token %}
                            {# Render SpecForm fields manually or using as_p #}
                             <div class="form-group">
                                <label for="{{ form.image.id_for_label }}">Icon Image:</label>
                                {{ form.image }} {# Assuming this is a Select for IconImage #}
                                {% if form.image.errors %}<div class="text-danger small">{{ form.image.errors }}</div>{% endif %}
                            </div>
                             <div class="form-group">
                                <label for="{{ form.detail.id_for_label }}">Detail:</label>
                                {{ form.detail }}
                                {% if form.detail.errors %}<div class="text-danger small">{{ form.detail.errors }}</div>{% endif %}
                            </div>

                            <div class="form-group text-left">
                                {# Button to submit the single form #}
                                <button type="submit" name="add_spec_button" class="btn btn-success">Add This Feature</button>
                            </div>
                        </form>
                        <hr>

                        {# --- Navigation Buttons --- #}
                        <div class="form-group text-right mt-4">
                            {# Next button to go to Step 6 (Custom Specs) #}
                            <a href="{% url 'managers:seller_add_product_wizard_step6_custom_specs' product_id=product.id %}" class="btn btn-primary">Next: Add Custom Specs</a>
                            {# Skip button goes to Step 6 (Custom Specs) #}
                            <a href="{% url 'managers:seller_add_product_wizard_step6_custom_specs' product_id=product.id %}" class="btn btn-secondary ml-2">Skip to Custom Specs</a>
                        </div>

                        <img src="{% static './images/admin-img1.png' %}" alt="" style="width: 50%;"> 

                    </div> {# End of card-body #}
                </div> {# End of card #}
            </div> {# End of col-12 #}
        </div> {# End of row #}
    </div> {# End of container-fluid #}
</div> {# End of content-body #}

{% endblock %}