{% extends "base/admin-base.html" %}
{% block container %}
{% include 'includes/seller-nav.html' %}
<div class="content-body">
    <div class="row page-titles mx-0"><div class="col p-md-0"><ol class="breadcrumb"><li class="breadcrumb-item"><a href="javascript:void(0)">{{ sub_title }}</a></li><li class="breadcrumb-item active"><a href="javascript:void(0)">{{ name }}</a></li></ol></div></div>
    <div class="container-fluid"><div class="row"><div class="col-12"><div class="card"><div class="card-body">
        <div class="heading-line"><h4 class="card-title m-0 p-0">{{ name }} - Step 5</h4><div><span class="badge badge-info">Product: {{ product.name }}</span></div></div>
        {% if messages %}<div class="mt-3">{% for message in messages %}<div class="alert {% if message.tags %}alert-{{ message.tags }}{% else %}alert-info{% endif %}" role="alert">{{ message }}</div>{% endfor %}</div>{% endif %}
        <div class="mt-4"><h5>Top Features Added:</h5><p class="text-danger small">Note: Only the latest 6 Top Features will be shown on the product page.</p>{% if specs_added %}<div class="table-responsive"><table class="table table-sm table-bordered"><thead><tr><th>Icon</th><th>Detail</th><th>Action</th></tr></thead><tbody>{% for spec in specs_added %}<tr><td>{% if spec.image %}<img src="{{ spec.image.image.url }}" alt="{{ spec.image.name }}" height="25">{% endif %}</td><td>{{ spec.detail|default:"-" }}</td><td><a href="{% url 'managers:delete_wizard_spec' product_id=product.id pk=spec.pk %}" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure?');">Delete</a></td></tr>{% endfor %}</tbody></table></div>{% else %}<p>No top features added yet.</p>{% endif %}</div><hr>
        <h5>Add / Update Top Features:</h5>
        <form method="post" class="mt-3">
            {% csrf_token %}
            <div id="spec-rows-container">
                <div class="form-row align-items-center mb-2 spec-row">
                    <div class="form-group col-md-5"><label>Icon Image</label><select name="image" class="form-control" required><option value="">---------</option>{% for icon in available_icons %}<option value="{{ icon.id }}">{{ icon.name }}</option>{% endfor %}</select></div>
                    <div class="form-group col-md-5"><label>Detail</label><input type="text" name="detail" class="form-control" placeholder="e.g., 120Hz Refresh Rate" required></div>
                    <div class="form-group col-md-2 d-flex align-items-end"><button type="button" class="btn btn-danger btn-sm remove-spec-row">Remove</button></div>
                </div>
            </div>
            <button type="button" id="add-spec-row" class="btn btn-info mt-2"><i class="fa fa-plus"></i> Add Another Feature</button>
            <hr>
            <div class="d-flex justify-content-between mt-4">
                <button type="submit" name="save_all_features" value="1" class="btn btn-success">Save All Features</button>
                <a href="{% url 'managers:seller_add_product_wizard_step6_custom_specs' product_id=product.id %}" class="btn btn-primary">Continue to Custom Specs</a>
            </div>
        </form>
    </div></div></div></div></div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    function cloneRow(containerId, rowClass) {
        const container = document.getElementById(containerId);
        const firstRow = container.querySelector(rowClass);
        if (!firstRow) return;
        const newRow = firstRow.cloneNode(true);
        newRow.querySelectorAll('input, select').forEach(input => {
            if (input.type !== 'checkbox' && input.type !== 'radio') input.value = '';
            if (input.tagName === 'SELECT') input.selectedIndex = 0;
        });
        container.appendChild(newRow);
    }
    document.getElementById('add-spec-row').addEventListener('click', () => cloneRow('spec-rows-container', '.spec-row'));
    document.getElementById('spec-rows-container').addEventListener('click', function(e) {
        if (e.target && e.target.classList.contains('remove-spec-row')) {
            if (document.querySelectorAll('.spec-row').length > 1) {
                e.target.closest('.spec-row').remove();
            } else {
                alert("You can't remove the last row.");
            }
        }
    });
});
</script>
{% endblock %}