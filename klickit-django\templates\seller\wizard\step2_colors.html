{% extends "base/admin-base.html" %}
{% load static %}

{% block container %}
{% include 'includes/seller-nav.html' %}

<style>
.wizard-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.wizard-progress {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    position: relative;
}

.wizard-progress::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 0;
    right: 0;
    height: 2px;
    background: #e9ecef;
    z-index: 1;
}

.wizard-progress::after {
    content: '';
    position: absolute;
    top: 20px;
    left: 0;
    width: 25%; /* 1 out of 4 steps completed */
    height: 2px;
    background: #28a745;
    z-index: 2;
}

.wizard-step {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    position: relative;
    z-index: 3;
}

.wizard-step.completed {
    background: #28a745;
    border-color: #28a745;
    color: white;
}

.wizard-step.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
}

.wizard-step-label {
    position: absolute;
    top: 50px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    white-space: nowrap;
}

.color-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.color-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    background: white;
    text-align: center;
    position: relative;
}

.color-preview {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin: 0 auto 10px;
    border: 3px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.color-name {
    font-weight: 600;
    color: #495057;
    margin-bottom: 10px;
}

.btn-remove-color {
    background: #dc3545;
    border: none;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
}

.add-color-form {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
    display: block;
}

.form-control {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    width: 100%;
}

.btn-primary {
    background: #007bff;
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
}

.btn-outline-secondary {
    background: transparent;
    border: 1px solid #6c757d;
    color: #6c757d;
    padding: 10px 20px;
    border-radius: 4px;
    text-decoration: none;
    display: inline-block;
}

.wizard-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
}

.help-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}
</style>

<div class="content-body">
    <div class="wizard-container">
        <!-- Progress Indicator -->
        <div class="wizard-progress">
            <div class="wizard-step completed">
                <span>1</span>
                <div class="wizard-step-label">Product Details</div>
            </div>
            <div class="wizard-step active">
                <span>2</span>
                <div class="wizard-step-label">Colors</div>
            </div>
            <div class="wizard-step">
                <span>3</span>
                <div class="wizard-step-label">Variants</div>
            </div>
            <div class="wizard-step">
                <span>4</span>
                <div class="wizard-step-label">Images</div>
            </div>
            <div class="wizard-step">
                <span>5</span>
                <div class="wizard-step-label">Specifications</div>
            </div>
        </div>

        <!-- Page Header -->
        <div class="text-center mb-4">
            <h2 class="mb-2">Add Product Colors</h2>
            <p class="text-muted">Create color options that will be available for your product variants</p>
            <div class="alert alert-info">
                <strong>Product:</strong> {{ product.name }}
            </div>
        </div>

        <!-- Messages -->
        {% if messages %}
            <div class="mb-4">
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <!-- Existing Colors -->
        {% if colors_added %}
            <div class="mb-4">
                <h4 class="mb-3">
                    <i class="fas fa-palette text-primary"></i>
                    Your Colors ({{ colors_added.count }})
                </h4>

                <div class="color-grid">
                    {% for color in colors_added %}
                        <div class="color-card">
                            <div class="color-preview" style="background-color: {{ color.color }};"></div>
                            <div class="color-name">{{ color.name }}</div>
                            <a href="{% url 'managers:delete_wizard_color' product_id=product.id pk=color.pk %}"
                               class="btn-remove-color"
                               onclick="return confirm('Are you sure you want to delete this color? This might affect other products using it.');">
                                <i class="fas fa-trash"></i> Remove
                            </a>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}

        <!-- Add New Color Form -->
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-plus-circle text-success"></i>
                    Add New Color
                </h4>
                <small class="text-muted">Create color options for your product variants</small>
            </div>
            <div class="card-body">
                {% if not colors_added %}
                    <div class="alert-info mb-3">
                        <strong><i class="fas fa-info-circle"></i> Getting Started:</strong>
                        You haven't created any colors yet. Add your first color below to get started with product variants.
                    </div>
                {% endif %}

                <form method="post" id="color-form">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="id_name">Color Name *</label>
                                {{ form.name }}
                                <div class="help-text">Enter a descriptive name for this color (e.g., "Midnight Black", "Ocean Blue")</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="id_color">Color Code *</label>
                                {{ form.color }}
                                <div class="help-text">Choose the exact color or enter a hex code</div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center">
                        <button type="submit" name="add_color_button" class="btn-primary">
                            <i class="fas fa-plus"></i> Add This Color
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="wizard-actions">
            <a href="{% url 'managers:seller_add_product_wizard_step1_details' %}" class="btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Previous: Product Details
            </a>

            <div>
                <a href="{% url 'managers:seller_add_product_wizard_step3_variants' product_id=product.id %}" class="btn-outline-secondary mr-3">
                    Skip Colors
                </a>
                <a href="{% url 'managers:seller_add_product_wizard_step3_variants' product_id=product.id %}" class="btn-primary">
                    Next: Add Variants <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    document.getElementById('color-form').addEventListener('submit', function(e) {
        const nameField = document.getElementById('id_name');
        const colorField = document.getElementById('id_color');

        if (!nameField.value.trim()) {
            e.preventDefault();
            nameField.style.borderColor = '#dc3545';
            alert('Please enter a color name.');
            return;
        }

        if (!colorField.value.trim()) {
            e.preventDefault();
            colorField.style.borderColor = '#dc3545';
            alert('Please select a color.');
            return;
        }
    });

    // Real-time validation feedback
    const inputs = document.querySelectorAll('input, select');
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            if (this.style.borderColor === 'rgb(220, 53, 69)' && this.value.trim()) {
                this.style.borderColor = '#ced4da';
            }
        });
    });
});
</script>

{% endblock %}