import React, { useState, useEffect } from "react";
import logo from "../../assets/images/logooo.png";
import searchIcon from "../../assets/images/search.png";
import serviceIcon from "../../assets/images/service-icon.png";
import menuIcon from "../../assets/images/menu.png";
import userIcon from "../../assets/images/user.png";
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import '../../index.css';
import { BASE_URL } from "../../utils/config";

const Header = ({ user }) => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [profileMenuOpen, setProfileMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [suggestions, setSuggestions] = useState([]);
  const [searchBarVisible, setSearchBarVisible] = useState(true);
  const navigate = useNavigate();
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [cartCount, setCartCount] = useState(0);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [userData, setUserData] = useState(null);

  // Initialize Axios headers and fetch cart count on app load
  useEffect(() => {
    const token = localStorage.getItem("access_token");
    if (token) {
      axios.defaults.headers.common["Authorization"] = `Bearer ${token}`;
      setIsLoggedIn(true);
      fetchCartCount(); // Fetch cart count when the component mounts
      fetchUserData(); // Fetch user data when the component mounts
    }
  }, []);

  // Fetch cart count whenever the login state changes
  useEffect(() => {
    if (isLoggedIn) {
      fetchCartCount();
    } else {
      setCartCount(0); // Reset cart count if user logs out
    }
  }, [isLoggedIn]);

  // Function to fetch cart count from the backend
  const fetchCartCount = async () => {
    try {
      const response = await axios.get(`${BASE_URL}/`);
      setCartCount(response.data.count || 0);
    } catch (error) {
      console.error("Error fetching cart count:", error);
    }
  };

  // Function to fetch user data from the backend
  const fetchUserData = async () => {
    try {
      const response = await axios.get(`${BASE_URL}/account/`);
      setUserData(response.data.user);
    } catch (error) {
      console.error("Error fetching user data:", error);
    }
  };

  const handleSearch = async (query) => {
    setSearchQuery(query);
    if (!query.trim() || query.length < 3) {
      setSuggestions([]);
      return;
    }
    
    try {
      const response = await axios.get(`${BASE_URL}/api/search-suggestions/?q=${encodeURIComponent(query)}`);
      setSuggestions(response.data || []);
    } catch (error) {
      console.error("Error fetching suggestions:", error);
      setSuggestions([]);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault(); // Prevent the default form submission behavior
    if (searchQuery.trim()) {
      setSuggestions([]); // Clear suggestions
      navigate(`/search?q=${encodeURIComponent(searchQuery)}`); // Navigate to the search results page
    }
  };

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      if (currentScrollY > lastScrollY) {
        setSearchBarVisible(false); // Hide on scroll down
      } else {
        setSearchBarVisible(true); // Show on scroll up
      }

      setLastScrollY(currentScrollY);
      setProfileMenuOpen(false); // Close menu on scroll
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [lastScrollY]);

  const handleLogout = () => {
    localStorage.removeItem("access_token");
    localStorage.removeItem("refresh_token");
    window.location.href = "/login";
  };

  return (
    <header className="fixed top-0 left-0 w-full shadow-md bg-white z-50">
      {/* Desktop Header */}
      <section className="wrapper flex justify-between items-center h-[80px]">
        {/* Logo */}
        <h1>
          <a onClick={() => navigate(`/`)} className="cursor-pointer">
            <img src={logo} alt="logo" className="w-[120px] md:w-[150px] h-auto" />
          </a>
        </h1>

        {/* Desktop Search Bar */}
        <form className="w-[30%] hidden md:block relative" onSubmit={handleSubmit}>
          <div className="flex justify-between items-center border hover:border-purple-500 rounded-full py-2 px-5">
            <input
              type="text"
              className="outline-none w-full placeholder:text-[13px] font-thin"
              placeholder="What are you looking for?"
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              required
            />
            <button type="submit" className="w-[16px]">
              <img src={searchIcon} alt="search" className="w-[16px]" />
            </button>
          </div>
          {suggestions.length > 0 && (
            <ul className="absolute left-0 right-0 bg-white border rounded-md shadow-lg mt-1 z-50 max-h-60 overflow-y-auto">
              {suggestions.map((suggestion, index) => (
                <li
                  key={index}
                  className="p-3 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0"
                  onClick={() => {
                    setSearchQuery(suggestion);
                    setSuggestions([]);
                    navigate(`/search?q=${encodeURIComponent(suggestion)}`);
                  }}
                >
                  <div className="flex items-center">
                    <svg className="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    {suggestion}
                  </div>
                </li>
              ))}
            </ul>
          )}
        </form>

        {/* Header Icons */}
        <div>
          <ul className="flex justify-end items-center">
            {/* Service */}
            <li className="mr-1 md:mr-7 hidden md:flex">
              <a
                onClick={() => navigate(`/service`)}
                className="text-[16px] cursor-pointer font-semibold flex items-center hover:text-purple-500"
              >
                <img
                  className="h-[35px] md:h-[16px] mr-[5px]"
                  src={serviceIcon}
                  alt="service"
                />
                <p className="hidden md:block">Service</p>
              </a>
            </li>

            {/* Wishlist */}
            <li className="mr-1 md:mr-7">
              <a onClick={() => navigate(`/wishlist`)} className="text-[16px] cursor-pointer font-semibold flex items-center hover:text-purple-500">
                <i className="text-[35px] md:text-[16px] bx bx-heart"></i>
                <p className="hidden md:block">Wishlist</p>
              </a>
            </li>

            {/* Cart */}
            <li className="mr-7 px-3 border-l border-r flex items-center justify-center">
              <a onClick={() => navigate(`/cart`)} className="relative cursor-pointer hover:text-purple-500">
                <i className="text-[35px] bx bx-cart"></i>
                <span className="absolute w-[16px] h-[16px] bg-gray-600 rounded-full text-white text-[9px] flex items-center justify-center top-[9px] right-0 transform translate-x-[50%] translate-y-[-50%]">
                  {cartCount}
                </span>
              </a>
            </li>

            {/* Profile Menu */}
            <li
              className="mr-7 hidden md:block cursor-pointer"
              onClick={() => setProfileMenuOpen(!profileMenuOpen)}
            >
              <p className="text-[16px] font-semibold flex items-center hover:text-purple-500">
                <i className="font-bold mr-[5px] bx bx-user"></i>
                <span>{userData?.first_name || user?.firstName || "Account"}</span>
              </p>
              {profileMenuOpen && (
                <div className="absolute z-50 top-[80px] bg-gray-100 right-[5%] border shadow rounded-b-xl p-5 w-[240px]">
                  <p className="font-semibold border-b cursor-default pb-2">
                    My Account
                  </p>
                  <p className="text-[16px] py-2 hover:text-purple-500">
                    <a onClick={() => navigate(`/account`)} className="cursor-pointer">Manage Profile</a>
                  </p>
                  <p className="text-[16px] py-2 hover:text-purple-500">
                    <a onClick={() => navigate(`/orders`)} className="cursor-pointer">View Orders</a>
                  </p>
                  <p className="text-[16px] py-2 hover:text-purple-500 border-b">
                    <a onClick={() => navigate(`/wishlist`)} className="cursor-pointer">Manage Wishlist</a>
                  </p>
                  <p className="text-[16px] py-2 hover:text-purple-500 border-b">
                    <a onClick={() => navigate(`/become-seller`)} className="cursor-pointer">Become a Seller</a>
                  </p>
                  {isLoggedIn ? (
                    <p className="text-[16px] py-2 hover:text-purple-500 border-b">
                      <a onClick={handleLogout} className="cursor-pointer">Logout</a>
                    </p>
                  ) : (
                    <p className="text-[16px] py-2 hover:text-purple-500">
                      <a onClick={() => navigate(`/login`)} className="cursor-pointer">Login</a>
                    </p>
                  )}
                </div>
              )}
            </li>

            {/* Mobile Menu Button */}
            <li className="md:hidden">
              <img
                src={menuIcon}
                alt="menu"
                className="w-[28px] cursor-pointer"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              />
            </li>
          </ul>
        </div>
      </section>

      {/* Mobile Search Bar */}
      {searchBarVisible && (
        <section className="wrapper pb-4 md:hidden relative">
          <form onSubmit={handleSubmit}>
            <div className="flex justify-between items-center border rounded-full py-2 px-5">
              <input
                type="text"
                className="outline-none w-full placeholder:text-[13px] font-thin"
                placeholder="What are you looking for?"
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
              />
              <button type="submit">
                <img src={searchIcon} alt="search" className="w-[16px]" />
              </button>
            </div>
            {suggestions.length > 0 && (
              <ul className="absolute left-5 right-5 bg-white border rounded-md shadow-lg mt-1 z-50 max-h-60 overflow-y-auto">
                {suggestions.map((suggestion, index) => (
                  <li
                    key={index}
                    className="p-3 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0"
                    onClick={() => {
                      setSearchQuery(suggestion);
                      setSuggestions([]);
                      navigate(`/search?q=${encodeURIComponent(suggestion)}`);
                    }}
                  >
                    <div className="flex items-center">
                      <svg className="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                      {suggestion}
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </form>
        </section>
      )}

      {/* Mobile Menu */}
        {mobileMenuOpen && (
        <div className="fixed top-0 left-0 h-full z-50 w-[70%] bg-gray-200">
            {/* Header with Close Button */}
            <div className="h-[100px] bg-slate-600 p-4 flex justify-between items-center text-white">
            <div className="flex items-center">
                <img src={userIcon} alt="user" className="w-[30px]" />
                <div className="ml-3 hover:text-purple-500">
                <p className="text-[14px] font-semibold">
                    {userData?.first_name || user?.firstName || "User"} {userData?.last_name || user?.lastName || ""}
                </p>
                <a
                    onClick={() => navigate(`/account`)}
                    className="text-[14px] font-normal hover:text-purple-500 cursor-pointer"
                >
                    My account
                </a>
                </div>
            </div>

            {/* Close Button */}
            <button
                onClick={() => setMobileMenuOpen(false)}
                className="text-white text-xl font-bold"
            >
                ✖
            </button>
            </div>

            {/* Menu Items */}
            <div className="p-5">
            <p className="pb-3 mb-3 border-b border-gray-300">
                <a
                onClick={() => navigate(`/service`)}
                className="text-[16px] font-semibold hover:text-purple-500 cursor-pointer"
                >
                Service
                </a>
            </p>
            <p className="pb-3 mb-3 border-b border-gray-300">
                <a
                onClick={() => navigate(`/cart`)}
                className="text-[16px] font-semibold hover:text-purple-500 cursor-pointer"
                >
                Cart
                </a>
            </p>
            <p className="pb-3 mb-3 border-b border-gray-300">
                <a
                onClick={() => navigate(`/orders`)}
                className="text-[16px] font-semibold hover:text-purple-500 cursor-pointer"
                >
                View Orders
                </a>
            </p>
            <p className="pb-3 mb-3 border-b border-gray-300">
                <a
                onClick={() => navigate(`/wishlist`)}
                className="text-[16px] font-semibold hover:text-purple-500 cursor-pointer"
                >
                Wishlist
                </a>
            </p>
            <p className="pb-3 mb-3 border-b border-gray-300">
                <a
                onClick={() => navigate(`/become-seller`)}
                className="text-[16px] font-semibold hover:text-purple-500 cursor-pointer"
                >
                Become a Seller
                </a>
            </p>
            {isLoggedIn ? (
                <p className="pb-3 mb-3 border-b border-gray-300">
                <a
                    onClick={handleLogout}
                    className="text-[16px] font-semibold hover:text-purple-500 cursor-pointer"
                >
                    Logout
                </a>
                </p>
            ) : (
                <p className="pb-3 mb-3 border-b border-gray-300">
                <a
                    onClick={() => navigate(`/login`)}
                    className="text-[16px] font-semibold hover:text-purple-500 cursor-pointer"
                >
                    Login
                </a>
                </p>
            )}
            </div>
        </div>
        )}
      {/* Mobile Search Bar */}
        {/* {searchBarVisible && (
        <section className="wrapper pb-4 md:hidden">
            <form id="search-bar hkefiqehf">
            <div className="flex justify-between items-center border rounded-full py-2 px-5">
                <input
                type="text"
                id="mobile-search-input"
                className="outline-none w-full placeholder:text-[13px] font-thin"
                placeholder="What are you looking for?"
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                />
                <button type="submit">
                <img src={searchIcon} alt="search" className="w-[16px]" />
                </button>
            </div>
            {suggestions.length > 0 && (
                <ul className="absolute left-0 right-0 bg-white border rounded-md shadow-md mt-1 z-50">
                {suggestions.map((suggestion, index) => (
                    <li key={index} className="p-2 hover:bg-gray-100 cursor-pointer">
                    {suggestion}
                    </li>
                ))}
                </ul>
            )}
            </form>
        </section>
        )} */}

    </header>
  );
};

export default Header;