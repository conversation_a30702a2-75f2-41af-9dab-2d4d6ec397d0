from django.contrib import admin
from .models import (
    Category,
    Brand,
    Color,
    Attribute,
    Product,
    ProductVariant,
    ProductAttributeValue,
    ProductImage,
    CustomSpecification,
    IconImage,
    Spec
)


class ProductImageInline(admin.TabularInline):
    model = ProductImage
    extra = 1  


class ProductAttributeValueInline(admin.TabularInline):
    model = ProductAttributeValue
    extra = 1


class CustomSpecificationInline(admin.TabularInline):
    model = CustomSpecification
    extra = 1


class SpecInline(admin.TabularInline):
    model = Spec
    extra = 1

@admin.register(ProductVariant)
class ProductVariantAdmin(admin.ModelAdmin):
    
    inlines = [ProductAttributeValueInline, ProductImageInline, CustomSpecificationInline, SpecInline]
    list_display = ('name', 'product', 'color', 'regular_price', 'sale_price', 'stock')
    list_filter = ('product', 'color')
    search_fields = ('name', 'sku', 'product__name')
    list_per_page = 20


class ProductVariantInline(admin.TabularInline):
    model = ProductVariant
    extra = 1
    show_change_link = True 

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    inlines = [ProductVariantInline]
    list_display = ('name', 'brand', 'category', 'seller')
    list_filter = ('brand', 'category', 'seller')
    search_fields = ('name',)

@admin.register(Color)
class ColorAdmin(admin.ModelAdmin):
    list_display = ('name', 'color', 'seller') 
    list_filter = ('seller',) 
    search_fields = ('name', 'seller__user__username')


admin.site.register(Category)
admin.site.register(Brand)
admin.site.register(Attribute)
admin.site.register(IconImage)



