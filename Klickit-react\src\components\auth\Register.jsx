import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import LoadingSpinner from '../includes/LoadingSpinner';
import '../../index.css';
import { BASE_URL } from "../../utils/config";
import logo from "../../assets/images/logooo.png";

const Register = ({ setIsLoggedIn }) => {
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    phone_number: '',
    password: '',
    confirm_password: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleRegister = async (e) => {
    e.preventDefault();

    if (formData.password !== formData.confirm_password) {
      setError("Passwords don't match.");
      return;
    }

    setLoading(true);
    try {
      // Create a copy of formData and remove phone_number if it's empty
      const payload = { ...formData };
      if (!payload.phone_number) {
        delete payload.phone_number;
      }

      await axios.post(`${BASE_URL}/register/`, payload);
      navigate('/verify-otp', { 
        state: { 
          email: formData.email,
          phone_number: formData.phone_number || null, // Ensure consistency
          password: formData.password
        }
      });
    } catch (error) {
      setError(error.response?.data?.error || 'Registration failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  if (loading) return <LoadingSpinner />;

  return (
    <section className="min-h-screen flex justify-center items-center bg-gray-200 px-4 py-12">
      <div className="bg-white shadow-md rounded-lg w-full max-w-md p-8 border border-gray-200 my-6 max-h-[90vh] overflow-y-auto">
        <div className="text-center mb-6">
          <img src={logo} alt="neumoon Logo" className="mx-auto w-16 mb-4" />
          <h2 className="text-2xl font-bold text-gray-800">neumoon</h2>
          <p className="text-sm text-gray-500 mt-1">Create your account</p>
        </div>
        
        <form onSubmit={handleRegister} className="space-y-4">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-2 rounded text-center text-sm">
              {error}
            </div>
          )}
          
          <div>
            <label htmlFor="first_name" className="block text-sm font-medium text-gray-700 mb-1">
              First Name
            </label>
            <input 
              type="text" 
              id="first_name"
              name="first_name" 
              placeholder="Your first name" 
              value={formData.first_name} 
              onChange={handleInputChange} 
              required 
              className="w-full border border-gray-300 rounded-lg py-3 px-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
            />
          </div>
          
          <div>
            <label htmlFor="last_name" className="block text-sm font-medium text-gray-700 mb-1">
              Last Name
            </label>
            <input 
              type="text" 
              id="last_name"
              name="last_name" 
              placeholder="Your last name" 
              value={formData.last_name} 
              onChange={handleInputChange} 
              required 
              className="w-full border border-gray-300 rounded-lg py-3 px-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
            />
          </div>
          
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <input 
              type="email" 
              id="email"
              name="email" 
              placeholder="Your email address" 
              value={formData.email} 
              onChange={handleInputChange} 
              required 
              className="w-full border border-gray-300 rounded-lg py-3 px-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
            />
          </div>
          
          <div>
            <label htmlFor="phone_number" className="block text-sm font-medium text-gray-700 mb-1">
              Phone Number 
              {/* <span className="text-gray-400 font-normal">(optional)</span> */}
            </label>
            <input 
              type="text" 
              id="phone_number"
              name="phone_number" 
              placeholder="Your phone number" 
              value={formData.phone_number} 
              onChange={handleInputChange} 
              className="w-full border border-gray-300 rounded-lg py-3 px-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
            />
          </div>
          
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              Password
            </label>
            <input 
              type="password" 
              id="password"
              name="password" 
              placeholder="Create a password" 
              value={formData.password} 
              onChange={handleInputChange} 
              required 
              className="w-full border border-gray-300 rounded-lg py-3 px-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
            />
          </div>
          
          <div>
            <label htmlFor="confirm_password" className="block text-sm font-medium text-gray-700 mb-1">
              Confirm Password
            </label>
            <input 
              type="password" 
              id="confirm_password"
              name="confirm_password" 
              placeholder="Confirm your password" 
              value={formData.confirm_password} 
              onChange={handleInputChange} 
              required 
              className="w-full border border-gray-300 rounded-lg py-3 px-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
            />
          </div>
          
          <div className="pt-2">
            <button 
              type="submit" 
              className="w-full py-3 px-4 text-white bg-blue-600 hover:bg-blue-700 rounded-lg font-medium transition-colors duration-200"
            >
              SIGN UP
            </button>
          </div>
        </form>
        
        <div className="text-center mt-6">
          <p className="text-sm text-gray-600">
            Already have an account?{" "}
            <button 
              onClick={() => navigate("/login")} 
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              Login
            </button>
          </p>
        </div>
      </div>
    </section>
  );
};

export default Register;