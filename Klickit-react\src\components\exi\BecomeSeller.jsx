import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import Header2 from "../includes/Header2"; // Assuming path is correct
import Footer from "../includes/Footer";   // Assuming path is correct
import LoadingSpinner from "../includes/LoadingSpinner"; // Assuming path is correct
import { BASE_URL } from "../../utils/config"; // Assuming path is correct

const BecomeSeller = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [apiMessage, setApiMessage] = useState(null);

  const handleExpressInterest = async () => {
    setIsLoading(true);
    setError(null);
    setApiMessage(null);

    try {
      const token = localStorage.getItem("access_token");
      if (!token) {
        // Redirect to login if user is not authenticated
        navigate("/login");
        return; // Stop execution
      }

      // Call the backend endpoint to log interest
      const response = await axios.post(
        `${BASE_URL}/seller-interest/`, // Ensure BASE_URL and path are correct
        {}, // Sending empty body
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      // Set success message (may flash briefly before mail redirect)
      // setApiMessage(response.data.message || "Interest expressed successfully!");

      // ** ACTION REQUIRED: Verify Email and Brand Name **
      const targetEmail = "<EMAIL>"; // <-- UPDATE if needed
      const brandName = "EcoReact";               // <-- UPDATE if needed (e.g., "NEUMOON")

      // Construct mailto URL with encoded parameters
      const emailSubject = encodeURIComponent(`Interest in Becoming a Seller on ${brandName}`);
      const emailBody = encodeURIComponent(
        `Hello ${brandName} Support,\n\n` +
        "I clicked the 'Become a Seller' button on the website and would like to express my interest. " +
        "Please provide me with more information.\n\n" +
        "Thank you,"
      );
      const mailtoUrl = `mailto:${targetEmail}?subject=${emailSubject}&body=${emailBody}`;

      // Attempt to trigger the user's default email client
      window.location.href = mailtoUrl;

    } catch (err) {
      // Log the error and set state to display feedback
      console.error("Error expressing interest:", err); // Keep error logging
      if (err.response) {
        setError(err.response?.data?.detail || err.response?.data?.error || `API Error: ${err.response.status}.`);
      } else if (err.request) {
        setError("No response from server. Please check your connection.");
      } else {
        setError(`Error: ${err.message}`);
      }
    } finally {
      // Ensure loading state is turned off
      setIsLoading(false);
    }
  };

  // Display loading spinner while processing
  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <>
      <Header2 />
      <div className="min-h-screen bg-gray-50 py-12 mt-[70px] px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto">
          <div className="bg-white shadow-lg rounded-lg p-8">
            {/* ** ACTION REQUIRED: Verify Brand Name ** */}
            <h2 className="text-3xl font-bold text-gray-900 mb-6 text-center">
              Become a Seller on NEUMOON
            </h2>

            <div className="space-y-6">
              <p className="text-gray-600 text-lg">
                 {/* ** ACTION REQUIRED: Verify Brand Name ** */}
                Join our growing community of sellers and start your journey with Neumoon.
                We provide a platform for sustainable and eco-friendly products.
              </p>

              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-blue-900 mb-2">Benefits of Selling with Us</h3>
                <ul className="list-disc list-inside space-y-2 text-blue-800">
                  <li>Access to a large customer base</li>
                  <li>Easy-to-use seller dashboard</li>
                  <li>Secure payment processing</li>
                  <li>Marketing support</li>
                  <li>Dedicated seller support team</li>
                </ul>
              </div>

              {/* Display Error Message if API call failed */}
              {error && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                  <strong className="font-bold">Error: </strong>
                  <span className="block sm:inline">{error}</span>
                </div>
              )}

              {/* Display Success Message (may only be visible if mailto redirect fails) */}
              {apiMessage && !error && (
                 <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                  <span className="block sm:inline">{apiMessage}</span>
                </div>
              )}

              {/* Button to trigger the process */}
              <button
                onClick={handleExpressInterest}
                disabled={isLoading}
                className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {/* Button text matches screenshot */}
                {isLoading ? "Processing..." : "Contact Us"}
              </button>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default BecomeSeller;