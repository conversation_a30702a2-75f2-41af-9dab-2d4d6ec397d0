# Generated by Django 5.2 on 2025-07-15 11:02

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Manager',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('updated_datetime', models.DateTimeField(auto_now=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_datetime', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'manager',
                'verbose_name_plural': 'managers',
                'db_table': 'managers_manager',
                'ordering': ('id',),
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_datetime', models.DateTime<PERSON>ield(auto_now_add=True, null=True)),
                ('updated_datetime', models.DateTime<PERSON>ield(auto_now=True, null=True)),
                ('is_active', models.<PERSON>olean<PERSON>ield(default=True)),
                ('title', models.CharField(max_length=256)),
                ('description', models.TextField()),
            ],
            options={
                'verbose_name': 'notification',
                'verbose_name_plural': 'notifications',
                'db_table': 'notification_table',
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='Seller',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('updated_datetime', models.DateTimeField(auto_now=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_datetime', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'seller',
                'verbose_name_plural': 'sellers',
                'db_table': 'sellers_seller',
                'ordering': ('id',),
            },
        ),
    ]
