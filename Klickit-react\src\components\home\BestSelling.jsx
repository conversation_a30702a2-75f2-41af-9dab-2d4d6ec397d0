import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import '../../index.css';
import LoadingSpinner from "../includes/LoadingSpinner"; // Ensure this exists
import AttractiveSectionLoader from "../includes/AttractiveSectionLoader";
import { BASE_URL } from "../../utils/config";

const BestSelling = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchBestSelling = async () => {
      try {
        const response = await fetch(`${BASE_URL}/`);
        if (!response.ok) {
          throw new Error("Failed to fetch best-selling products");
        }
        const data = await response.json();
        setProducts(data.best_selling_products || []);
        setLoading(false);
      } catch (err) {
        setError(err.message);
        setLoading(false);
      }
    };

    fetchBestSelling();
  }, []);

  if (loading) return <AttractiveSectionLoader />;

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <section className="py-6 md:py-14">
      <div className="wrapper">
        <h3 className="mb-1 md:mb-3 text-[18px] md:text-[24px] font-semibold text-gray-700">
          Best Selling
        </h3>
        <div className="relative flex items-center">
          <button
            onClick={() => document.getElementById("sliderone").scrollBy({ left: -200, behavior: "smooth" })}
            className="absolute left-[-10px] z-10 bg-gray-200 p-1 md:p-2 rounded-full shadow-lg hover:bg-gray-300 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 md:h-6 w-4 md:w-6 text-gray-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <div id="sliderone" className="flex gap-5 overflow-x-auto scrollbar-hide pb-2 snap-x snap-mandatory">
            {products.length > 0 ? (
              products.map((product) => (
                <div
                  className="min-w-[150px] md:min-w-[200px] loalo px-[2px] max-w-[150px] md:max-w-[200px] shadow-lg rounded-xl relative snap-center"
                  key={product.id}
                >
                  <a onClick={() => navigate(`/product/${product.id}`)} className="cursor-pointer block">
                    <img
                      src={product.mainImage && product.mainImage.startsWith('http')
                        ? product.mainImage
                        : product.mainImage && product.mainImage.startsWith('/media')
                          ? `${BASE_URL}${product.mainImage}`
                          : '/placeholder-product.jpg'}
                      alt={product.name}
                      className="responsive-image"
                      onError={(e) => {
                        e.target.src = "/placeholder-product.jpg";
                        e.target.classList.add("object-contain", "p-4");
                      }}
                    />
                  </a>

                  <div className="p-3">
                    <h3 className="text-[15px] font-normal mb-1 product-name-truncate">
                      <a onClick={() => navigate(`/product/${product.id}`)} className="cursor-pointer hover:text-blue-600" title={product.name}>
                        {product.name}
                      </a>
                    </h3>
                    <div className="flex items-center gap-2">
                      {product.regularPrice && (
                        <span className="text-[10px] line-through text-gray-500">{product.regularPrice}AED</span>
                      )}
                      <span className="text-[14px] font-semibold">{product.salePrice}AED</span>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-gray-500 text-sm">No best-selling products available</p>
            )}
          </div>

          <button
            onClick={() => document.getElementById("sliderone").scrollBy({ left: 200, behavior: "smooth" })}
            className="absolute right-[-10px] z-10 bg-gray-200 p-1 md:p-2 rounded-full shadow-lg hover:bg-gray-300 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 md:h-6 w-4 md:w-6 text-gray-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>
    </section>
  );
};

export default BestSelling;
