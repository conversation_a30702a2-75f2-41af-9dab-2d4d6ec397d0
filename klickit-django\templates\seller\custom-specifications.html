{% extends "base/admin-base.html" %}
{% block container %}
{% load static %}

{% include 'includes/seller-nav.html' %}

<!--**********************************
    Content body start
***********************************-->
<div class="content-body">
    <div class="row page-titles mx-0">
        <div class="col p-md-0">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="javascript:void(0)">{{sub_title}}</a></li>
                <li class="breadcrumb-item active"><a href="javascript:void(0)">{{name}}</a></li>
            </ol>
        </div>
    </div>
    <!-- row -->

    <div class="container-fluid">
        <!-- Custom Specifications Section -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">


                        {# Display messages #}
                        {% if messages %}
                            <div class="mt-3">
                            {% for message in messages %}
                                <div class="alert {% if message.tags %}alert-{{ message.tags }}{% else %}alert-info{% endif %}" role="alert">{{ message }}</div>
                            {% endfor %}
                            </div>
                        {% endif %}

                        {# --- Form to Add New Custom Specification --- #}
                        

                        <div class="heading-line">
                            <h4 class="card-title m-0 p-0">{{name}}</h4>
                            <div>
                                <a href="{% url 'seller:custom_specifications_add' %}" id="custom-btn">ADD NEW</a>
                            </div>
                        </div>

                        {# --- List Existing Custom Specifications --- #}
                        <div class="mt-4">
                            <h5>Your Custom Specifications:</h5>
                            {% if instances %}
                            <div class="table-responsive">
                                <table class="table table-bordered zero-configuration" style="border-radius: 14px;">
                                    <thead>
                                        <tr>
                                            <th>PRODUCT</th>
                                            <th>KEY</th>
                                            <th>VALUE</th>
                                            <th>EDIT</th>
                                            <th>DELETE</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for instance in instances %}
                                        <tr>
                                            <td>{{ instance.product.name|default:"-" }}</td>
                                            <td>{{ instance.key }}</td>
                                            <td>{{ instance.value|truncatechars:50 }}</td>
                                            <td>
                                                <a href="{% url 'seller:custom_specifications_edit' id=instance.id %}" id="custom-btn">EDIT</a>
                                            </td>
                                            <td>
                                                <a href="{% url 'seller:custom_specifications_delete' id=instance.id %}" id="custom-red">DELETE</a>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <p>You haven't created any custom specifications yet. Add one below.</p>
                            {% endif %}
                        </div>
                        <hr>


                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--**********************************
    Content body end
***********************************-->

{% endblock %}
