import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import LoadingSpinner from "../includes/LoadingSpinner"; // Ensure this exists
import { BASE_URL } from "../../utils/config";

export default function AddrsessC() {
    const [addresses, setAddresses] = useState([]);
    const [loading, setLoading] = useState(true);
    const navigate = useNavigate();

    useEffect(() => {
        const fetchAddresses = async () => {
            try {
                const token = localStorage.getItem("access_token");
                const response = await fetch(`${BASE_URL}/addresses-c/`, {
                    headers: {
                        "Authorization": `Bearer ${token}`,
                        "Content-Type": "application/json",
                    },
                });

                if (!response.ok) throw new Error("Failed to fetch addresses");

                const data = await response.json();
                setAddresses(data);
            } catch (err) {
                console.error("Error fetching addresses:", err);
                navigate("/login");
            } finally {
                setLoading(false);
            }
        };
        fetchAddresses();
    }, [navigate]);

    if (loading) return <LoadingSpinner />;

    const handleDelete = async (id) => {
        if (!window.confirm("Are you sure you want to delete this address?")) return;

        try {
            const token = localStorage.getItem("access_token");
            const response = await fetch(`${BASE_URL}/addresses/${id}/delete/`, {
                method: "DELETE",
                headers: {
                    "Authorization": `Bearer ${token}`,
                    "Content-Type": "application/json",
                },
            });

            if (!response.ok) throw new Error("Failed to delete address");

            setAddresses(addresses.filter(address => address.id !== id));
        } catch (err) {
            console.error("Error deleting address:", err);
        }
    };

    return (
        <div className="container mx-auto mt-6 p-6">
            <header className="">
                <div className="flex justify-between items-center">
                    <button
                        onClick={() => navigate('/checkout')}
                        className="hover:text-purple-500 font-semibold text-lg flex items-center"
                    >
                        <i className="bx bx-arrow-back text-xl font-bold mr-2"></i> Back
                    </button>                
                    <button
                        onClick={() => navigate("/addresses-c/add/")}
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition"
                    >
                        Add Address
                    </button>
                </div>
                <h1 className="text-2xl font-semibold text-center">My Addresses</h1>
                
            </header>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                {addresses.length > 0 ? (
                    addresses.map((address) => (
                        <div key={address.id} className="p-4 border rounded-lg shadow-md bg-gray-50">
                            <p className="text-lg font-semibold">{address.address1}</p>
                            {address.address2 && <p className="text-sm text-gray-600">{address.address2}</p>}
                            <p className="text-sm text-gray-500">{address.city}, {address.state}, {address.pincode}</p>
                            <p className="text-sm text-gray-500">Type: {address.address_type}</p>

                            <div className="mt-4 flex space-x-4">
                                <button
                                    onClick={() => navigate(`/addresses-c/${address.id}/edit/`)}
                                    className="bg-yellow-500 text-white px-4 py-2 rounded-lg hover:bg-yellow-600 transition"
                                >
                                    Edit
                                </button>
                                <button
                                    onClick={() => handleDelete(address.id)}
                                    className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition"
                                >
                                    Delete
                                </button>
                            </div>
                        </div>
                    ))
                ) : (
                    <p className="text-gray-500 text-center w-full">No addresses found.</p>
                )}
            </div>
        </div>
    );
}
