import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// Central function to calculate all totals. This is our single source of truth.
const calculateTotals = (items) => {
  const subtotal = items.reduce((acc, item) => acc + (item.price || 0) * (item.quantity || 0), 0);
  // Dynamic delivery charge: 25 if subtotal > 0 and < 500, else 0
  const delivery = subtotal > 0 && subtotal < 500 ? 25 : 0;
  const discount = 0;
  const total = subtotal + delivery - discount;
  return { subtotal, total, delivery, discount };
};

export const useCartStore = create(
  persist(
    (set, get) => ({
      // State
      items: [],
      subtotal: 0,
      total: 0,
      delivery: 0,
      discount: 0,

      // Actions
      // An internal helper action to update state and recalculate everything
      _recalculate: (newItems) => {
        const { subtotal, total, delivery, discount } = calculateTotals(newItems);
        set({ items: newItems, subtotal, total, delivery, discount });
      },
      
      // Use this to set the initial cart from your API
      setInitialCart: (data) => {
        const items = data.cart_items || [];
        const subtotal = parseFloat(data.total_price) || 0;
        const delivery = parseFloat(data.delivery_charge) || 0;
        const discount = parseFloat(data.discount_amount) || 0;
        const total = subtotal + delivery - discount;
        set({ items, subtotal, total, delivery, discount });
      },

      increaseQuantity: (id) => {
        const updatedItems = get().items.map(item =>
          item.id === id ? { ...item, quantity: item.quantity + 1 } : item
        );
        get()._recalculate(updatedItems);
      },

      decreaseQuantity: (id) => {
        const currentItem = get().items.find(item => item.id === id);
        // If quantity is 1, call removeItem instead
        if (currentItem && currentItem.quantity <= 1) {
            get().removeItem(id);
            return;
        }
        const updatedItems = get().items.map(item =>
          item.id === id ? { ...item, quantity: item.quantity - 1 } : item
        );
        get()._recalculate(updatedItems);
      },
      
      removeItem: (id) => {
        const updatedItems = get().items.filter(item => item.id !== id);
        get()._recalculate(updatedItems);
      },
      
      clearCart: () => set({ items: [], subtotal: 0, total: 0, discount: 0, delivery: 0 }),
    }),
    {
      name: 'cart-storage', // The key for localStorage
    }
  )
);

export default useCartStore;