from django.shortcuts import render
import datetime
from django.db.models import Sum

from django.shortcuts import get_object_or_404, render, reverse, redirect
from django.contrib.auth import authenticate, login as auth_login, logout as auth_logout
from django.contrib.auth.decorators import login_required
from django.http.response import HttpResponseRedirect
from django.http import JsonResponse
from django.views.decorators.http import require_POST

from main.decorators import allow_manager,allow_seller
from main.functions import generate_form_errors

from promos.models import *
from items.models import Category, Brand, Color, CustomSpecification, Product, Option, ProductImage,Ram, Storage, IconImage, Spec
from users.models import *
from customers.models import Customer, CartItem, Whishlist, Service,ServiceRequest, Coupon, Address, CartTotal, OrderItem, Order, Review
from managers.forms import *
from managers.models import *
from django.contrib import messages
from django.forms import inlineformset_factory


@login_required(login_url="/app/login")
@allow_manager
def index(request):
    user = request.user
    manager = Manager.objects.get(user=user)
    orders = Order.objects.all().exclude(order_status='IN').count()
    earnings = Order.objects.exclude(order_status__in=['IN', 'CA']).aggregate(Sum('total'))["total__sum"]
    items = Product.objects.all().count()
    customers = Customer.objects.all().count()
    categories = Category.objects.filter(parent__isnull=True)


    instances = Order.objects.all().exclude(order_status='IN')[:5]

    context= {
        "title": "Store | Dashboard",
        "items": items,
        "customers": customers,
        "earnings":earnings,
        "orders": orders,
        "instances":instances,
        "categories":categories,
    }
    return render(request, "panel/index.html", context=context)



def login(request):
    if request.method == 'POST':
        email = request.POST.get('email')
        password = request.POST.get('password')

        if email and password:
            user = authenticate(request, email=email, password=password)

            if user is not None:
                auth_login(request, user)


                if user.is_manager:
                    return HttpResponseRedirect(reverse("managers:index"))
                elif user.is_seller:
                    return HttpResponseRedirect(reverse("seller:index"))
                else:
                    return HttpResponseRedirect(reverse("home"))


        context = {
            "title": "Login | Home",
            "error": True,
            "message": "Invalid credentials"
        }
        return render(request, "panel/login.html", context=context)


    return render(request, "panel/login.html", {"title": "Login | Home"})



def logout(request):
    auth_logout(request)
    return HttpResponseRedirect(reverse("managers:login"))


@login_required(login_url="/app/login")
@allow_manager
def order(request,id):
    user = request.user
    manager = Manager.objects.get(user=user)

    instance = get_object_or_404(Order, id=id)
    categories = Category.objects.filter(parent__isnull=True)


    context= {
        "title": "Orders | Dashboard",
        "sub_title": "Orders",
        "name": "Orders List",
        "instance":instance,
    }
    return render(request, "panel/order.html", context=context)



@login_required(login_url="/app/login")
@allow_manager
def reports(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)

    instances = Order.objects.all().exclude(order_status__in=['IN', 'CA'])


    context= {
        "title": "Reports | Dashboard",
        "sub_title": "Reports",
        "name": "Reports List",
        "instances":instances,
        "categories":categories,
    }
    return render(request, "panel/reports.html", context=context)




@login_required(login_url="/app/login")
@allow_manager
def settings(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)

    if request.method == "POST":
        first_name = request.POST.get('first_name')
        last_name = request.POST.get('last_name')
        phone_number = request.POST.get('phone_number')


        user.first_name = first_name
        user.last_name = last_name


        if phone_number:
            user.phone_number = phone_number
        else:
            user.phone_number = None  # Clear phone number if no input

        user.save()

        return HttpResponseRedirect(reverse('managers:settings'))
    else:
        context = {
            "title": "Settings | Dashboard",
            "sub_title": "Settings",
            "name": "Settings",
            "manager": manager,
        }
        return render(request, "panel/settings.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def password(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)

    if request.method == "POST":
        password = request.POST.get('password')
        password2 = request.POST.get('password2')

        if password!= password2:
            context= {
                "title": "Settings | Dashboard",
                "sub_title": "Settings",
                "error": True,
                "message": "Password is missmatch",
                "name": "Settings",
                "manager":manager,
            }
            return render(request, "panel/password.html", context=context)

        else:
            user.set_password(password)
            user.save()

            return HttpResponseRedirect(reverse('managers:settings'))
    else:

        context= {
            "title": "Settings | Dashboard",
            "sub_title": "Settings",
            "name": "Settings",
            "manager":manager,
            "categories":categories,
        }
        return render(request, "panel/password.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def customer_order(request,id):
    user = request.user
    manager = Manager.objects.get(user=user)

    customer = Customer.objects.get(id=id)

    instances = Order.objects.filter(customer=customer).exclude(order_status='IN')


    context= {
        "title": "Custoemr | Dashboard",
        "sub_title": "Custoemr",
        "name": "Orders List",
        "instances":instances,
    }
    return render(request, "panel/orders.html", context=context)




@login_required(login_url="/app/login")
@allow_manager
def order_accept(request,id):

    instance = get_object_or_404(Order, id=id)

    instance.order_status = 'IP'
    instance.save()

    return HttpResponseRedirect(reverse("managers:order", kwargs={'id': instance.id}))


@login_required(login_url="/app/login")
@allow_manager
def order_reject(request,id):

    instance = get_object_or_404(Order, id=id)

    instance.order_status = 'CA'
    instance.save()

    return HttpResponseRedirect(reverse("managers:order", kwargs={'id': instance.id}))


@login_required(login_url="/app/login")
@allow_manager
def order_dispatched(request,id):

    instance = get_object_or_404(Order, id=id)

    instance.order_status = 'DI'
    instance.save()

    return HttpResponseRedirect(reverse("managers:order", kwargs={'id': instance.id}))


@login_required(login_url="/app/login")
@allow_manager
def order_completed(request,id):

    instance = get_object_or_404(Order, id=id)

    instance.order_status = 'CO'
    instance.save()

    return HttpResponseRedirect(reverse("managers:order", kwargs={'id': instance.id}))


@login_required(login_url="/app/login")
@allow_manager
def categories(request):
    user = request.user
    manager = Manager.objects.get(user=user)
    instances = Category.objects.all().order_by('-id')
    categories = Category.objects.filter(parent__isnull=True)
    context = {
        "title": "Categories | Dashboard",
        "sub_title": "Categories",
        "name": "Categories List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/categories.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def categories_add(request):
    if request.method == "POST":
        form = CategoryForm(request.POST, request.FILES)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:categories"))
        else:
            message = generate_form_errors(form)
            context = {
                "title": "Manager Dashboard | Add Category",
                "sub_title": "Categories",
                "name": "Add Category",
                "error": True,
                "message": message,
                "form": form,
            }
            return render(request, "panel/categories-add.html", context=context)

    form = CategoryForm()
    context = {
        "title": "Manager Dashboard | Add Category",
        "sub_title": "Categories",
        "name": "Add Category",
        "form": form,
    }
    return render(request, "panel/categories-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def categories_edit(request, id):
    instance = Category.objects.get(id=id)
    if request.method == "POST":
        form = CategoryForm(request.POST, request.FILES, instance=instance)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:categories"))
        else:
            message = generate_form_errors(form)
            context = {
                "title": "Manager Dashboard | Edit Category",
                "sub_title": "Categories",
                "name": "Edit Category",
                "error": True,
                "message": message,
                "form": form,
                "instance": instance,
            }
            return render(request, "panel/categories-add.html", context=context)

    form = CategoryForm(instance=instance)
    context = {
        "title": "Manager Dashboard | Edit Category",
        "sub_title": "Categories",
        "name": "Edit Category",
        "form": form,
        "instance": instance,
    }
    return render(request, "panel/categories-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def categories_delete(request, id):
    instance = Category.objects.get(id=id)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:categories"))


@login_required(login_url="/app/login")
@allow_manager
def brands(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = Brand.objects.all().order_by('-id')
    context = {
        "title": "Brands | Dashboard",
        "sub_title": "Brands",
        "name": "Brands List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/brands.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def brands_add(request):
    if request.method == "POST":
        form = BrandForm(request.POST, request.FILES)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:brands"))
        else:
            message = generate_form_errors(form)
            context = {
                "title": "Manager Dashboard | Add Brand",
                "sub_title": "Brands",
                "name": "Add Brand",
                "error": True,
                "message": message,
                "form": form,
            }
            return render(request, "panel/brands-add.html", context=context)

    form = BrandForm()
    context = {
        "title": "Manager Dashboard | Add Brand",
        "sub_title": "Brands",
        "name": "Add Brand",
        "form": form,
    }
    return render(request, "panel/brands-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def brands_edit(request, id):
    instance = Brand.objects.get(id=id)
    if request.method == "POST":
        form = BrandForm(request.POST, request.FILES, instance=instance)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:brands"))
        else:
            message = generate_form_errors(form)
            context = {
                "title": "Manager Dashboard | Edit Brand",
                "sub_title": "Brands",
                "name": "Edit Brand",
                "error": True,
                "message": message,
                "form": form,
                "instance": instance,
            }
            return render(request, "panel/brands-add.html", context=context)

    form = BrandForm(instance=instance)
    context = {
        "title": "Manager Dashboard | Edit Brand",
        "sub_title": "Brands",
        "name": "Edit Brand",
        "form": form,
        "instance": instance,
    }
    return render(request, "panel/brands-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def brands_delete(request, id):
    instance = Brand.objects.get(id=id)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:brands"))


@login_required(login_url="/app/login")
@allow_manager
def colors(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = Color.objects.all()
    context = {
        "title": "Colors | Dashboard",
        "sub_title": "Colors",
        "name": "Colors List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/colors.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def colors_add(request):
    if request.method == "POST":
        form = ColorForm(request.POST)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:colors"))
        else:
            message = generate_form_errors(form)
            context = {
                "title": "Manager Dashboard | Add Color",
                "sub_title": "Colors",
                "name": "Add Color",
                "error": True,
                "message": message,
                "form": form,
            }
            return render(request, "panel/colors-add.html", context=context)

    form = ColorForm()
    context = {
        "title": "Manager Dashboard | Add Color",
        "sub_title": "Colors",
        "name": "Add Color",
        "form": form,
    }
    return render(request, "panel/colors-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def colors_edit(request, id):
    instance = Color.objects.get(id=id)
    if request.method == "POST":
        form = ColorForm(request.POST, instance=instance)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:colors"))
        else:
            message = generate_form_errors(form)
            context = {
                "title": "Manager Dashboard | Edit Color",
                "sub_title": "Colors",
                "name": "Edit Color",
                "error": True,
                "message": message,
                "form": form,
                "instance": instance,
            }
            return render(request, "panel/colors-add.html", context=context)

    form = ColorForm(instance=instance)
    context = {
        "title": "Manager Dashboard | Edit Color",
        "sub_title": "Colors",
        "name": "Edit Color",
        "form": form,
        "instance": instance,
    }
    return render(request, "panel/colors-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def colors_delete(request, id):
    instance = Color.objects.get(id=id)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:colors"))


@login_required(login_url="/app/login")
@allow_manager
def custom_specifications(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = CustomSpecification.objects.all().order_by('-id')
    context = {
        "title": "Custom Specifications | Dashboard",
        "sub_title": "Custom Specifications",
        "name": "Custom Specifications List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/custom-specifications.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def custom_specifications_add(request):
    if request.method == "POST":
        form = CustomSpecificationForm(request.POST)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:custom_specifications"))
        else:
            message = generate_form_errors(form)
            context = {
                "title": "Manager Dashboard | Add Custom Specification",
                "sub_title": "Custom Specifications",
                "name": "Add Custom Specification",
                "error": True,
                "message": message,
                "form": form,
            }
            return render(request, "panel/custom-specifications-add.html", context=context)

    form = CustomSpecificationForm()
    context = {
        "title": "Manager Dashboard | Add Custom Specification",
        "sub_title": "Custom Specifications",
        "name": "Add Custom Specification",
        "form": form,
    }
    return render(request, "panel/custom-specifications-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def custom_specifications_edit(request, id):
    instance = CustomSpecification.objects.get(id=id)
    if request.method == "POST":
        form = CustomSpecificationForm(request.POST, instance=instance)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:custom_specifications"))
        else:
            message = generate_form_errors(form)
            context = {
                "title": "Manager Dashboard | Edit Custom Specification",
                "sub_title": "Custom Specifications",
                "name": "Edit Custom Specification",
                "error": True,
                "message": message,
                "form": form,
                "instance": instance,
            }
            return render(request, "panel/custom-specifications-add.html", context=context)

    form = CustomSpecificationForm(instance=instance)
    context = {
        "title": "Manager Dashboard | Edit Custom Specification",
        "sub_title": "Custom Specifications",
        "name": "Edit Custom Specification",
        "form": form,
        "instance": instance,
    }
    return render(request, "panel/custom-specifications-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def custom_specifications_delete(request, id):
    instance = CustomSpecification.objects.get(id=id)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:custom_specifications"))


@login_required(login_url="/app/login")
@allow_manager
def options(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = Option.objects.all().order_by('-id')
    context = {
        "title": "Options | Dashboard",
        "sub_title": "Options",
        "name": "Options List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/options.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def options_add(request):
    if request.method == "POST":
        form = OptionForm(request.POST)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:options"))
        else:
            message = generate_form_errors(form)
            context = {
                "title": "Manager Dashboard | Add Option",
                "sub_title": "Options",
                "name": "Add Option",
                "error": True,
                "message": message,
                "form": form,
            }
            return render(request, "panel/options-add.html", context=context)

    form = OptionForm()
    context = {
        "title": "Manager Dashboard | Add Option",
        "sub_title": "Options",
        "name": "Add Option",
        "form": form,
    }
    return render(request, "panel/options-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def options_edit(request, id):
    instance = Option.objects.get(id=id)
    if request.method == "POST":
        form = OptionForm(request.POST, instance=instance)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:options"))
        else:
            message = generate_form_errors(form)
            context = {
                "title": "Manager Dashboard | Edit Option",
                "sub_title": "Options",
                "name": "Edit Option",
                "error": True,
                "message": message,
                "form": form,
                "instance": instance,
            }
            return render(request, "panel/options-add.html", context=context)

    form = OptionForm(instance=instance)
    context = {
        "title": "Manager Dashboard | Edit Option",
        "sub_title": "Options",
        "name": "Edit Option",
        "form": form,
        "instance": instance,
    }
    return render(request, "panel/options-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def options_delete(request, id):
    instance = Option.objects.get(id=id)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:options"))



@login_required(login_url="/app/login")
@allow_manager
def product_images(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = ProductImage.objects.all().order_by('-id')
    context = {
        "title": "Product Images | Dashboard",
        "sub_title": "Product Images",
        "name": "Product Images List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/product-images.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def product_images_add(request):
    if request.method == "POST":
        form = ProductImageForm(request.POST, request.FILES)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:product_images"))
        else:
            message = generate_form_errors(form)
            context = {
                "title": "Manager Dashboard | Add Product Image",
                "sub_title": "Product Images",
                "name": "Add Product Image",
                "error": True,
                "message": message,
                "form": form,
            }
            return render(request, "panel/product-images-add.html", context=context)

    form = ProductImageForm()
    context = {
        "title": "Manager Dashboard | Add Product Image",
        "sub_title": "Product Images",
        "name": "Add Product Image",
        "form": form,
    }
    return render(request, "panel/product-images-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def product_images_edit(request, id):
    instance = ProductImage.objects.get(id=id)
    if request.method == "POST":
        form = ProductImageForm(request.POST, request.FILES, instance=instance)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:product_images"))
        else:
            message = generate_form_errors(form)
            context = {
                "title": "Manager Dashboard | Edit Product Image",
                "sub_title": "Product Images",
                "name": "Edit Product Image",
                "error": True,
                "message": message,
                "form": form,
                "instance": instance,
            }
            return render(request, "panel/product-images-add.html", context=context)

    form = ProductImageForm(instance=instance)
    context = {
        "title": "Manager Dashboard | Edit Product Image",
        "sub_title": "Product Images",
        "name": "Edit Product Image",
        "form": form,
        "instance": instance,
    }
    return render(request, "panel/product-images-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def product_images_delete(request, id):
    instance = ProductImage.objects.get(id=id)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:product_images"))



@login_required(login_url="/app/login")
@allow_manager
def rams(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = Ram.objects.all()
    context = {
        "title": "RAMs | Dashboard",
        "sub_title": "RAMs",
        "name": "RAMs List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/rams.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def rams_add(request):
    if request.method == "POST":
        form = RamForm(request.POST)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:rams"))
        else:
            message = generate_form_errors(form)
            context = {
                "title": "Manager Dashboard | Add RAM",
                "sub_title": "RAMs",
                "name": "Add RAM",
                "error": True,
                "message": message,
                "form": form,
            }
            return render(request, "panel/rams-add.html", context=context)

    form = RamForm()
    context = {
        "title": "Manager Dashboard | Add RAM",
        "sub_title": "RAMs",
        "name": "Add RAM",
        "form": form,
    }
    return render(request, "panel/rams-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def rams_edit(request, id):
    instance = Ram.objects.get(id=id)
    if request.method == "POST":
        form = RamForm(request.POST, instance=instance)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:rams"))
        else:
            message = generate_form_errors(form)
            context = {
                "title": "Manager Dashboard | Edit RAM",
                "sub_title": "RAMs",
                "name": "Edit RAM",
                "error": True,
                "message": message,
                "form": form,
                "instance": instance,
            }
            return render(request, "panel/rams-add.html", context=context)

    form = RamForm(instance=instance)
    context = {
        "title": "Manager Dashboard | Edit RAM",
        "sub_title": "RAMs",
        "name": "Edit RAM",
        "form": form,
        "instance": instance,
    }
    return render(request, "panel/rams-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def rams_delete(request, id):
    instance = Ram.objects.get(id=id)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:rams"))


@login_required(login_url="/app/login")
@allow_manager
def services(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = Service.objects.all()

    context = {
        "title": "Services | Dashboard",
        "sub_title": "Services",
        "name": "Services List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/services.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def services_add(request):
    if request.method == "POST":
        form = ServiceForm(request.POST, request.FILES)  # Ensure you pass request.FILES for image upload
        if form.is_valid():
            form.save()  # Save the form data
            return HttpResponseRedirect(reverse("managers:services"))  # Redirect to the services list page after submission
        else:
            message = "There were errors in the form. Please correct them below."
    else:
        form = ServiceForm()
        message = None

    context = {
        "title": "Manager Dashboard | Add Service",
        "sub_title": "Services",
        "name": "Add Service",
        "form": form,
        "message": message,
    }
    return render(request, "panel/services-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def services_edit(request, id):
    instance = get_object_or_404(Service, id=id)  # Better than using Service.objects.get()

    if request.method == "POST":
        form = ServiceForm(request.POST, request.FILES, instance=instance)  # Ensure request.FILES is included for image upload
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:services"))  # Redirect after saving
        else:
            message = "There were errors in the form. Please correct them below."
    else:
        form = ServiceForm(instance=instance)  # Pre-populate form with existing instance data
        message = None

    context = {
        "title": "Manager Dashboard | Edit Service",
        "sub_title": "Services",
        "name": "Edit Service",
        "form": form,
        "message": message,
        "instance": instance,
    }
    return render(request, "panel/services-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def services_delete(request, id):
    instance = Service.objects.get(id=id)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:services"))



@login_required(login_url="/app/login")
@allow_manager
def service_requests(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = ServiceRequest.objects.all()
    context = {
        "title": "Service Requests | Dashboard",
        "sub_title": "Service Requests",
        "name": "Service Request List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/service-requests.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def service_requests_add(request):
    if request.method == "POST":
        form = ServiceRequestForm(request.POST)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:service_requests"))
        else:
            message = generate_form_errors(form)
    else:
        form = ServiceRequestForm()
        message = None

    context = {
        "title": "Manager Dashboard | Add Service Request",
        "sub_title": "Service Requests",
        "name": "Add Service Request",
        "form": form,
        "message": message,
    }
    return render(request, "panel/service-requests-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def service_requests_edit(request, id):
    instance = ServiceRequest.objects.get(id=id)

    if request.method == "POST":
        form = ServiceRequestForm(request.POST, instance=instance)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:service_requests"))
        else:
            message = generate_form_errors(form)
    else:
        form = ServiceRequestForm(instance=instance)
        message = None

    context = {
        "title": "Manager Dashboard | Edit Service Request",
        "sub_title": "Service Requests",
        "name": "Edit Service Request",
        "form": form,
        "message": message,
        "instance": instance,
    }
    return render(request, "panel/service-requests-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def service_requests_delete(request, id):
    instance = ServiceRequest.objects.get(id=id)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:service_requests"))


def request_service_detail_view(request, id):
    instance = get_object_or_404(ServiceRequest, id=id)

    context = {
        "title": "Manager Dashboard | Request Details",
        "sub_title": "Requests",
        "name": "Service Request Details",
        "instance": instance,
    }
    return render(request, "panel/request-service-detail.html", context=context)



@login_required(login_url="/app/login")
@allow_manager
def coupons(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = Coupon.objects.all()

    context = {
        "title": "Coupons | Dashboard",
        "sub_title": "Coupons",
        "name": "Coupons List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/coupons.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def coupons_add(request):
    if request.method == "POST":
        form = CouponForm(request.POST)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:coupons"))
        else:
            message = generate_form_errors(form)
    else:
        form = CouponForm()
        message = None

    context = {
        "title": "Manager Dashboard | Add Coupon",
        "sub_title": "Coupons",
        "name": "Add Coupon",
        "form": form,
        "message": message,
    }
    return render(request, "panel/coupons-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def coupons_edit(request, id):
    instance = Coupon.objects.get(id=id)

    if request.method == "POST":
        form = CouponForm(request.POST, instance=instance)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:coupons"))
        else:
            message = generate_form_errors(form)
    else:
        form = CouponForm(instance=instance)
        message = None

    context = {
        "title": "Manager Dashboard | Edit Coupon",
        "sub_title": "Coupons",
        "name": "Edit Coupon",
        "form": form,
        "message": message,
        "instance": instance,
    }
    return render(request, "panel/coupons-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def coupons_delete(request, id):
    instance = Coupon.objects.get(id=id)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:coupons"))



@login_required(login_url="/app/login")
@allow_manager
def addresses(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = Address.objects.all()

    context = {
        "title": "Addresses | Dashboard",
        "sub_title": "Addresses",
        "name": "Addresses List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/addresses.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def addresses_add(request):
    if request.method == "POST":
        form = AddressForm(request.POST)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:addresses"))
        else:
            message = generate_form_errors(form)
    else:
        form = AddressForm()
        message = None

    context = {
        "title": "Manager Dashboard | Add Address",
        "sub_title": "Addresses",
        "name": "Add Address",
        "form": form,
        "message": message,
    }
    return render(request, "panel/addresses-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def addresses_edit(request, id):
    instance = Address.objects.get(id=id)

    if request.method == "POST":
        form = AddressForm(request.POST, instance=instance)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:addresses"))
        else:
            message = generate_form_errors(form)
    else:
        form = AddressForm(instance=instance)
        message = None

    context = {
        "title": "Manager Dashboard | Edit Address",
        "sub_title": "Addresses",
        "name": "Edit Address",
        "form": form,
        "message": message,
        "instance": instance,
    }
    return render(request, "panel/addresses-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def addresses_delete(request, id):
    instance = Address.objects.get(id=id)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:addresses"))


@login_required(login_url="/app/login")
@allow_manager
def cart_totals(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = CartTotal.objects.all()

    context = {
        "title": "Cart Totals | Dashboard",
        "sub_title": "Cart Totals",
        "name": "Cart Totals List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/cart-totals.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def cart_totals_add(request):
    if request.method == "POST":
        form = CartTotalForm(request.POST)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:cart_totals"))
        else:
            message = generate_form_errors(form)
    else:
        form = CartTotalForm()
        message = None

    context = {
        "title": "Manager Dashboard | Add Cart Total",
        "sub_title": "Cart Totals",
        "name": "Add Cart Total",
        "form": form,
        "message": message,
    }
    return render(request, "panel/cart-totals-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def cart_totals_edit(request, id):
    instance = CartTotal.objects.get(id=id)

    if request.method == "POST":
        form = CartTotalForm(request.POST, instance=instance)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:cart_totals"))
        else:
            message = generate_form_errors(form)
    else:
        form = CartTotalForm(instance=instance)
        message = None

    context = {
        "title": "Manager Dashboard | Edit Cart Total",
        "sub_title": "Cart Totals",
        "name": "Edit Cart Total",
        "form": form,
        "message": message,
        "instance": instance,
    }
    return render(request, "panel/cart-totals-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def cart_totals_delete(request, id):
    instance = CartTotal.objects.get(id=id)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:cart_totals"))


@login_required(login_url="/app/login")
@allow_manager
def order_items(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = OrderItem.objects.all()

    context = {
        "title": "Order Items | Dashboard",
        "sub_title": "Order Items",
        "name": "Order Items List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/order-items.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def order_items_add(request):
    if request.method == "POST":
        form = OrderItemForm(request.POST)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:order_items"))
        else:
            message = generate_form_errors(form)
    else:
        form = OrderItemForm()
        message = None

    context = {
        "title": "Manager Dashboard | Add Order Item",
        "sub_title": "Order Items",
        "name": "Add Order Item",
        "form": form,
        "message": message,
    }
    return render(request, "panel/order-items-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def order_items_edit(request, id):
    instance = OrderItem.objects.get(id=id)

    if request.method == "POST":
        form = OrderItemForm(request.POST, instance=instance)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:order_items"))
        else:
            message = generate_form_errors(form)
    else:
        form = OrderItemForm(instance=instance)
        message = None

    context = {
        "title": "Manager Dashboard | Edit Order Item",
        "sub_title": "Order Items",
        "name": "Edit Order Item",
        "form": form,
        "message": message,
        "instance": instance,
    }
    return render(request, "panel/order-items-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def order_items_delete(request, id):
    instance = OrderItem.objects.get(id=id)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:order_items"))


@login_required(login_url="/app/login")
@allow_manager
def orders(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = Order.objects.all()

    context = {
        "title": "Orders | Dashboard",
        "sub_title": "Orders",
        "name": "Orders List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/orders.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def orders_add(request):
    if request.method == "POST":
        form = OrderForm(request.POST)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:orders"))
        else:
            message = generate_form_errors(form)
    else:
        form = OrderForm()
        message = None

    context = {
        "title": "Manager Dashboard | Add Order",
        "sub_title": "Orders",
        "name": "Add Order",
        "form": form,
        "message": message,
    }
    return render(request, "panel/orders-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def orders_edit(request, id):
    instance = Order.objects.get(id=id)

    if request.method == "POST":
        form = OrderForm(request.POST, instance=instance)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:orders"))
        else:
            message = generate_form_errors(form)
    else:
        form = OrderForm(instance=instance)
        message = None

    context = {
        "title": "Manager Dashboard | Edit Order",
        "sub_title": "Orders",
        "name": "Edit Order",
        "form": form,
        "message": message,
        "instance": instance,
    }
    return render(request, "panel/orders-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def orders_delete(request, id):
    instance = Order.objects.get(id=id)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:orders"))


@login_required(login_url="/app/login")
@allow_manager
def reviews(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = Review.objects.all()

    context = {
        "title": "Reviews | Dashboard",
        "sub_title": "Reviews",
        "name": "Reviews List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/reviews.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def reviews_add(request):
    if request.method == "POST":
        form = ReviewForm(request.POST)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:reviews"))
        else:
            message = generate_form_errors(form)
    else:
        form = ReviewForm()
        message = None

    context = {
        "title": "Manager Dashboard | Add Review",
        "sub_title": "Reviews",
        "name": "Add Review",
        "form": form,
        "message": message,
    }
    return render(request, "panel/reviews-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def reviews_edit(request, id):
    instance = Review.objects.get(id=id)

    if request.method == "POST":
        form = ReviewForm(request.POST, instance=instance)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:reviews"))
        else:
            message = generate_form_errors(form)
    else:
        form = ReviewForm(instance=instance)
        message = None

    context = {
        "title": "Manager Dashboard | Edit Review",
        "sub_title": "Reviews",
        "name": "Edit Review",
        "form": form,
        "message": message,
        "instance": instance,
    }
    return render(request, "panel/reviews-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def reviews_delete(request, id):
    instance = Review.objects.get(id=id)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:reviews"))


@login_required(login_url="/app/login")
@allow_manager
def products(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = Product.objects.all()

    context = {
        "title": "Products | Dashboard",
        "sub_title": "Products",
        "name": "Products List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/products.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def products_add(request):
    if request.method == "POST":
        form = ProductForm(request.POST, request.FILES)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:products"))
        else:
            message = generate_form_errors(form)
    else:
        form = ProductForm()
        message = None

    context = {
        "title": "Manager Dashboard | Add Product",
        "sub_title": "Products",
        "name": "Add Product",
        "form": form,
        "message": message,
    }
    return render(request, "panel/products-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def products_edit(request, id):
    instance = Product.objects.get(id=id)

    if request.method == "POST":
        form = ProductForm(request.POST, request.FILES, instance=instance)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:products"))
        else:
            message = generate_form_errors(form)
    else:
        form = ProductForm(instance=instance)
        message = None

    context = {
        "title": "Manager Dashboard | Edit Product",
        "sub_title": "Products",
        "name": "Edit Product",
        "form": form,
        "message": message,
        "instance": instance,
    }
    return render(request, "panel/products-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def products_delete(request, id):
    instance = Product.objects.get(id=id)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:products"))


@login_required(login_url="/app/login")
@allow_manager
def storages(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = Storage.objects.all()

    context = {
        "title": "Storages | Dashboard",
        "sub_title": "Storages",
        "name": "Storages List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/storages.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def storages_add(request):
    if request.method == "POST":
        form = StorageForm(request.POST)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:storages"))
        else:
            message = generate_form_errors(form)
    else:
        form = StorageForm()
        message = None

    context = {
        "title": "Manager Dashboard | Add Storage",
        "sub_title": "Storages",
        "name": "Add Storage",
        "form": form,
        "message": message,
    }
    return render(request, "panel/storages-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def storages_edit(request, id):
    instance = Storage.objects.get(id=id)

    if request.method == "POST":
        form = StorageForm(request.POST, instance=instance)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:storages"))
        else:
            message = generate_form_errors(form)
    else:
        form = StorageForm(instance=instance)
        message = None

    context = {
        "title": "Manager Dashboard | Edit Storage",
        "sub_title": "Storages",
        "name": "Edit Storage",
        "form": form,
        "message": message,
        "instance": instance,
    }
    return render(request, "panel/storages-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def storages_delete(request, id):
    instance = Storage.objects.get(id=id)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:storages"))


@login_required(login_url="/app/login")
@allow_manager
def icon_images(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = IconImage.objects.all().order_by('-id')

    context = {
        "title": "Icon Images | Dashboard",
        "sub_title": "Icon Images",
        "name": "Icon Images List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/icon-images.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def icon_images_add(request):
    if request.method == "POST":
        form = IconImageForm(request.POST, request.FILES)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:icon_images"))
        else:
            message = generate_form_errors(form)
    else:
        form = IconImageForm()
        message = None

    context = {
        "title": "Manager Dashboard | Add Icon Image",
        "sub_title": "Icon Images",
        "name": "Add Icon Image",
        "form": form,
        "message": message,
    }
    return render(request, "panel/icon-images-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def icon_images_edit(request, id):
    instance = IconImage.objects.get(id=id)

    if request.method == "POST":
        form = IconImageForm(request.POST, request.FILES, instance=instance)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:icon_images"))
        else:
            message = generate_form_errors(form)
    else:
        form = IconImageForm(instance=instance)
        message = None

    context = {
        "title": "Manager Dashboard | Edit Icon Image",
        "sub_title": "Icon Images",
        "name": "Edit Icon Image",
        "form": form,
        "message": message,
        "instance": instance,
    }
    return render(request, "panel/icon-images-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def icon_images_delete(request, id):
    instance = IconImage.objects.get(id=id)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:icon_images"))




@login_required(login_url="/app/login")
@allow_manager
def specs(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = Spec.objects.all().order_by('-id')

    context = {
        "title": "Specs | Dashboard",
        "sub_title": "Specs",
        "name": "Specs List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/specs.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def specs_add(request):
    if request.method == "POST":
        form = SpecForm(request.POST, request.FILES)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:specs"))
        else:
            message = generate_form_errors(form)
    else:
        form = SpecForm()
        message = None

    context = {
        "title": "Manager Dashboard | Add Spec",
        "sub_title": "Specs",
        "name": "Add Spec",
        "form": form,
        "message": message,
    }
    return render(request, "panel/specs-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def specs_edit(request, id):
    instance = Spec.objects.get(id=id)

    if request.method == "POST":
        form = SpecForm(request.POST, request.FILES, instance=instance)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:specs"))
        else:
            message = generate_form_errors(form)
    else:
        form = SpecForm(instance=instance)
        message = None

    context = {
        "title": "Manager Dashboard | Edit Spec",
        "sub_title": "Specs",
        "name": "Edit Spec",
        "form": form,
        "message": message,
        "instance": instance,
    }
    return render(request, "panel/specs-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def specs_delete(request, id):
    instance = Spec.objects.get(id=id)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:specs"))



@login_required(login_url="/app/login")
@allow_manager
def customers(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = Customer.objects.all()

    context = {
        "title": "Customers | Dashboard",
        "sub_title": "Customers",
        "name": "Customers List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/customers.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def customers_add(request):
    if request.method == "POST":
        form = CustomerForm(request.POST)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:customers"))
        else:
            message = generate_form_errors(form)
    else:
        form = CustomerForm()
        message = None

    context = {
        "title": "Manager Dashboard | Add Customer",
        "sub_title": "Customers",
        "name": "Add Customer",
        "form": form,
        "message": message,
    }
    return render(request, "panel/customers-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def customers_edit(request, id):
    instance = Customer.objects.get(id=id)

    if request.method == "POST":
        form = CustomerForm(request.POST, instance=instance)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:customers"))
        else:
            message = generate_form_errors(form)
    else:
        form = CustomerForm(instance=instance)
        message = None

    context = {
        "title": "Manager Dashboard | Edit Customer",
        "sub_title": "Customers",
        "name": "Edit Customer",
        "form": form,
        "message": message,
        "instance": instance,
    }
    return render(request, "panel/customers-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def customers_delete(request, id):
    instance = Customer.objects.get(id=id)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:customers"))

@login_required(login_url="/app/login")
@allow_manager
def cart_items(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = CartItem.objects.all()

    context = {
        "title": "Cart Items | Dashboard",
        "sub_title": "Cart Items",
        "name": "Cart Items List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/cart-items.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def cart_items_add(request):
    if request.method == "POST":
        form = CartItemForm(request.POST)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:cart_items"))
        else:
            message = generate_form_errors(form)
    else:
        form = CartItemForm()
        message = None

    context = {
        "title": "Manager Dashboard | Add Cart Item",
        "sub_title": "Cart Items",
        "name": "Add Cart Item",
        "form": form,
        "message": message,
    }
    return render(request, "panel/cart-items-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def cart_items_edit(request, id):
    instance = CartItem.objects.get(id=id)

    if request.method == "POST":
        form = CartItemForm(request.POST, instance=instance)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:cart_items"))
        else:
            message = generate_form_errors(form)
    else:
        form = CartItemForm(instance=instance)
        message = None

    context = {
        "title": "Manager Dashboard | Edit Cart Item",
        "sub_title": "Cart Items",
        "name": "Edit Cart Item",
        "form": form,
        "message": message,
        "instance": instance,
    }
    return render(request, "panel/cart-items-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def cart_items_delete(request, id):
    instance = CartItem.objects.get(id=id)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:cart_items"))


@login_required(login_url="/app/login")
@allow_manager
def whishlist(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = Whishlist.objects.all()

    context = {
        "title": "Whishlist | Dashboard",
        "sub_title": "Whishlist",
        "name": "Whishlist Items",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/whishlist.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def whishlist_add(request):
    if request.method == "POST":
        form = WhishlistForm(request.POST)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:whishlist"))
        else:
            message = generate_form_errors(form)
    else:
        form = WhishlistForm()
        message = None

    context = {
        "title": "Manager Dashboard | Add Whishlist Item",
        "sub_title": "Whishlist",
        "name": "Add Whishlist Item",
        "form": form,
        "message": message,
    }
    return render(request, "panel/whishlist-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def whishlist_edit(request, id):
    instance = Whishlist.objects.get(id=id)

    if request.method == "POST":
        form = WhishlistForm(request.POST, instance=instance)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:whishlist"))
        else:
            message = generate_form_errors(form)
    else:
        form = WhishlistForm(instance=instance)
        message = None

    context = {
        "title": "Manager Dashboard | Edit Whishlist Item",
        "sub_title": "Whishlist",
        "name": "Edit Whishlist Item",
        "form": form,
        "message": message,
        "instance": instance,
    }
    return render(request, "panel/whishlist-add.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def whishlist_delete(request, id):
    instance = Whishlist.objects.get(id=id)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:whishlist"))



@login_required(login_url="/app/login")
@allow_manager
def user_list(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = User.objects.all()
    context = {
        "title": "Users | Dashboard",
        "sub_title": "Users",
        "name": "User List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/users.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def user_add(request):
    if request.method == "POST":
        form = UserForm(request.POST)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:user_list"))
        else:
            message = generate_form_errors(form)
            context = {
                "title": "Manager Dashboard | Add User",
                "sub_title": "Users",
                "name": "Add User",
                "error": True,
                "message": message,
                "form": form,
            }
            return render(request, "panel/add-user.html", context=context)

    form = UserForm()
    context = {
        "title": "Manager Dashboard | Add User",
        "sub_title": "Users",
        "name": "Add User",
        "form": form,
    }
    return render(request, "panel/add-user.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def user_edit(request, pk):
    instance = get_object_or_404(User, pk=pk)
    if request.method == "POST":
        form = UserForm(request.POST, instance=instance)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:user_list"))
        else:
            message = generate_form_errors(form)
            context = {
                "title": "Manager Dashboard | Edit User",
                "sub_title": "Users",
                "name": "Edit User",
                "error": True,
                "message": message,
                "form": form,
                "instance": instance,
            }
            return render(request, "panel/add-user.html", context=context)

    form = UserForm(instance=instance)
    context = {
        "title": "Manager Dashboard | Edit User",
        "sub_title": "Users",
        "name": "Edit User",
        "form": form,
        "instance": instance,
    }
    return render(request, "panel/add-user.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def user_delete(request, pk):
    instance = get_object_or_404(User, pk=pk)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:user_list"))




@login_required(login_url="/app/login")
@allow_manager
def otpverifier_list(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = OTPVerifier.objects.all()
    context = {
        "title": "OTP Verifiers | Dashboard",
        "sub_title": "OTP Verifiers",
        "name": "OTP Verifier List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/otp-verifiers.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def sliders(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = Slider.objects.all()
    context = {
        "title": "Sliders | Dashboard",
        "sub_title": "Sliders",
        "name": "Slider List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/sliders.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def slider_add(request):
    if request.method == "POST":
        form = SliderForm(request.POST, request.FILES)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:sliders"))
        else:
            message = generate_form_errors(form)
            context = {
                "title": "Manager Dashboard | Add Slider",
                "sub_title": "Sliders",
                "name": "Add Slider",
                "error": True,
                "message": message,
                "form": form,
            }
            return render(request, "panel/add-slider.html", context=context)

    form = SliderForm()
    context = {
        "title": "Manager Dashboard | Add Slider",
        "sub_title": "Sliders",
        "name": "Add Slider",
        "form": form,
    }
    return render(request, "panel/add-slider.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def slider_edit(request, pk):
    instance = get_object_or_404(Slider, pk=pk)
    if request.method == "POST":
        form = SliderForm(request.POST, request.FILES, instance=instance)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:sliders"))
        else:
            message = generate_form_errors(form)
            context = {
                "title": "Manager Dashboard | Edit Slider",
                "sub_title": "Sliders",
                "name": "Edit Slider",
                "error": True,
                "message": message,
                "form": form,
                "instance": instance,
            }
            return render(request, "panel/add-slider.html", context=context)

    form = SliderForm(instance=instance)
    context = {
        "title": "Manager Dashboard | Edit Slider",
        "sub_title": "Sliders",
        "name": "Edit Slider",
        "form": form,
        "instance": instance,
    }
    return render(request, "panel/add-slider.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def slider_delete(request, pk):
    instance = get_object_or_404(Slider, pk=pk)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:sliders"))


@login_required(login_url="/app/login")
@allow_manager
def offer_list(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = Offer.objects.all()
    context = {
        "title": "Offers | Dashboard",
        "sub_title": "Offers",
        "name": "Offer List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/offers.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def offer_add(request):
    if request.method == "POST":
        form = OfferForm(request.POST, request.FILES)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:offer"))
        else:
            message = generate_form_errors(form)
            context = {
                "title": "Manager Dashboard | Add Offer",
                "sub_title": "Offers",
                "name": "Add Offer",
                "error": True,
                "message": message,
                "form": form,
            }
            return render(request, "panel/add-offer.html", context=context)

    form = OfferForm()
    context = {
        "title": "Manager Dashboard | Add Offer",
        "sub_title": "Offers",
        "name": "Add Offer",
        "form": form,
    }
    return render(request, "panel/add-offer.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def offer_edit(request, pk):
    instance = get_object_or_404(Offer, pk=pk)
    if request.method == "POST":
        form = OfferForm(request.POST, request.FILES, instance=instance)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:offer"))
        else:
            message = generate_form_errors(form)
            context = {
                "title": "Manager Dashboard | Edit Offer",
                "sub_title": "Offers",
                "name": "Edit Offer",
                "error": True,
                "message": message,
                "form": form,
                "instance": instance,
            }
            return render(request, "panel/add-offer.html", context=context)

    form = OfferForm(instance=instance)
    context = {
        "title": "Manager Dashboard | Edit Offer",
        "sub_title": "Offers",
        "name": "Edit Offer",
        "form": form,
        "instance": instance,
    }
    return render(request, "panel/add-offer.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def offer_delete(request, pk):
    instance = get_object_or_404(Offer, pk=pk)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:offer"))


@login_required(login_url="/app/login")
@allow_manager
def offers_list(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    manager = Manager.objects.get(user=user)
    instances = Offers.objects.all()
    context = {
        "title": "Offers List | Dashboard",
        "sub_title": "Offers List",
        "name": "Offers List",
        "instances": instances,
        "categories":categories,
    }
    return render(request, "panel/offers-list.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def offers_add(request):
    if request.method == "POST":
        form = OffersForm(request.POST, request.FILES)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:offers"))
        else:
            message = generate_form_errors(form)
            context = {
                "title": "Manager Dashboard | Add Offers",
                "sub_title": "Offers",
                "name": "Add Offers",
                "error": True,
                "message": message,
                "form": form,
            }
            return render(request, "panel/add-offers.html", context=context)

    form = OffersForm()
    context = {
        "title": "Manager Dashboard | Add Offers",
        "sub_title": "Offers",
        "name": "Add Offers",
        "form": form,
    }
    return render(request, "panel/add-offers.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def offers_edit(request, pk):
    instance = get_object_or_404(Offers, pk=pk)
    if request.method == "POST":
        form = OffersForm(request.POST, request.FILES, instance=instance)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("managers:offers"))
        else:
            message = generate_form_errors(form)
            context = {
                "title": "Manager Dashboard | Edit Offers",
                "sub_title": "Offers",
                "name": "Edit Offers",
                "error": True,
                "message": message,
                "form": form,
                "instance": instance,
            }
            return render(request, "panel/add-offers.html", context=context)

    form = OffersForm(instance=instance)
    context = {
        "title": "Manager Dashboard | Edit Offers",
        "sub_title": "Offers",
        "name": "Edit Offers",
        "form": form,
        "instance": instance,
    }
    return render(request, "panel/add-offers.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def offers_delete(request, pk):
    instance = get_object_or_404(Offers, pk=pk)
    instance.delete()
    return HttpResponseRedirect(reverse("managers:offers"))


@login_required(login_url="/app/login")
@allow_manager
def category_nav(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    # categories = Category.objects.filter(parent__isnull=True).prefetch_related('subcategories')
    categories = Category.objects.filter(parent__isnull=True)
    context = {
        "title": "Category List | Dashboard",
        "sub_title": "Category List",
        "name": "Category List",
        "categories": categories,
    }
    return render(request, "includes/admin-nav.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def subcategory_list(request, category_id):
    subcategories = Category.objects.filter(parent_id=category_id)
    categories = Category.objects.filter(parent__isnull=True)
    selected_category = Category.objects.get(id=category_id)

    context = {
        "title": f"{selected_category.name} - Subcategory List | Dashboard",
        "sub_title": f"Subcategories of {selected_category.name}",
        "name": "Subcategory List",
        "instances": subcategories,
        "categories": categories,
    }
    return render(request, 'panel/subcategory_list.html', context)


@login_required(login_url="/app/login")
@allow_manager
def sellers_add(request):
    """
    Assigns seller role to an existing user based on their email.
    """
    if request.method == "POST":
        form = AssignSellerByEmailForm(request.POST)
        if form.is_valid():
            email = form.cleaned_data['email']
            try:

                user_to_make_seller = User.objects.get(email__iexact=email)


                if Seller.objects.filter(user=user_to_make_seller).exists():
                    messages.warning(request, f'User with email {email} is already a seller.')
                else:


                    if hasattr(user_to_make_seller, 'is_seller'):
                         user_to_make_seller.is_seller = True
                         user_to_make_seller.save()


                    Seller.objects.create(user=user_to_make_seller)
                    messages.success(request, f'Successfully made user {email} a seller.')
                    return HttpResponseRedirect(reverse("managers:sellers"))

            except User.DoesNotExist:

                messages.error(request, f'No user found with the email address: {email}.')
            except Exception as e:

                 messages.error(request, f'An unexpected error occurred: {str(e)}')





    else:
        form = AssignSellerByEmailForm()

    context = {
        "title": "Manager Dashboard | Add Seller by Email",
        "sub_title": "Sellers",
        "name": "Add Seller by Email",
        "form": form,

        # "message": message,
         # Add other context variables needed by your base template
        # "categories": Category.objects.filter(parent__isnull=True),
    }
    return render(request, "panel/sellers-add.html", context=context)

# sellers list view (sellers), sellers_delete view
@login_required(login_url="/app/login")
@allow_manager
def sellers(request):
    instances = Seller.objects.select_related('user').all().order_by('-id')
    context = {
        "title": "Sellers | Dashboard",
        "sub_title": "Sellers",
        "name": "Sellers List",
        "instances": instances,
    }
    return render(request, "panel/sellers.html", context=context)


@login_required(login_url="/app/login")
@allow_manager
def sellers_delete(request, id):
    instance = get_object_or_404(Seller, id=id)
    user_email = instance.user.email
    instance.delete()
    messages.success(request, f'Seller profile for {user_email} deleted.')
    return HttpResponseRedirect(reverse("managers:sellers"))









# --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- ---------------------------------------------------------------------------------------------------------------------------------------
#  --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- ---------------------------------------------------------------------------------------------------------------------------------------
#  --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- ---------------------------------------------------------------------------------------------------------------------------------------
#  --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- ---------------------------------------------------------------------------------------------------------------------------------------
#  --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- ---------------------------------------------------------------------------------------------------------------------------------------
#  --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- ---------------------------------------------------------------------------------------------------------------------------------------
#  --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- ---------------------------------------------------------------------------------------------------------------------------------------


                                    #   SELLER


#  --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- ---------------------------------------------------------------------------------------------------------------------------------------
#  --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- ---------------------------------------------------------------------------------------------------------------------------------------
#  --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- ---------------------------------------------------------------------------------------------------------------------------------------
#  --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- ---------------------------------------------------------------------------------------------------------------------------------------
#  --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- ---------------------------------------------------------------------------------------------------------------------------------------
#  --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- ---------------------------------------------------------------------------------------------------------------------------------------
#  --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- ---------------------------------------------------------------------------------------------------------------------------------------
#  --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- ---------------------------------------------------------------------------------------------------------------------------------------
#  --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- ---------------------------------------------------------------------------------------------------------------------------------------
#  --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- ---------------------------------------------------------------------------------------------------------------------------------------
#  --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------- ---------------------------------------------------------------------------------------------------------------------------------------



@login_required(login_url="/app/login")
@allow_seller
def seller_index(request):
    user = request.user
    seller = Seller.objects.get(user=user)


    orders = Order.objects.filter(sellers=seller).exclude(order_status='IN').count()
    earnings = Order.objects.filter(sellers=seller).exclude(order_status__in=['IN', 'CA']).aggregate(Sum('total'))["total__sum"]
    items = Product.objects.filter(seller=seller).count()
    customers = Customer.objects.filter(id__in=Order.objects.filter(sellers=seller).values('customer_id').distinct()).count()
    categories = Category.objects.filter(parent__isnull=True)

    instances = Order.objects.filter(sellers=seller).exclude(order_status='IN')[:5]

    context = {
        "title": "Seller | Dashboard",
        "items": items,
        "customers": customers,
        "earnings": earnings or 0,
        "orders": orders,
        "instances": instances,
        "categories": categories,
    }
    return render(request, "seller/index.html", context=context)




@login_required(login_url="/app/login")
def seller_logout(request):
    logout(request)
    return HttpResponseRedirect(reverse("managers:login"))



@login_required(login_url="/app/login")
@allow_seller
def seller_orders(request):
    user = request.user
    seller = Seller.objects.get(user=user)
    categories = Category.objects.filter(parent__isnull=True)

    instances = Order.objects.filter(sellers=seller).exclude(order_status='IN').order_by('-created_datetime')

    context = {
        "title": "Orders | Seller Dashboard",
        "sub_title": "Orders",
        "name": "Orders List",
        "instances": instances,
        "categories": categories,
    }
    return render(request, "seller/orders.html", context=context)




@login_required(login_url="/app/login")
@allow_seller
def seller_order_detail(request, id):
    user = request.user
    seller = Seller.objects.get(user=user)


    instance = get_object_or_404(Order, id=id, sellers=seller)
    categories = Category.objects.filter(parent__isnull=True)

    context = {
        "title": "Orders | Seller Dashboard",
        "sub_title": "Orders",
        "name": "Order Details",
        "instance": instance,
        "categories": categories,
    }
    return render(request, "seller/order.html", context=context)





@login_required(login_url="/app/login")
@allow_seller
def seller_order_accept(request, id):
    instance = get_object_or_404(Order, id=id)
    instance.order_status = 'IP'
    instance.save()
    return HttpResponseRedirect(reverse("seller:order", kwargs={'id': instance.id}))


@login_required(login_url="/app/login")
@allow_seller
def seller_order_reject(request, id):
    instance = get_object_or_404(Order, id=id)
    instance.order_status = 'CA'
    instance.save()
    return HttpResponseRedirect(reverse("seller:order", kwargs={'id': instance.id}))


@login_required(login_url="/app/login")
@allow_seller
def seller_order_dispatched(request, id):
    instance = get_object_or_404(Order, id=id)
    instance.order_status = 'DI'
    instance.save()
    return HttpResponseRedirect(reverse("seller:order", kwargs={'id': instance.id}))


@login_required(login_url="/app/login")
@allow_seller
def seller_order_completed(request, id):
    instance = get_object_or_404(Order, id=id)
    instance.order_status = 'CO'
    instance.save()
    return HttpResponseRedirect(reverse("seller:order", kwargs={'id': instance.id}))




@login_required(login_url="/app/login")
@allow_seller
def seller_products(request):
    user = request.user
    categories = Category.objects.filter(parent__isnull=True)
    seller = Seller.objects.get(user=user)
    instances = Product.objects.filter(seller=seller)

    context = {
        "title": "Products | Dashboard",
        "sub_title": "Products",
        "name": "Products List",
        "instances": instances,
        "categories": categories,
    }
    return render(request, "seller/products.html", context)


@login_required(login_url="/app/login")
@allow_seller
def seller_products_add(request):
    seller = Seller.objects.get(user=request.user)
    user = request.user

    if request.method == "POST":

        form = ProductForm(request.POST, request.FILES, user=user)
        if form.is_valid():
            product = form.save(commit=False)
            product.seller = seller
            product.save()
            form.save_m2m()
            return HttpResponseRedirect(reverse("seller:products"))
        else:
            message = "Form is not valid"
    else:

        form = ProductForm(user=user)
        message = None

    context = {
        "title": "Seller Dashboard | Add Product",
        "sub_title": "Products",
        "name": "Add Product",
        "form": form,
        "message": message,
    }
    return render(request, "seller/products-add.html", context)


@login_required(login_url="/app/login")
@allow_seller
def seller_products_edit(request, id):
    seller = Seller.objects.get(user=request.user)
    user = request.user

    instance = Product.objects.get(id=id, seller=seller)

    if request.method == "POST":

        form = ProductForm(request.POST, request.FILES, instance=instance, user=user)
        if form.is_valid():
            form.save()
            return HttpResponseRedirect(reverse("seller:products"))
        else:
            message = "Form is not valid"
    else:

        form = ProductForm(instance=instance, user=user)
        message = None

    context = {
        "title": "Seller Dashboard | Edit Product",
        "sub_title": "Products",
        "name": "Edit Product",
        "form": form,
        "message": message,
        "instance": instance,
    }
    return render(request, "seller/products-add.html", context)


@login_required(login_url="/app/login")
@allow_seller
def seller_products_delete(request, id):
    seller = Seller.objects.get(user=request.user)
    instance = Product.objects.get(id=id, seller=seller)
    instance.delete()
    return HttpResponseRedirect(reverse("seller:products"))



@login_required(login_url="/app/login")
@allow_seller
def seller_product_images(request):
    seller = Seller.objects.get(user=request.user)
    instances = ProductImage.objects.filter(product__seller=seller).order_by('-id')
    context = {
        "title": "Product Images | Seller Dashboard",
        "sub_title": "Product Images",
        "name": "Product Images List",
        "instances": instances,
    }
    return render(request, "seller/product-images.html", context)


@login_required(login_url="/app/login")
@allow_seller
def seller_product_images_add(request):

    seller = get_object_or_404(Seller, user=request.user)
    user = request.user

    if request.method == "POST":

        form = ProductImageForm(request.POST, request.FILES, user=user)
        if form.is_valid():
            product_image = form.save(commit=False)

            if product_image.product.seller == seller:
                product_image.save()
                form.save_m2m()
                messages.success(request, "Product image added successfully.")
                return redirect('seller:seller_product_images')
            else:

                messages.error(request, "Selected product does not belong to you.")

                context = {
                    "title": "Add Product Image",
                    "sub_title": "Product Images",
                    "name": "Add Product Image",
                    "form": form,
                }
                return render(request, "seller/product-images-add.html", context)
        else:
             messages.error(request, "Please correct the errors below.")
    else:

        form = ProductImageForm(user=user)

    context = {
        "title": "Add Product Image",
        "sub_title": "Product Images",
        "name": "Add Product Image",
        "form": form,
    }
    return render(request, "seller/product-images-add.html", context)



@login_required(login_url="/app/login")
@allow_seller
def seller_product_images_edit(request, id):
    seller = get_object_or_404(Seller, user=request.user)
    user = request.user

    instance = get_object_or_404(ProductImage, id=id, product__seller=seller)

    if request.method == "POST":

        form = ProductImageForm(request.POST, request.FILES, instance=instance, user=user)
        if form.is_valid():
            product_image = form.save(commit=False)

            if product_image.product.seller == seller:
                product_image.save()
                form.save_m2m()
                messages.success(request, "Product image updated successfully.")
                return redirect('seller:seller_product_images')
            else:
                messages.error(request, "Selected product does not belong to you.")

                context = {
                    "title": "Edit Product Image",
                    "sub_title": "Product Images",
                    "name": "Edit Product Image",
                    "form": form,
                    "instance": instance,
                }
                return render(request, "seller/product-images-add.html", context)
        else:
             messages.error(request, "Please correct the errors below.")
    else:

        form = ProductImageForm(instance=instance, user=user)

    context = {
        "title": "Edit Product Image",
        "sub_title": "Product Images",
        "name": "Edit Product Image",
        "form": form,
        "instance": instance,
    }
    return render(request, "seller/product-images-add.html", context)


@login_required(login_url="/app/login")
@allow_seller
def seller_product_images_delete(request, id):
    seller = Seller.objects.get(user=request.user)
    instance = get_object_or_404(ProductImage, id=id, product__seller=seller)
    instance.delete()
    messages.success(request, "Product image deleted successfully.")
    return redirect('seller:seller_product_images')


@login_required(login_url="/app/login")
@allow_seller
def seller_colors(request):
    user = request.user
    instances = Color.objects.filter(created_by=user)

    context = {
        "title": "Colors | Dashboard",
        "sub_title": "Colors",
        "name": "Colors List",
        "instances": instances,
    }
    return render(request, "seller/colors.html", context=context)


@login_required(login_url="/app/login")
@allow_seller
def seller_colors_add(request):
    user = request.user

    if request.method == "POST":
        form = ColorForm(request.POST)
        if form.is_valid():
            color = form.save(commit=False)
            color.created_by = user
            color.save()
            messages.success(request, "Color added successfully.")
            return redirect('seller:colors')
        else:
            message = "There was an error adding the color."
            context = {
                "title": "Add Color",
                "sub_title": "Colors",
                "name": "Add Color",
                "error": True,
                "message": message,
                "form": form
            }
            return render(request, "seller/colors-add.html", context=context)

    form = ColorForm()
    context = {"title": "Add Color", "sub_title": "Colors", "name": "Add Color", "form": form}
    return render(request, "seller/colors-add.html", context=context)


@login_required(login_url="/app/login")
@allow_seller
def seller_colors_edit(request, id):
    user = request.user
    instance = get_object_or_404(Color, id=id, created_by=user)

    if request.method == "POST":
        form = ColorForm(request.POST, instance=instance)
        if form.is_valid():
            form.save()
            messages.success(request, "Color updated successfully.")
            return redirect('seller:colors')
        else:
            message = "There was an error updating the color."
            context = {
                "title": "Edit Color",
                "sub_title": "Colors",
                "name": "Edit Color",
                "error": True,
                "message": message,
                "form": form,
                "instance": instance
            }
            return render(request, "seller/colors-add.html", context=context)

    form = ColorForm(instance=instance)
    context = {"title": "Edit Color", "sub_title": "Colors", "name": "Edit Color", "form": form, "instance": instance}
    return render(request, "seller/colors-add.html", context=context)


@login_required(login_url="/app/login")
@allow_seller
def seller_colors_delete(request, id):
    user = request.user
    instance = get_object_or_404(Color, id=id, created_by=user)
    instance.delete()
    messages.success(request, "Color deleted successfully.")
    return redirect('seller:colors')




@login_required(login_url="/app/login")
@allow_seller
def seller_options(request):
    user = request.user
    instances = Option.objects.filter(created_by=user).order_by('-id')

    context = {
        "title": "Options | Dashboard",
        "sub_title": "Options",
        "name": "Options List",
        "instances": instances,
    }
    return render(request, "seller/options.html", context)

@login_required(login_url="/app/login")
@allow_seller
def seller_options_add(request):
    user = request.user

    if request.method == "POST":

        form = OptionForm(request.POST, user=user)
        if form.is_valid():
            option = form.save(commit=False)
            option.created_by = user

            try:
                seller = user.seller
                if option.product.seller == seller:
                    option.save()
                    messages.success(request, "Option added successfully.")
                    return HttpResponseRedirect(reverse("seller:options"))
                else:

                    messages.error(request, "Selected product does not belong to you.")

                    context = {"title": "Add Option", "sub_title": "Options", "name": "Add Option", "form": form}
                    return render(request, "seller/options-add.html", context)
            except (AttributeError, Seller.DoesNotExist):
                 messages.error(request, "Seller profile not found for user.")
                 context = {"title": "Add Option", "sub_title": "Options", "name": "Add Option", "form": form}
                 return render(request, "seller/options-add.html", context)

        else:


            messages.error(request, "Please correct the errors below.")
            context = {
                "title": "Add Option",
                "sub_title": "Options",
                "name": "Add Option",


                "form": form,
            }
            return render(request, "seller/options-add.html", context)


    form = OptionForm(user=user)
    context = {
        "title": "Add Option",
        "sub_title": "Options",
        "name": "Add Option",
        "form": form,
    }
    return render(request, "seller/options-add.html", context)

@login_required(login_url="/app/login")
@allow_seller
def seller_options_edit(request, id):
    user = request.user

    instance = get_object_or_404(Option, id=id, created_by=user)

    if request.method == "POST":

        form = OptionForm(request.POST, instance=instance, user=user)
        if form.is_valid():

            option = form.save(commit=False)
            try:
                seller = user.seller
                if option.product.seller == seller:
                     option.save()
                     messages.success(request, "Option updated successfully.")
                     return HttpResponseRedirect(reverse("seller:options"))
                else:
                     messages.error(request, "Selected product does not belong to you.")

                     context = {"title": "Edit Option", "sub_title": "Options", "name": "Edit Option", "form": form, "instance": instance}
                     return render(request, "seller/options-add.html", context)
            except (AttributeError, Seller.DoesNotExist):
                 messages.error(request, "Seller profile not found for user.")
                 context = {"title": "Edit Option", "sub_title": "Options", "name": "Edit Option", "form": form, "instance": instance}
                 return render(request, "seller/options-add.html", context)
        else:


            messages.error(request, "Please correct the errors below.")
            context = {
                "title": "Edit Option",
                "sub_title": "Options",
                "name": "Edit Option",


                "form": form,
                "instance": instance,
            }
            return render(request, "seller/options-add.html", context)


    form = OptionForm(instance=instance, user=user)
    context = {
        "title": "Edit Option",
        "sub_title": "Options",
        "name": "Edit Option",
        "form": form,
        "instance": instance,
    }
    return render(request, "seller/options-add.html", context)

@login_required(login_url="/app/login")
@allow_seller
def seller_options_delete(request, id):
    user = request.user
    instance = get_object_or_404(Option, id=id, created_by=user)
    instance.delete()
    return HttpResponseRedirect(reverse("seller:options"))



@login_required(login_url="/app/login")
@allow_seller
def seller_custom_specifications(request):
    categories = Category.objects.filter(parent__isnull=True)
    instances = CustomSpecification.objects.filter(created_by=request.user).order_by('-id')

    # Handle form submission for adding new custom specification
    if request.method == "POST":
        name = request.POST.get('name')
        key = request.POST.get('key')
        value = request.POST.get('value')

        if name and key and value:
            try:
                spec = CustomSpecification(
                    name=name,
                    key=key,
                    value=value,
                    created_by=request.user
                )
                spec.save()
                messages.success(request, "Custom specification added successfully.")
                return redirect('seller:custom_specifications')
            except Exception as e:
                messages.error(request, f"Error adding custom specification: {str(e)}")
        else:
            messages.error(request, "Please fill in all fields.")

    context = {
        "title": "Custom Specifications | Dashboard",
        "sub_title": "Custom Specifications",
        "name": "Custom Specifications List",
        "instances": instances,
        "categories": categories,
    }
    return render(request, "seller/custom-specifications.html", context)


@login_required(login_url="/app/login")
@allow_seller
def seller_custom_specifications_add(request):
    if request.method == "POST":
        form = CustomSpecificationForm(request.POST)
        if form.is_valid():
            spec = form.save(commit=False)
            spec.created_by = request.user
            spec.save()
            return HttpResponseRedirect(reverse("seller:custom_specifications"))
        else:
            messages.error(request, "Please correct the form errors.")
    else:
        form = CustomSpecificationForm()
    
    # Filter product queryset for the seller
    form.fields['product'].queryset = Product.objects.filter(seller=request.user.seller)

    context = {
        "title": "Seller Dashboard | Add Custom Specification",
        "sub_title": "Custom Specifications",
        "name": "Add Custom Specification",
        "form": form,
    }
    return render(request, "seller/custom-specifications-add.html", context)


@login_required(login_url="/app/login")
@allow_seller
def seller_custom_specifications_edit(request, id):
    instance = get_object_or_404(CustomSpecification, id=id, created_by=request.user)

    if request.method == "POST":
        form = CustomSpecificationForm(request.POST, instance=instance)
        if form.is_valid():
            spec = form.save(commit=False)
            spec.updated_by = request.user
            spec.save()
            return HttpResponseRedirect(reverse("seller:custom_specifications"))
        else:
            message = generate_form_errors(form)
    else:
        form = CustomSpecificationForm(instance=instance)
        message = None

    context = {
        "title": "Seller Dashboard | Edit Custom Specification",
        "sub_title": "Custom Specifications",
        "name": "Edit Custom Specification",
        "form": form,
        "instance": instance,
        "message": message,
    }
    return render(request, "seller/custom-specifications-add.html", context=context)


@login_required(login_url="/app/login")
@allow_seller
def seller_custom_specifications_delete(request, id):
    instance = get_object_or_404(CustomSpecification, id=id, created_by=request.user)
    instance.delete()
    return HttpResponseRedirect(reverse("seller:custom_specifications"))




@login_required(login_url="/app/login")
@allow_seller
def seller_specs(request):
    seller = Seller.objects.get(user=request.user)
    categories = Category.objects.filter(parent__isnull=True)
    instances = Spec.objects.filter(product__seller=seller).order_by('-id')

    context = {
        "title": "Top Features | Dashboard",
        "sub_title": "Top Features",
        "name": "Top Features List",
        "instances": instances,
        "categories": categories,
    }
    return render(request, "seller/specs.html", context=context)



@login_required(login_url="/app/login")
@allow_seller
def seller_specs_add(request):
    user = request.user

    if request.method == "POST":

        form = SpecForm(request.POST, request.FILES, user=user)
        if form.is_valid():
            spec = form.save(commit=False)
            spec.created_by = user


            try:
                seller = user.seller
                if spec.product.seller == seller:
                    spec.save()
                    messages.success(request, "Spec added successfully.")
                    return HttpResponseRedirect(reverse("seller:specs"))
                else:
                    messages.error(request, "Selected product does not belong to you.")

            except (AttributeError, Seller.DoesNotExist):
                 messages.error(request, "Seller profile not found.")


        else:


            messages.error(request, "Please correct the errors below.")

    else:

        form = SpecForm(user=user)



    context = {
        "title": "Seller Dashboard | Add Spec",
        "sub_title": "Specs",
        "name": "Add Spec",
        "form": form,

    }
    return render(request, "seller/specs-add.html", context)


@login_required(login_url="/app/login")
@allow_seller
def seller_specs_edit(request, id):
    user = request.user

    try:
        seller = user.seller
    except (AttributeError, Seller.DoesNotExist):
        messages.error(request,"Seller profile not found.")
        return redirect('seller:specs')


    instance = get_object_or_404(Spec, id=id, product__seller=seller)

    if request.method == "POST":

        form = SpecForm(request.POST, request.FILES, instance=instance, user=user)
        if form.is_valid():
            spec = form.save(commit=False)

            if spec.product.seller == seller:
                spec.updated_by = user
                spec.save()
                messages.success(request, "Spec updated successfully.")
                return HttpResponseRedirect(reverse("seller:specs"))
            else:
                 messages.error(request, "Selected product does not belong to you.")

        else:


            messages.error(request, "Please correct the errors below.")


    else:

        form = SpecForm(instance=instance, user=user)



    context = {
        "title": "Seller Dashboard | Edit Spec",
        "sub_title": "Specs",
        "name": "Edit Spec",
        "form": form,
        "instance": instance,

    }
    return render(request, "seller/specs-add.html", context)



@login_required(login_url="/app/login")
@allow_seller
def seller_specs_delete(request, id):
    seller = Seller.objects.get(user=request.user)
    instance = get_object_or_404(Spec, id=id, product__seller=seller)
    instance.delete()
    return HttpResponseRedirect(reverse("seller:specs"))







@login_required
@allow_seller
def seller_add_product_wizard_step1_details(request):
    try:
        seller = request.user.seller
    except Seller.DoesNotExist:
        messages.error(request, "Seller profile not found.")
        return redirect('managers:index')

    if request.method == 'POST':
        form = ProductForm(request.POST, request.FILES, user=request.user)
        if form.is_valid():
            product = form.save(commit=False)
            product.seller = seller

            product.save()
            form.save_m2m()
            messages.success(request, 'Product details saved. Step 2: Add Colors.')

            return redirect('managers:seller_add_product_wizard_step2_colors', product_id=product.id)
        else:
             messages.error(request, 'Please correct the errors below.')
    else:
        form = ProductForm(user=request.user)

    context = {
        'form': form,
        'title': 'Add New Product - Step 1',
        'sub_title': 'Add Product Details',
        'name': 'Product Details'
    }
    return render(request, 'seller/wizard/step1_details.html', context)


@login_required
@allow_seller
def seller_add_product_wizard_step2_colors(request, product_id):
    try:
        seller = request.user.seller
        product = get_object_or_404(Product, id=product_id, seller=seller)
    except Seller.DoesNotExist:
        messages.error(request, "Seller profile not found.")
        return redirect('managers:index')
    except Product.DoesNotExist:
        messages.error(request, "Product not found or you don't have permission.")
        return redirect('managers:products')

    form = ColorForm()

    if request.method == 'POST':

        form = ColorForm(request.POST)
        if form.is_valid():
            try:
                color = form.save(commit=False)
                color.created_by = request.user
                color.save()
                messages.success(request, f'Color "{color.name}" added successfully.')

                return redirect('managers:seller_add_product_wizard_step2_colors', product_id=product.id)
            except Exception as e:
                messages.error(request, f'Error saving color: {e}')
        else:
            messages.error(request, 'Error adding color. Please check the form.')



    colors_added = Color.objects.filter(created_by=request.user)

    context = {
        'product': product,
        'form': form,
        'colors_added': colors_added,
        'title': 'Add New Product - Step 2',
        'sub_title': 'Add Colors',
        'name': 'Product Colors'
    }
    return render(request, 'seller/wizard/step2_colors.html', context)



@login_required
@allow_seller
def seller_add_product_wizard_step3_options(request, product_id):
    try:
        seller = get_object_or_404(Seller, user=request.user)
        product = get_object_or_404(Product, id=product_id, seller=seller)
    except Seller.DoesNotExist:
        messages.error(request, "Seller profile not found.")
        return redirect('managers:index')
    except Product.DoesNotExist:
        messages.error(request, "Product not found or you don't have permission.")
        return redirect('managers:products')

    form = OptionFormWizard()


    if request.method == 'POST':

        form = OptionFormWizard(request.POST)

        if 'color' in form.fields:
             form.fields['color'].queryset = Color.objects.filter(created_by=request.user)

        if form.is_valid():
            try:
                instance = form.save(commit=False)
                instance.product = product
                instance.created_by = request.user
                instance.save()
                messages.success(request, f'Variant "{instance.name or instance.pk}" added.')

                return redirect('managers:seller_add_product_wizard_step3_options', product_id=product.id)
            except Exception as e:
                 messages.error(request, f'Error adding variant: {e}')
        else:
            messages.error(request, "Error adding variant. Please check the form below.")



    options_added = Option.objects.filter(product=product)

    if 'color' in form.fields:
         form.fields['color'].queryset = Color.objects.filter(created_by=request.user)

    context = {
        'product': product,
        'form': form,
        'options_added': options_added,
        'title': 'Add New Product - Step 3',
        'sub_title': 'Add Variants',
        'name': 'Product Variants'
    }
    return render(request, 'seller/wizard/step3_options.html', context)


@login_required
@allow_seller
def seller_add_product_wizard_step4_images(request, product_id):
    try:
        seller = get_object_or_404(Seller, user=request.user)
        product = get_object_or_404(Product, id=product_id, seller=seller)
    except Seller.DoesNotExist:
        messages.error(request, "Seller profile not found.")
        return redirect('managers:index')
    except Product.DoesNotExist:
        messages.error(request, "Product not found or you don't have permission.")
        return redirect('managers:products')


    form = ProductImageFormWizard()


    if 'product' in form.fields:
        form.fields['product'].required = False


    product_options = Option.objects.filter(product=product)
    if 'variant' in form.fields:
        form.fields['variant'].queryset = product_options

    if request.method == 'POST':

        form = ProductImageFormWizard(request.POST, request.FILES)


        if 'product' in form.fields:
            form.fields['product'].required = False


        if 'variant' in form.fields:
            form.fields['variant'].queryset = product_options

        if form.is_valid():
            try:
                instance = form.save(commit=False)
                instance.product = product

                instance.save()
                form.save_m2m()
                messages.success(request, 'Image added successfully.')

                return redirect('managers:seller_add_product_wizard_step4_images', product_id=product.id)
            except Exception as e:
                messages.error(request, f'Error adding image: {e}')
        else:


            messages.error(request, "Error adding image. Please check the form below.")



    images_added = ProductImage.objects.filter(product=product)

    context = {
        'product': product,
        'form': form,
        'images_added': images_added,
        'title': 'Add New Product - Step 4',
        'sub_title': 'Add Images',
        'name': 'Product Images'
    }

    return render(request, 'seller/wizard/step4_images.html', context)



@login_required
@allow_seller
def seller_add_product_wizard_step5_specs(request, product_id):
    try:
        seller = get_object_or_404(Seller, user=request.user)
        product = get_object_or_404(Product, id=product_id, seller=seller)
    except Seller.DoesNotExist:
        messages.error(request, "Seller profile not found.")
        return redirect('managers:index')
    except Product.DoesNotExist:
        messages.error(request, "Product not found or you don't have permission.")
        return redirect('managers:products')


    form = SpecForm()



    if 'product' in form.fields:
        form.fields['product'].required = False






    if request.method == 'POST':

        if 'add_spec_button' in request.POST:
            form = SpecForm(request.POST, request.FILES)



            if 'product' in form.fields:
                form.fields['product'].required = False






            if form.is_valid():
                try:
                    instance = form.save(commit=False)
                    instance.product = product
                    instance.created_by = request.user
                    instance.save()

                    messages.success(request, 'Top feature added successfully.')

                    return redirect('managers:seller_add_product_wizard_step5_specs', product_id=product.id)
                except Exception as e:
                    messages.error(request, f'Error adding top feature: {e}')
            else:
                messages.error(request, "Error adding top feature. Please check the form below.")
        else:

             pass


    specs_added = Spec.objects.filter(product=product)

    context = {
        'product': product,
        'form': form,
        'specs_added': specs_added,
        'title': 'Add New Product - Step 5',
        'sub_title': 'Add Top Features',
        'name': 'Product Top Features'
    }
    return render(request, 'seller/wizard/step5_specs.html', context)




@login_required
@allow_seller
def delete_wizard_color(request, product_id, pk):

    try:
        instance = get_object_or_404(Color, pk=pk, created_by=request.user)
        instance.delete()
        messages.success(request, 'Color deleted.')
    except Exception as e:
        messages.error(request, f'Error deleting color: {e}')

    return redirect('managers:seller_add_product_wizard_step2_colors', product_id=product_id)

@login_required
@allow_seller
def delete_wizard_option(request, product_id, pk):
    try:
        instance = get_object_or_404(Option, pk=pk, product_id=product_id, product__seller=request.user.seller)
        instance.delete()
        messages.success(request, 'Variant deleted.')
    except Exception as e:
        messages.error(request, f'Error deleting variant: {e}')
    return redirect('managers:seller_add_product_wizard_step3_options', product_id=product_id)

@login_required
@allow_seller
def delete_wizard_image(request, product_id, pk):
    try:
        instance = get_object_or_404(ProductImage, pk=pk, product_id=product_id, product__seller=request.user.seller)
        instance.delete()
        messages.success(request, 'Image deleted.')
    except Exception as e:
        messages.error(request, f'Error deleting image: {e}')
    return redirect('managers:seller_add_product_wizard_step4_images', product_id=product_id)

@login_required
@allow_seller
def delete_wizard_spec(request, product_id, pk):
    try:
        instance = get_object_or_404(Spec, pk=pk, product_id=product_id, product__seller=request.user.seller)
        instance.delete()
        messages.success(request, 'Top feature deleted.')
    except Exception as e:
        messages.error(request, f'Error deleting top feature: {e}')
    return redirect('managers:seller_add_product_wizard_step5_specs', product_id=product_id)


@login_required
@allow_seller
def seller_add_product_wizard_step6_custom_specs(request, product_id):
    try:
        seller = get_object_or_404(Seller, user=request.user)
        product = get_object_or_404(Product, id=product_id, seller=seller)
    except (Seller.DoesNotExist, Product.DoesNotExist):
        messages.error(request, "Product not found or you don't have permission.")
        return redirect('seller:products')

    if request.method == 'POST':
        keys = request.POST.getlist('key')
        values = request.POST.getlist('value')

        # Loop through all submitted key-value pairs
        for key, value in zip(keys, values):
            # Only create a spec if both key and value are provided
            if key and value:
                CustomSpecification.objects.create(
                    product=product,
                    key=key,
                    value=value,
                    created_by=request.user
                    # The 'name' field will be blank as per our model, or you can auto-generate it
                )

        messages.success(request, 'Specifications have been updated.')
        return redirect('managers:seller_add_product_wizard_step6_custom_specs', product_id=product.id)

    specs_added = CustomSpecification.objects.filter(product=product)
    context = {
        'product': product,
        'specs_added': specs_added,
        'title': 'Add New Product - Step 6',
        'sub_title': 'Add Custom Specifications',
        'name': 'Product Specifications'
    }
    return render(request, 'seller/wizard/step6_custom_specs.html', context)


@login_required
@allow_seller
def delete_wizard_custom_spec(request, product_id, pk):
    try:
        instance = get_object_or_404(
            CustomSpecification, 
            pk=pk, 
            product_id=product_id, 
            product__seller=request.user.seller
        )
        instance.delete()
        messages.success(request, 'Specification deleted successfully.')
    except Exception as e:
        messages.error(request, f'Error deleting specification: {str(e)}')
    return redirect('managers:seller_add_product_wizard_step6_custom_specs', product_id=product_id)
