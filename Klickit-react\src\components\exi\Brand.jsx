import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useParams, Link, useNavigate } from 'react-router-dom';
import Header from '../includes/Header';
import LoadingSpinner from '../includes/LoadingSpinner';
import '../../index.css';
import { BASE_URL } from "../../utils/config";

const Brand = () => {
    const { id } = useParams();
    const [pageData, setPageData] = useState({
        brand: null,
        products: [],
        all_brands: [],
        cart_count: 0
    });
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const navigate = useNavigate();

    useEffect(() => {
        const fetchData = async () => {
            try {
                const response = await axios.get(`${BASE_URL}/brand-page/${id}/`);

                // Validate and normalize API response
                const normalizedData = {
                    brand: response.data.brand || null,
                    products: Array.isArray(response.data.products) ? response.data.products : [],
                    all_brands: Array.isArray(response.data.all_brands) ? response.data.all_brands : [],
                    cart_count: response.data.cart_count || 0
                };

                if (response.data.title) {
                    document.title = response.data.title;
                }
                setPageData(normalizedData);
                setError(null);
            } catch (error) {
                setError('Failed to load brand data. Please try again later.');
                console.error('API Error:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [id]);

    if (loading) return <LoadingSpinner />;

    if (error) {
        return <div className="text-center py-8 text-red-500">{error}</div>;
    }

    return (
        <div className="">
            <Header cartCount={pageData.cart_count} />

            <div className="md:mt-[100px] mt-[150px] ml-5">
                <p className="text-[18px] font-semibold mb-7 flex items-center">
                    <button
                        onClick={() => navigate('/')}
                        className="hover:text-purple-500 font-semibold text-[18px] flex items-center"
                    >
                        <i className='text-[20px] md:text-[20px] mr-2 font-bold bx bx-arrow-back'></i>
                    </button>
                    {pageData.brand?.name || 'Brand Details'}
                </p>
            </div>

            <div className="flex justify-start w-full overflow-x-auto scrollbar-hide mb-8 px-5">
                {pageData.all_brands?.map?.(brand => (
                    <div key={brand?.id} className="group rounded-lg bg-white p-2 md:p-3 flex flex-col items-center min-w-[90px] md:min-w-[125px] mr-5">
                        <button
                            onClick={() => navigate(`/brand-page/${brand?.id}`)}
                            className="flex flex-col items-center w-full"
                        >
                            <div className="rounded-full p-[10px] mb-2 border border-gray-300 group-hover:scale-105 transition-transform">
                                <img
                                    src={brand.image && brand.image.startsWith('http')
                                      ? brand.image
                                      : brand.image
                                        ? `${BASE_URL}${brand.image}`
                                        : '/fallback-image.png'}
                                    alt={brand?.name || 'Brand image'}
                                    className="w-[30px] md:w-[40px] h-[30px] md:h-[40px] imgg"
                                    onError={(e) => {
                                        e.target.src = '/fallback-image.png';
                                        e.target.alt = 'Image not available';
                                    }}
                                />
                            </div>
                            <p className="text-xs text-center">{brand?.name}</p>
                        </button>
                    </div>
                )) ?? <p className="text-gray-500">No brands available</p>}
            </div>

            <section className="py-6 md:py-14 px-5">
                <div className="wrapper">
                    {pageData.products?.length ? (
                        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 xl:grid-cols-5 gap-6">
                            {pageData.products.map(product => (
                                <div
                                className="min-w-[150px] md:min-w-[200px] loalo px-[2px] max-w-[150px] md:max-w-[200px] shadow-lg rounded-xl relative snap-center"
                                key={product.id}
                              >
                                <a
                                  onClick={() => navigate(`/product/${product.id}`)}
                                  className="cursor-pointer block"
                                >
                                  <img
                                    src={product.mainImage && product.mainImage.startsWith('http')
                                      ? product.mainImage
                                      : product.mainImage && product.mainImage.startsWith('/media')
                                        ? `${BASE_URL}${product.mainImage}`
                                        : '/placeholder-product.jpg'}
                                    alt={product.name}
                                    className="responsive-image"
                                    onError={(e) => {
                                      e.target.src = '/placeholder-product.jpg';
                                      e.target.classList.add('object-contain', 'p-4');
                                    }}
                                  />
                                </a>

                                <div className="p-3">
                                  <h3 className="text-[15px] font-normal mb-1 product-name-truncate">
                                    <a
                                      onClick={() => navigate(`/product/${product.id}`)}
                                      className="cursor-pointer hover:text-blue-600"
                                      title={product.name}
                                    >
                                      {product.name}
                                    </a>
                                  </h3>
                                  <div className="flex items-center gap-2">
                                    {product.regular_price && (
                                      <span className="text-[10px] line-through text-gray-500">
                                        {product.regular_price}AED
                                      </span>
                                    )}
                                    <span className="text-[14px] font-semibold">
                                      {product.sale_price}AED
                                    </span>
                                  </div>
                                </div>
                              </div>
                            ))}
                        </div>
                    ) : (
                        <div className="text-center py-8">
                            <p className="text-gray-500">No products found for this brand</p>
                        </div>
                    )}
                </div>
            </section>

        </div>
    );
};

export default Brand;
