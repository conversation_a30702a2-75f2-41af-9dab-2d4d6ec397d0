from django.db import models


from main.models import CommonModel
from managers.models import Seller


class Category(CommonModel):
    name = models.CharField(max_length=100)
    image = models.ImageField(upload_to='category', null=True, blank=True)
    parent = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='subcategories'
    )


    class Meta:
        db_table = 'items_category'
        verbose_name ='category'
        verbose_name_plural='categories'
        ordering = ('id',)

    def __str__(self):
        return self.name
    

class Brand(CommonModel):
    name = models.CharField(max_length=100)
    image = models.ImageField(upload_to='brand', null=True, blank=True)


    class Meta:
        db_table = 'items_brand'
        verbose_name ='brand'
        verbose_name_plural='brands'
        ordering = ('id',)

    def __str__(self):
        return self.name
    


class Color(CommonModel):
    seller = models.ForeignKey(Seller, on_delete=models.CASCADE, null=True, blank=True)
    color = models.CharField(max_length=50)
    name = models.CharField(max_length=50)
    class Meta:
        db_table = 'items_color'
        verbose_name ='color'
        verbose_name_plural='colors'
        ordering = ('-id',)

    def __str__(self):
        return self.name

    




class Product(CommonModel):
    seller = models.ForeignKey(Seller, on_delete=models.CASCADE, related_name="products")
    name = models.CharField(max_length=100)  
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    brand = models.ForeignKey(Brand, on_delete=models.SET_NULL, blank=True, null=True)

    class Meta:
        db_table = 'items_product'
        verbose_name = 'product'
        verbose_name_plural = 'products'
        ordering = ('-id',)

    def __str__(self):
        return self.name

class Attribute(CommonModel):
    name = models.CharField(max_length=100, unique=True, help_text="e.g., RAM, Storage, Processor")

    class Meta:
        db_table = 'items_attribute'
        verbose_name = 'Attribute'
        verbose_name_plural = 'Attributes'
        ordering = ('name',)

    def __str__(self):
        return self.name



class ProductAttributeValue(CommonModel):
    variant = models.ForeignKey('ProductVariant', on_delete=models.CASCADE, related_name="attribute_values")
    attribute = models.ForeignKey(Attribute, on_delete=models.CASCADE, related_name="values")
    value = models.CharField(max_length=255, help_text="e.g., 16GB, 512GB, Snapdragon 8 Gen 2")

    class Meta:
        db_table = 'items_product_attribute_value'
        unique_together = ('variant', 'attribute') 
        verbose_name = 'Product Attribute Value'
        verbose_name_plural = 'Product Attribute Values'

    def __str__(self):
        return f"{self.variant.name} - {self.attribute.name}: {self.value}"        


class ProductVariant(CommonModel):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name="variants")
    name = models.CharField(max_length=255) 
    
    
    color = models.ForeignKey(Color, on_delete=models.SET_NULL, blank=True, null=True)
    attributes = models.ManyToManyField(Attribute, through=ProductAttributeValue, related_name="variants")

    
    sku = models.CharField(max_length=100, unique=True, blank=True, null=True)
    product_model = models.CharField(max_length=100, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    details = models.TextField(blank=True, null=True)
    mainimage = models.ImageField(upload_to='variant_images/', blank=True, null=True)
    video = models.URLField(blank=True, null=True)
    
    regular_price = models.FloatField()
    sale_price = models.FloatField(blank=True, null=True)
    stock = models.IntegerField(default=0)
    
    sales_count = models.IntegerField(default=0, blank=True, null=True)
    delivery_title = models.CharField(max_length=100, blank=True, null=True)
    delivery_duration = models.CharField(max_length=100, blank=True, null=True)
    garantee_title = models.CharField(max_length=100, blank=True, null=True)
    garantee_time = models.CharField(max_length=100, blank=True, null=True)

    class Meta:
        db_table = 'items_product_variant'
        verbose_name = 'Product Variant'
        verbose_name_plural = 'Product Variants'
        ordering = ('id',)

    def __str__(self):
        return self.name or f"{self.product.name} Variant"
    



class ProductImage(CommonModel):
    variant = models.ForeignKey(ProductVariant, on_delete=models.CASCADE, related_name="images")
    image = models.ImageField(upload_to='product_variant_images')
    alt_text = models.CharField(max_length=255, blank=True, null=True)
    class Meta:
        db_table = "items_productimages"
        verbose_name = "productimage"
        verbose_name_plural = "productimages"
        ordering = ["id"]
    def __str__(self):
        return self.alt_text or "Product Image"


class CustomSpecification(CommonModel):
    variant = models.ForeignKey(ProductVariant, on_delete=models.CASCADE, related_name="custom_specs")
    name = models.CharField(max_length=100, null=True, blank=True)
    key = models.CharField(max_length=100)
    value = models.TextField()
    class Meta:
        db_table = 'items_specification'
        verbose_name = 'specification'
        verbose_name_plural = 'specifications'
        ordering = ('-id',)
    def __str__(self):
        return self.key
    



class IconImage(CommonModel):
    image = models.ImageField(upload_to="products")
    name = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        db_table = "items_iconimages"
        verbose_name = "iconimage"
        verbose_name_plural = "iconimages"
        ordering = ["id"]

    def __str__(self):
        return self.name
    


class Spec(CommonModel):
    variant = models.ForeignKey(ProductVariant, on_delete=models.CASCADE, related_name="specs")
    image = models.ForeignKey(IconImage, on_delete=models.CASCADE)
    detail = models.CharField(max_length=50, blank=True, null=True)
    class Meta:
        db_table = "items_specs"
        verbose_name = "spec"
        verbose_name_plural = "specs"
        ordering = ["id"]
    def __str__(self):
        return self.detail
    


