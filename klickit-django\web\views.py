from django.shortcuts import render, reverse, redirect,get_object_or_404, HttpResponse
from django.contrib.auth import authenticate, login as auth_login, logout as auth_logout, update_session_auth_hash
from rest_framework.views import APIView
from rest_framework.permissions import BasePermission
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view, permission_classes
from rest_framework import status, permissions
from rest_framework.response import Response
from django.db.models import Sum, Avg, Count, Q
from rest_framework.permissions import AllowAny, IsAuthenticated
from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.utils.encoding import force_bytes, force_str
from rest_framework_simplejwt.tokens import RefreshToken
from django.core.mail import send_mail
from django.utils.timezone import now
from datetime import timedelta
from django.http import JsonResponse
from collections import defaultdict
from django.db import IntegrityError
from users.models import User, OTPVerifier
from customers.models import *
from promos.models import *
from main.models import *
from users.serializers import *
from items.serializers import *
from customers.serializers import *
from customers.serializers import OrderSerializer
from promos.serializers import *
from decimal import Decimal
from main.decorators import allow_customer
from main.webhooks import send_order_confirmation_email
import random
import logging
from django.conf import settings
import stripe
stripe.api_key = settings.STRIPE_SECRET_KEY
logger = logging.getLogger(__name__)
from django.db import transaction
from django.core.exceptions import ValidationError

# Import necessary modules for HTML to PDF
from django.template.loader import render_to_string
from django.http import HttpResponse
from xhtml2pdf import pisa
import io
from datetime import datetime
from django.utils import timezone # Import timezone
from decimal import Decimal # Ensure Decimal is imported

# Import reportlab specific modules
from reportlab.pdfgen import canvas

# Import for Stripe verification
import logging
logger = logging.getLogger(__name__)
from reportlab.lib.pagesizes import letter
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.enums import TA_RIGHT, TA_LEFT, TA_CENTER
from django.contrib.staticfiles import finders
import os








class IndexAPIView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        
        # Fetch all products initially
        products = Product.objects.all()     

        # Serialize the filtered and sorted products
        product_serializer = ProductSerializer(products, many=True, context={'request': request})

        sliders = Slider.objects.all().order_by('-id')
        slider_serializer = SliderSerializer(sliders, many=True, context={'request': request})
        

        logger = logging.getLogger(__name__)
        logger.info("Received request: %s", request)
        
        
        categories = Category.objects.filter(parent__isnull=True)
        category_serializer = CategorySerializer(categories, many=True, context={'request': request})

        brands = Brand.objects.all()
        brand_serializer = BrandSerializer(brands, many=True, context={'request': request})

        latest_offer = Offer.objects.all()
        offer_serializer = OfferSerializer(latest_offer, many=True, context={'request': request})

        last_offer = Offers.objects.last()
        offers_serializer = OffersSerializer(last_offer, context={'request': request}) if last_offer else None


        instances = Product.objects.all()[:10]
        product_serializer = ProductSerializer(instances, many=True, context={'request': request})

        
        best_selling_products = list(
            Product.objects.annotate(sales_count_annotation=Sum('orderitem__quantity'))
            .order_by('-sales_count_annotation')[:10]
            .values()
        )
        for product in best_selling_products:
            product["mainImage"] = request.build_absolute_uri(settings.MEDIA_URL + product["mainimage"])
            product["regularPrice"] = product.get("regular_price")
            product["salePrice"] = product.get("sale_price")
        
        
        
        cart_count = 0
        cart_items = []
        cart_products = []
        if request.user.is_authenticated:
            customer = Customer.objects.filter(user=request.user).first()
            if customer:
                cart_items = list(
                    CartItem.objects.filter(customer=customer).values(
                        'id', 'product__name', 'quantity', 'price'
                    )
                )
                cart_products = list(
                    Product.objects.filter(cartitem__customer=customer).values(
                        'id', 'name', 'sale_price'
                    )
                )                
                cart_count = CartItem.objects.filter(customer=customer).count()
                

        


        
        return Response({
            "title": "Neumoon - Home",
            "slider": slider_serializer.data,  
            "message": "Sliders fetched successfully",
            "categories": category_serializer.data,
            "offers": offer_serializer.data,
            "last_offer": offers_serializer.data,
            "brands": brand_serializer.data,
            "instances": product_serializer.data,
            "best_selling_products": best_selling_products,
            "count": cart_count,
            "cart_items": cart_items,
            "cart_products": cart_products,
        }, status=200)
    
# --------------------------------------------------------------------------------------------------------------------------------

@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def product(request, id):
    logger.info(f"Fetching product details for product ID: {id}")
    try:
        product = get_object_or_404(Product, id=id)
        logger.info(f"Product found: {product.name}")

        user = request.user
        customer = None
        wishlist_items_pks = [] # Correct variable name
        is_in_wishlist = False
        has_ordered = False
        is_in_cart = False
        cart_count = 0

        # --- Wishlist, Cart, Order status calculation moved earlier ---
        if user.is_authenticated:
            try:
                 customer = Customer.objects.filter(user=user).first()
                 if customer:
                     has_ordered = Order.objects.filter(customer=customer, items__product=product).exists()
                     is_in_cart = CartItem.objects.filter(customer=customer, product=product).exists()
                     wishlist_items_qs = Whishlist.objects.filter(customer=customer)
                     wishlist_items_pks = list(wishlist_items_qs.values_list('product_id', flat=True)) # Use pks variable
                     is_in_wishlist = product.id in wishlist_items_pks
                     cart_count = CartItem.objects.filter(customer=customer).count()
            except Exception as e_cust:
                 logger.error(f"Error fetching customer data for user {user.id}: {e_cust}")
        # -------------------------------------------------------------

        # --- Build common customer data dictionary ONCE ---
        customer_data = {
            "id": customer.id if customer else None,
            "has_ordered": has_ordered,
            "is_in_cart": is_in_cart,
            "is_in_wishlist": is_in_wishlist,
            "wishlist_items": wishlist_items_pks, # Use correct variable
            "cart_count": cart_count,
        }
        # -------------------------------------------

        related_products_qs = Product.objects.filter(
            Q(category=product.category) & ~Q(id=product.id))[:10]
        
        related_products_data = []
        for rel_product in related_products_qs:
            image_url = None
            if rel_product.mainimage: 
                try:
                    image_url = request.build_absolute_uri(rel_product.mainimage.url)
                except ValueError:
                    
                    image_url = None

            related_products_data.append({
                'id': rel_product.id,
                'name': rel_product.name,
                'sale_price': rel_product.sale_price,
                'regular_price': rel_product.regular_price,
                'mainimage_url': image_url 
            })


        reviews = Review.objects.filter(product=product)
        avg_rating = reviews.aggregate(Avg('rating'))['rating__avg'] or 0
        review_count = reviews.aggregate(Count('id'))['id__count'] or 0

        options = Option.objects.filter(product=product).select_related("color")

        # Prepare image list (common logic)
        main_image_obj = product.mainimage
        main_image = {"url": request.build_absolute_uri(main_image_obj.url), "alt_text": product.name, "type": "image"} if main_image_obj else None
        video_thumb = {"url": product.video, "alt_text": "Product Video", "type": "video"} if product.video else None

        # If no options exist
        if not options.exists():
            selected_price = product.sale_price
            regular_price = product.regular_price
            stock_available = product.stock > 0 if product.stock is not None else False
            images_qs = ProductImage.objects.filter(product=product).distinct() # Use a different variable name
            other_images = [{"url": request.build_absolute_uri(img.image.url), "alt_text": img.alt_text or product.name, "type": "image"} for img in images_qs]
            image_list = [img for img in [main_image, video_thumb] if img] + other_images

            response_data = {
                "title": f"{product.name} - Neumoon",
                "product": ProductDetailSerializer(product, context={'request': request}).data,
                "customer": customer_data, # Use the common dictionary
                "related_products": related_products_data,
                "reviews": ReviewSerializer(reviews, many=True).data,
                "rating": {"avg_rating": round(avg_rating, 1), "review_count": review_count},
                "pricing": {"selected_price": selected_price, "regular_price": regular_price},
                "stock_available": stock_available,
                "images": image_list,
                "options": None
            }
            return Response(response_data, status=status.HTTP_200_OK)

        # If options exist
        else:
            # (Keep the complex logic for finding matching option based on GET params)
            selected_storage_value = request.GET.get("storage_value")
            selected_ram_value = request.GET.get("ram_value")
            color_id_str = request.GET.get("color_id")
            first_option = options.first()
            selected_storage_value = selected_storage_value if selected_storage_value is not None else (first_option.storage if first_option else None)
            selected_ram_value = selected_ram_value if selected_ram_value is not None else (first_option.ram if first_option else None)

            current_combo_options = options.filter(storage=selected_storage_value, ram=selected_ram_value)
            available_colors = {opt.color.id for opt in current_combo_options if opt.color}
            has_null_color_option = current_combo_options.filter(color__isnull=True).exists()

            selected_color_id = None
            try:
                 if color_id_str is not None:
                     color_id_int = int(color_id_str)
                     if color_id_int in available_colors: selected_color_id = color_id_int
                     else:
                          if not has_null_color_option and available_colors: selected_color_id = next(iter(available_colors), None)
                 else:
                      if not has_null_color_option and available_colors: selected_color_id = next(iter(available_colors), None)
            except (ValueError, TypeError):
                 if not has_null_color_option and available_colors: selected_color_id = next(iter(available_colors), None)

            filter_params = {'product': product, 'storage': selected_storage_value, 'ram': selected_ram_value}
            if selected_color_id is not None: filter_params['color_id'] = selected_color_id
            else: filter_params['color__isnull'] = True
            matching_option = options.filter(**filter_params).first() or first_option

            # Determine price, stock, images based on matching_option
            if matching_option:
                 selected_price = matching_option.sale_price
                 regular_price = matching_option.regular_price
                 stock_available = matching_option.stock > 0
                 variant_images_qs = ProductImage.objects.filter(variant=matching_option).distinct()
                 images_qs = variant_images_qs if variant_images_qs.exists() else ProductImage.objects.filter(product=product).distinct()
            else: # Should not happen if options exist, but as fallback
                 selected_price = product.sale_price
                 regular_price = product.regular_price
                 stock_available = product.stock > 0 if product.stock is not None else False
                 images_qs = ProductImage.objects.filter(product=product).distinct()

            # Recalculate image_list based on determined images_qs
            other_images = [{"url": request.build_absolute_uri(img.image.url), "alt_text": img.alt_text or product.name, "type": "image"} for img in images_qs]
            image_list = [img for img in [main_image, video_thumb] if img] + other_images

            # Prepare options data for frontend (using the original 'options' queryset)
            grouped_options_temp = defaultdict(lambda: defaultdict(set))
            for option in options:
                 if option.color: grouped_options_temp[option.storage][option.ram].add(option.color)
            grouped_options = {s: {r: [{"id": c.id, "name": c.name} for c in cols if c] for r, cols in rs.items()} for s, rs in grouped_options_temp.items()}

            unique_colors_data = []
            if available_colors: # Use the already calculated available_colors for the *current* combo
                 valid_color_objects = Color.objects.filter(id__in=available_colors)
                 color_map = {c.id: c.name for c in valid_color_objects}
                 unique_colors_data = [{"id": c_id, "name": color_map.get(c_id)} for c_id in available_colors if c_id in color_map]

            unique_storages = list({option.storage for option in options if option.storage})
            unique_rams = list({option.ram for option in options if option.ram})

            # !!! Redundant wishlist query REMOVED !!!

            response_data = {
                "title": f"{product.name} - Neumoon",
                "product": ProductDetailSerializer(product, context={'request': request}).data,
                "customer": customer_data, # <-- Use the common dictionary HERE
                "related_products": related_products_data,
                "reviews": ReviewSerializer(reviews, many=True).data,
                "rating": {"avg_rating": round(avg_rating, 1), "review_count": review_count},
                "images": image_list,
                "options": {
                    "unique_colors": unique_colors_data,
                    "unique_storages": unique_storages,
                    "unique_rams": unique_rams,
                    "grouped_options": grouped_options,
                },
                "pricing": {"selected_price": selected_price, "regular_price": regular_price},
                "stock_available": stock_available,
            }
            return Response(response_data, status=status.HTTP_200_OK)

    except Product.DoesNotExist:
         logger.warning(f"Product with ID {id} not found during processing.")
         return Response({"error": "Product not found."}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"Error in product view (ID: {id}): {str(e)}", exc_info=True)
        return Response({"error": "An unexpected error occurred."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


    
# --------------------------------------------------------------------------------------------------------------------------------

class CartAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        customer = get_object_or_404(Customer, user=request.user)
        cart_items = CartItem.objects.filter(customer=customer)

        total_price = Decimal(sum(Decimal(item.total_price()) for item in cart_items))
        discount_amount = Decimal("0.00")
        best_offer = None
        out_of_stock_items = []
        if total_price > 500:
            delivery_charge = Decimal("0.00")
        else:
            delivery_charge = Decimal("25")

        for item in cart_items:
            if item.option and item.option.stock == 0:
                out_of_stock_items.append(f"{item.product.name} - {item.option.name}")
            elif not item.option and item.product.stock == 0:
                out_of_stock_items.append(item.product.name)

        cart_total, created = CartTotal.objects.get_or_create(
            customer=customer,
            defaults={
                'item_total': total_price,
                'total': total_price - discount_amount + delivery_charge,
                'offer': discount_amount,
                'delivery': delivery_charge,
            }
        )

        if not created:
            cart_total.item_total = total_price
            cart_total.total = total_price - discount_amount + delivery_charge
            cart_total.offer = discount_amount
            cart_total.save()

        cart_count = CartItem.objects.filter(customer=customer).aggregate(total=Sum('quantity'))['total'] or 0

        return Response({
            "title": "Your Cart - Neumoon",
            "cart_count": cart_count,
            "cart_items": CartItemSerializer(cart_items, many=True, context={'request': request}).data,
            "total_price": str(total_price),
            "total_amount_to_pay": str(cart_total.total),
            "discount_amount": str(discount_amount),
            "delivery_charge": str(delivery_charge),
            "best_offer": best_offer,
            "out_of_stock_items": out_of_stock_items,
        }, status=status.HTTP_200_OK)

    def post(self, request):
        customer = get_object_or_404(Customer, user=request.user)
        cart_items = CartItem.objects.filter(customer=customer)

        total_price = Decimal(sum(Decimal(item.total_price()) for item in cart_items))
        discount_amount = Decimal("0.00")
        best_offer = None
        code = request.data.get('code', '').strip()

        if any(item.option and item.option.stock == 0 or not item.option and item.product.stock == 0 for item in cart_items):
            return Response({"error": "Some items in your cart are out of stock. Please remove them to proceed."},
                            status=status.HTTP_400_BAD_REQUEST)

        try:
            offer = Coupon.objects.get(code=code)
            if not offer.is_valid():
                return Response({"error": f"Coupon '{code}' is invalid or has expired."},
                                status=status.HTTP_400_BAD_REQUEST)

            discount = (offer.discount_value / Decimal("100")) * total_price if offer.is_Percentage else offer.discount_value
            if discount > discount_amount:
                discount_amount = discount
                best_offer = {"id": offer.id, "code": offer.code, "discount_value": str(discount_amount)}

        except Coupon.DoesNotExist:
            return Response({"error": f"Coupon '{code}' is invalid or has expired."},
                            status=status.HTTP_400_BAD_REQUEST)

        total_amount_to_pay = total_price - discount_amount

        cart_total, created = CartTotal.objects.get_or_create(
            customer=customer,
            defaults={
                'item_total': total_price,
                'total': total_amount_to_pay,
                'offer': discount_amount,
                'delivery': Decimal("0.00"),
            }
        )

        if not created:
            cart_total.item_total = total_price
            cart_total.total = total_amount_to_pay
            cart_total.offer = discount_amount
            cart_total.save()

        # Get the full cart data
        full_cart_data = get_full_cart_data(customer)
        
        # Add applied coupon details to the response
        if best_offer:
            full_cart_data["applied_coupon"] = {
                "code": best_offer["code"],
                "discount_value": best_offer["discount_value"]
            }
            
        return Response(full_cart_data, status=status.HTTP_200_OK)
    
# --------------------------------------------------------------------------------------------------------------------------------


class CheckoutAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        customer = get_object_or_404(Customer, user=request.user)
        addresses = Address.objects.filter(customer=customer)
        addres_serializer = AddressSerializer(addresses, many=True)
        cart_items = CartItem.objects.filter(customer=customer)
        cart_bill = CartTotal.objects.get(customer=customer)

        return Response({
            "title": "Checkout - Neumoon",
            "success": True,
            "order_completed": False,
            "addresses": addres_serializer.data,
            "cart_items": [
                {
                    "id": item.id,
                    "product": {
                        "id": item.product.id,
                        "name": item.product.name,
                        "price": str(item.product.sale_price),
                    },
                    "quantity": item.quantity,
                    "total_price": str(item.price),
                    "option": {
                        "id": item.option.id,
                        "name": item.option.name,
                        "stock": item.option.stock,
                    } if item.option else None,
                }
                for item in cart_items
            ],
            "subtotal": str(cart_bill.item_total or 0),
            "discount": str(cart_bill.offer or 0),
            "delivery": str(cart_bill.delivery or 0),
            "total": str(cart_bill.total or 0),
        }, status=status.HTTP_200_OK)

    @transaction.atomic
    def post(self, request):                
        customer = get_object_or_404(Customer, user=request.user)        
        cart_items = CartItem.objects.filter(customer=customer)        
        cart_bill = CartTotal.objects.get(customer=customer)        

        # Validate minimum order amount
        if cart_bill.total < Decimal('10.00'):  # Minimum order amount
            return Response({
                "success": False,
                "message": "Minimum order amount is AED 10.00"
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate stock availability
        for item in cart_items:
            if item.option:
                if item.option.stock < item.quantity:
                    return Response({
                        "success": False,
                        "message": f"Insufficient stock for {item.product.name} - {item.option.name}"
                    }, status=status.HTTP_400_BAD_REQUEST)
            else:
                if item.product.stock < item.quantity:
                    return Response({
                        "success": False,
                        "message": f"Insufficient stock for {item.product.name}"
                    }, status=status.HTTP_400_BAD_REQUEST)
                

        address_id = request.data.get("address_id")
        first_name = request.data.get('first_name')
        last_name = request.data.get('last_name')
        email = request.data.get('email')
        phone_number = request.data.get('phone_number')
        payment_method_from_request = request.data.get('payment_method')

        try:
            address = Address.objects.get(id=address_id, customer=customer)

            if payment_method_from_request == 'COD':
                # --- COD Specific Logic ---
                print("------- COD Checkout POST Request Started -------")
                # Ensure COD is allowed for this order total (backend validation)
                if cart_bill.total > Decimal('1000.00'):
                    return Response({
                        "success": False,
                        "message": "Cash on Delivery is not available for orders above 1000 AED."
                    }, status=status.HTTP_400_BAD_REQUEST)

                # Create order with 'Placed' status for COD
                order = Order.objects.create(
    customer=customer,
    address=address,
    order_id=f"ORD{Order.objects.count() + 1}",
    sub_total=cart_bill.item_total,
    total=cart_bill.total,
    offer=cart_bill.offer,
    delivery_charge=cart_bill.delivery,
    first_name=first_name,
    last_name=last_name,
    email=email,
    phone_number=phone_number,
    order_status="PL",  # Directly to Placed for COD
    payment_status="PA",  # Payment Pending
    address1=address.address1,
    address2=address.address2,
    city=address.city,
    state=address.state,
    pincode=address.pincode,
    address_type=address.address_type or None,
    payment_method="COD",
)
                print(f"COD Order created: {order.order_id}")

                # Create OrderItems, deduct stock, add sellers for COD
                for item in cart_items:
                    order_item = OrderItem.objects.create(
                        customer=customer,
                        seller=item.seller,
                        product=item.product,
                        quantity=item.quantity,
                        amount=item.price,
                        option=item.option,
                    )
                    order.items.add(order_item)

                    # Deduct stock
                    if item.option:
                        item.option.stock -= item.quantity
                        item.option.save()
                    else:
                        item.product.stock -= item.quantity
                        item.product.save()
                print(f"OrderItems created and stock deducted for COD order {order.order_id}")

                # Add sellers to order
                sellers_in_cart = list(cart_items.values_list("seller", flat=True).distinct())
                valid_sellers = [s_id for s_id in sellers_in_cart if s_id is not None]
                if valid_sellers:
                    order.sellers.add(*valid_sellers)
                print(f"Sellers added to COD order {order.order_id}")
                
                # Clear cart
                cart_items.delete()
                print(f"Cart cleared for COD order {order.order_id}")

                # Send order confirmation email for COD
                send_order_confirmation_email(order)

                return Response({
                    "success": True,
                    "message": "Order placed successfully with Cash on Delivery.",
                    "order_id": order.order_id
                }, status=status.HTTP_200_OK)

            else: # For CARD, GPAY, etc.
                print("------- Online Payment (Stripe Hosted Checkout) POST Request Started -------")
                # Create order with initial status for online payments
                order = Order.objects.create(
                    customer=customer,
                    address=address,
                    order_id=f"ORD{Order.objects.count() + 1}",
                    sub_total=cart_bill.item_total,
                    total=cart_bill.total,
                    offer=cart_bill.offer,
                    delivery_charge=cart_bill.delivery,
                    first_name=first_name,
                    last_name=last_name,
                    email=email,
                    phone_number=phone_number,
                    order_status="IN",  # Initial status for online payments
                    payment_status="PA",  # Payment Pending
                    address1=address.address1,
                    address2=address.address2,
                    city=address.city,
                    state=address.state,
                    pincode=address.pincode,
                    address_type=address.address_type or None,
                    payment_method="ONLINE",
                )
                print(f"Order for online payment created: {order.order_id}")

                # Prepare line_items for Stripe
                line_items = []
                for item in cart_items:
                    product_name = f"{item.product.name} ({item.option.name})" if item.option else item.product.name
                    line_items.append({
                        'price_data': {
                            'currency': 'aed',
                            'product_data': {
                                'name': product_name,
                            },
                            'unit_amount': int(item.price * 100),
                        },
                        'quantity': item.quantity,
                    })

                # Create Stripe Checkout Session
                try:
                    session_metadata = {
                        'order_id': order.order_id,
                        'customer_id': str(customer.id)
                    }
                    checkout_session = stripe.checkout.Session.create(
                        payment_method_types=['card'],
                        line_items=line_items,
                        mode='payment',
                        customer_email=email,
                        success_url=f"{settings.FRONTEND_URL}order/success?session_id={{CHECKOUT_SESSION_ID}}",
                        cancel_url=f"{settings.FRONTEND_URL}checkout",
                        metadata=session_metadata,
                        payment_intent_data={
                            'metadata': session_metadata
                        }
                    )
                    print(f"Stripe Checkout Session created: {checkout_session.id}")
                    
                    # Create local Payment record
                    payment = Payment.objects.create(
                        customer=request.user,
                        amount=cart_bill.total,
                        currency='AED',
                        status='requires_payment_method',
                        payment_intent_id=checkout_session.id, # Storing session_id here
                        description=f"Payment for order {order.order_id}",
                        metadata=session_metadata  # Store the same metadata as in Stripe
                    )
                    order.payment = payment
                    order.save()
                    print(f"Payment record {payment.id} created and linked to order {order.order_id}")

                    # Return the session ID to the frontend
                    return Response({'sessionId': checkout_session.id}, status=status.HTTP_200_OK)

                except stripe.error.StripeError as e:
                    order.delete() # Rollback order creation if Stripe fails
                    logger.error(f"Stripe Error in CheckoutAPIView: {str(e)}")
                    return Response({
                        "success": False,
                        "message": f"Payment processing failed: {str(e)}",
                    }, status=status.HTTP_400_BAD_REQUEST)

        except Address.DoesNotExist:
            return Response({
                "success": False,
                "message": "Invalid address selected."
            }, status=status.HTTP_400_BAD_REQUEST)
        except ValidationError as e:
            return Response({
                "success": False,
                "message": str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                "success": False,
                "message": "An unexpected error occurred."
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
# --------------------------------------------------------------------------------------------------------------------------------        

def get_all_subcategories(category):
    """
    Recursively fetch all subcategories of a given category, including itself.
    """
    subcategories = [category]
    for sub in category.subcategories.all():
        subcategories.extend(get_all_subcategories(sub))
    return subcategories

@api_view(['GET'])
@permission_classes([AllowAny])
def category_detail(request, id):    
    category = get_object_or_404(Category, id=id)        
    all_categories = get_all_subcategories(category)        
    products = Product.objects.filter(category__in=all_categories)        
    categories = Category.objects.filter(parent__isnull=True)        
    subcategories = category.subcategories.all()        
    
    # Default cart count
    cart_count = 0

    if request.user.is_authenticated:
        customer = Customer.objects.get(user=request.user)
        cart_count = CartItem.objects.filter(customer=customer).aggregate(
            total=Sum('quantity')
        )['total'] or 0
    
    serializer = {
    'title': f"{category.name} - Neumoon",
    'products': ProductSerializer(products, many=True, context={'request': request}).data,  
    'categories': CategorySerializer(categories, many=True, context={'request': request}).data, 
    'category': CategorySerializer(category, context={'request': request}).data,
    'subcategories': CategorySerializer(subcategories, many=True, context={'request': request}).data,
    'cart_count': cart_count
    }
    
    return Response(serializer)

# --------------------------------------------------------------------------------------------------------------------------------

@api_view(['GET'])
def brand_page(request, id):
    # Get main brand
    brand = get_object_or_404(Brand, id=id)
    
    # Get related data
    products = Product.objects.filter(brand=brand)
    all_brands = Brand.objects.all()
    
    # Cart count logic
    cart_count = 0
    if request.user.is_authenticated:
        customer = Customer.objects.filter(user=request.user).first()
        if customer:
            cart_count = CartItem.objects.filter(customer=customer).aggregate(
                total=Sum('quantity')
            )['total'] or 0

    serializer = {
        'title': f"{brand.name} - Neumoon",
        'brand': BrandSerializer(brand, context={'request': request}).data,
        'products': ProductSerializer(products, many=True, context={'request': request}).data,  
        'all_brands': BrandSerializer(all_brands, many=True).data,
        'cart_count': cart_count
    }

    # Serialize data
    return Response(serializer)
    

# --------------------------------------------------------------------------------------------------------------------------------


class ServiceAPIView(APIView):
    def get(self, request):
        services = Service.objects.all()
        serializer = ServiceSerializer(services, many=True)
        
        cart_count = 0
        if request.user.is_authenticated:
            customer = Customer.objects.filter(user=request.user).first()
            if customer:
                cart_count = CartItem.objects.filter(customer=customer).aggregate(
                    total=Sum('quantity')
                )['total'] or 0
        
        return Response({
            "title": "Services - Neumoon",
            "services": serializer.data,
            "cart_count": cart_count
        })
    
# --------------------------------------------------------------------------------------------------------------------------------    

class RequestServiceAPIView(APIView):
    def post(self, request):
        print("📌 Received Data:", request.data)  # Debugging
        serializer = ServiceRequestSerializer(data=request.data)
        
        if serializer.is_valid():
            service_request = serializer.save()
            
            send_mail("📌 Received Data:", request.data)
            print(f"✅ Saved Request for Service ID: {service_request.service.id}")  # Debugging

            return Response(
                {"message": "Request submitted successfully"},
                status=status.HTTP_201_CREATED
            )
        
        print("❌ Validation Errors:", serializer.errors)  # Debugging
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# --------------------------------------------------------------------------------------------------------------------------------

@csrf_exempt
@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
def remove_from_cart(request, id):
    user = request.user
    customer = get_object_or_404(Customer, user=user)
    cart_item = get_object_or_404(CartItem, id=id, customer=customer)
    cart_item.delete()
    
    # Reset any applied coupon
    cart_total = CartTotal.objects.filter(customer=customer).first()
    if cart_total and (cart_total.offer is not None and cart_total.offer > 0):
        cart_total.offer = Decimal('0.00')
        cart_total.save()
    
    full_cart_data = get_full_cart_data(customer)
    return JsonResponse(full_cart_data, status=200)




def get_full_cart_data(customer):
    cart_items = CartItem.objects.filter(customer=customer)
    cart_bill, _ = CartTotal.objects.get_or_create(customer=customer)

    # Calculate subtotal
    subtotal = sum([item.total_price() for item in cart_items])
    # Use cart_bill.offer and cart_bill.delivery if set, otherwise default to 0
    discount = cart_bill.offer if cart_bill.offer is not None else 0
    delivery = cart_bill.delivery if cart_bill.delivery is not None else 0
    # Always calculate total as subtotal - discount + delivery
    total = subtotal - discount + delivery

    # Update CartTotal to be consistent
    cart_bill.item_total = subtotal
    cart_bill.offer = discount
    cart_bill.delivery = delivery
    cart_bill.total = total
    cart_bill.save()

    # Serialize cart items (let DRF handle Decimal serialization)
    serialized_items = []
    for item in cart_items:
        # Safe access to mainimage.url
        mainimage_url = None
        if hasattr(item.product, 'mainimage') and item.product.mainimage:
            try:
                mainimage_url = item.product.mainimage.url
            except Exception:
                mainimage_url = None
        serialized_items.append({
            "id": item.id,
            "product": {
                "id": item.product.id,
                "name": item.product.name,
                "mainImage": mainimage_url,
            },
            "quantity": item.quantity,
            "price": item.price,  # Let DRF handle Decimal
            "option": {
                "id": item.option.id,
                "name": item.option.name,
                "stock": item.option.stock,
            } if item.option else None
        })

    return {
        "title": "Your Cart - Neumoon",
        "cart_items": serialized_items,
        "total_price": subtotal,
        "discount_amount": discount,
        "delivery_charge": delivery,
        "total_amount_to_pay": total,
    }


class AddToCartAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = request.user
        customer = get_object_or_404(Customer, user=user)

        
        product_id = request.data.get("product_id")
        option_id = request.data.get("option_id")
        
        try:
            quantity = int(request.data.get("quantity", 1))
            if quantity <= 0:
                raise ValueError("Quantity must be positive")
        except (TypeError, ValueError):
            return Response({"message": "Invalid quantity provided."}, status=status.HTTP_400_BAD_REQUEST)

        
        if not product_id:
            return Response({"message": "Product ID is required."}, status=status.HTTP_400_BAD_REQUEST)

        
        try:
            product = Product.objects.get(id=product_id)
        except Product.DoesNotExist:
            return Response({"message": "Product not found."}, status=status.HTTP_404_NOT_FOUND)

        seller = product.seller
        option = None
        price = None
        available_stock = 0

        
        has_options = Option.objects.filter(product=product).exists()

        if has_options:
            
            if not option_id:
                return Response({"message": "Option ID is required for this product."}, status=status.HTTP_400_BAD_REQUEST)

            
            try:
                option = Option.objects.get(id=option_id, product=product)
                price = option.sale_price  
                available_stock = option.stock 
            except Option.DoesNotExist:
                return Response({"message": "Invalid option selected for this product."}, status=status.HTTP_400_BAD_REQUEST)
        else:
            
            if option_id:
                 
                 return Response({"message": "Options are not available for this product."}, status=status.HTTP_400_BAD_REQUEST)
            
            price = product.sale_price
            available_stock = product.stock

        
        if price is None:
             return Response({"message": "Could not determine price for the item."}, status=status.HTTP_400_BAD_REQUEST)

        

        
        existing_cart_item = CartItem.objects.filter(customer=customer, product=product, option=option).first()

        if existing_cart_item:
            new_quantity = existing_cart_item.quantity + quantity
            if available_stock < new_quantity:
                return Response(
                    {"message": f"Insufficient stock. Only {available_stock} items available.",
                     "available_stock": available_stock},
                    status=status.HTTP_400_BAD_REQUEST
                )
            existing_cart_item.quantity = new_quantity
            existing_cart_item.save()
        else:
            if available_stock < quantity:
                 return Response(
                    {"message": f"Insufficient stock. Only {available_stock} items available.",
                     "available_stock": available_stock},
                    status=status.HTTP_400_BAD_REQUEST
                )
            CartItem.objects.create(
                customer=customer,
                product=product,
                option=option,
                seller=seller,
                quantity=quantity,
                price=price 
            )
        
        # Reset any applied coupon
        cart_total = CartTotal.objects.filter(customer=customer).first()
        if cart_total and (cart_total.offer is not None and cart_total.offer > 0):
            cart_total.offer = Decimal('0.00')
            cart_total.save()
            
        full_cart_data = get_full_cart_data(customer)
        return Response(full_cart_data, status=status.HTTP_200_OK)

    
# --------------------------------------------------------------------------------------------------------------------------------

class AllowCustomerPermission(BasePermission):
    def has_permission(self, request, view):
        return allow_customer(request.user)

class WishlistAPI(APIView):
    permission_classes = [IsAuthenticated, AllowCustomerPermission]

    def get(self, request):
        try:
            customer = Customer.objects.get(user=request.user)
        except Customer.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)
            
        wishlist_items = Whishlist.objects.filter(customer=customer)
        cart_count = CartItem.objects.filter(customer=customer).aggregate(
            total=Sum('quantity')
        )['total'] or 0
        
        serializer = WhishlistSerializer(wishlist_items, many=True, context={'request': request})
        return Response({
            "title": "Your Wishlist - Neumoon",
            'cart_count': cart_count,
            'wishlist_items': serializer.data
        })
    
# --------------------------------------------------------------------------------------------------------------------------------

class AddToWishlistAPI(APIView):
    permission_classes = [IsAuthenticated, AllowCustomerPermission]

    def post(self, request, id):
        try:
            customer = Customer.objects.get(user=request.user)
            product = Product.objects.get(id=id)
        except (Customer.DoesNotExist, Product.DoesNotExist):
            return Response(status=status.HTTP_404_NOT_FOUND)

        if Whishlist.objects.filter(customer=customer, product=product).exists():
            return Response(
                {'message': 'Product already in wishlist'},
                status=status.HTTP_400_BAD_REQUEST
            )

        Whishlist.objects.create(customer=customer, product=product)
        return Response(status=status.HTTP_201_CREATED)
    
# --------------------------------------------------------------------------------------------------------------------------------

class RemoveFromWishlistAPI(APIView):
    permission_classes = [IsAuthenticated, AllowCustomerPermission]

    def delete(self, request, id):
        try:
            customer = Customer.objects.get(user=request.user)
            product = Product.objects.get(id=id)
            wishlist_item = Whishlist.objects.get(customer=customer, product=product)
        except (Customer.DoesNotExist, Product.DoesNotExist, Whishlist.DoesNotExist):
            return Response(status=status.HTTP_404_NOT_FOUND)

        wishlist_item.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

class CartUpdateAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, id):
        user = request.user
        customer = get_object_or_404(Customer, user=user)
        cart_item = get_object_or_404(CartItem, id=id, customer=customer)

        action = request.data.get("action")  # "plus" or "minus"

        if action == "plus":
            cart_item.quantity += 1
            cart_item.save()
        elif action == "minus":
            cart_item.quantity -= 1
            if cart_item.quantity <= 0:
                cart_item.delete()
            else:
                cart_item.save()
        
        # Reset any applied coupon
        cart_total = CartTotal.objects.filter(customer=customer).first()
        if cart_total and (cart_total.offer is not None and cart_total.offer > 0):
            cart_total.offer = Decimal('0.00')
            cart_total.save()
            
        # Always return the full cart data after any modification
        full_cart_data = get_full_cart_data(customer)
        return Response(full_cart_data, status=status.HTTP_200_OK)


# --------------------------------------------------------------------------------------------------------------------------------

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def orders_api(request):
    customer = Customer.objects.get(user=request.user)
    orders = Order.objects.filter(customer=customer).exclude(order_status='IN').prefetch_related('items__product')

    cart_count = CartItem.objects.filter(customer=customer).aggregate(total=Sum('quantity'))['total'] or 0

    serialized_orders = OrderSerializer(orders, many=True).data

    return Response({
        "title": "Your Orders - Neumoon",
        "cart_count": cart_count,
        "orders": serialized_orders
    })

# --------------------------------------------------------------------------------------------------------------------------------

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def order_detail(request, id):
    user = request.user
    customer = Customer.objects.get(user=user)
    order = get_object_or_404(Order, id=id, customer=customer)
    
    serializer = OrderSerializer(order)
    return Response({
        "title": f"Order #{order.id} Details - Neumoon",
        "order": serializer.data
    }, status=status.HTTP_200_OK)

# --------------------------------------------------------------------------------------------------------------------------------

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_addresses(request):
    user = request.user
    customer = Customer.objects.get(user=user)
    addresses = Address.objects.filter(customer=customer)
    serializer = AddressSerializer(addresses, many=True)
    return Response({
        "title": "Your Addresses - Neumoon",
        "addresses": serializer.data
    })

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def add_address(request):
    """Add a new address"""
    customer = get_object_or_404(Customer, user=request.user)
    data = request.data.copy()
    data["customer"] = customer.id  # Associate address with the customer
    serializer = AddressSerializer(data=data)
    
    if serializer.is_valid():
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['PUT'])
@permission_classes([IsAuthenticated])
def edit_address(request, id):
    """Edit an existing address"""
    address = get_object_or_404(Address, id=id, customer__user=request.user)
    serializer = AddressSerializer(address, data=request.data, partial=True)
    
    if serializer.is_valid():
        serializer.save()
        return Response(serializer.data)
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


def get_address_by_id(request, id):
    address = get_object_or_404(Address, id=id)
    return JsonResponse({
        "id": address.id,
        "address1": address.address1,
        "address2": address.address2,
        "city": address.city,
        "state": address.state,
        "pincode": address.pincode,
        "address_type": address.address_type,
    })

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_address(request, id):
    """Delete an address"""
    address = get_object_or_404(Address, id=id, customer__user=request.user)
    address.delete()
    return Response({"message": "Address deleted"}, status=status.HTTP_204_NO_CONTENT)



@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_account_data(request):
    user = request.user
    customer = Customer.objects.filter(user=user).first()
    
    cart_count = 0
    if customer:
        cart_count = CartItem.objects.filter(customer=customer).aggregate(total=Sum('quantity'))['total'] or 0
    
    recent_orders = Order.objects.all()[:3]
    
    orders_data = [
        {
            "order_id": order.id,
            "first_product": order.items.first().product.name if order.items.exists() else "No items",
            "item_count": order.items.count(),
            "status": order.get_order_status_display()
        }
        for order in recent_orders
    ]
    
    data = {
        "title": "Your Account - Neumoon",
        "cart_count": cart_count,
        "user": {
            "first_name": user.first_name,
            "last_name": user.last_name,
            "email": user.email,
            "phone_number": user.phone_number if user and hasattr(user, 'phone_number') else "Phone Not Set"
        },
        "recent_orders": orders_data
    }
    
    return Response(data)


# --------------------------------------------------------------------------------------------------------------------------------


class RegisterAPIView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        serializer = RegisterSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            data = serializer.validated_data
            otp = random.randint(100000, 999999)

            # Store all registration data with OTP
            otp_verifier = OTPVerifier.objects.create(
                email=data['email'],
                otp=otp,
                first_name=data['first_name'],
                last_name=data['last_name'],
                phone_number=data.get('phone_number'),
                password=data['password']
            )

            # Send OTP email
            subject = "Your OTP Code"
            message = f"Hello {data['first_name']},\n\nYour OTP code is: {otp}\n\nUse this code to complete your registration."
            recipient_list = [data['email']]

            send_mail(subject, message, settings.EMAIL_HOST_USER, recipient_list)            

            return Response({
                "message": "OTP sent to email",
                "email": data['email']
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


# --------------------------------------------------------------------------------------------------------------------------------

class VerifyOTPAPIView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        serializer = VerifyOTPSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        email = serializer.validated_data['email']
        otp = serializer.validated_data['otp']

        try:
            otp_verifier = OTPVerifier.objects.filter(email=email).latest('created_datetime')
            
            # Validate OTP
            if now() > otp_verifier.created_datetime + timedelta(minutes=5):
                otp_verifier.delete()
                return Response({"error": "OTP expired"}, status=status.HTTP_410_GONE)

            if otp_verifier.otp != otp:
                return Response({"error": "Invalid OTP"}, status=status.HTTP_401_UNAUTHORIZED)

            # Create user with stored data
            user = User.objects.create_user(
                email=email,
                password=otp_verifier.password,
                first_name=otp_verifier.first_name,
                last_name=otp_verifier.last_name,
                phone_number=otp_verifier.phone_number,
                is_customer=True,
                is_active=True
            )

            # Create customer profile
            Customer.objects.create(user=user)

            # Cleanup OTP records
            OTPVerifier.objects.filter(email=email).delete()

            # Generate tokens
            refresh = RefreshToken.for_user(user)
            return Response({
                "access": str(refresh.access_token),
                "refresh": str(refresh),
                "user_id": user.id
            }, status=status.HTTP_201_CREATED)

        except OTPVerifier.DoesNotExist:
            return Response({"error": "OTP not found"}, status=status.HTTP_400_BAD_REQUEST)
        except IntegrityError as e:
            return Response({"error": "User already exists"}, status=status.HTTP_409_CONFLICT)




class ResendOTPAPIView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        email = request.data.get("email")
        if not email:
            return Response({"error": "Email is required"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Get the latest OTP record or create new
            otp_verifier = OTPVerifier.objects.filter(email=email).latest('created_datetime')
            
            # Update the OTP and timestamp
            otp = random.randint(100000, 999999)
            otp_verifier.otp = otp
            otp_verifier.created_datetime = now()
            otp_verifier.save()

            # Send OTP email again
            subject = "Your OTP Code (Resent)"
            message = f"Hello {otp_verifier.first_name},\n\nYour new OTP code is: {otp}\n\nUse this code to complete your registration."
            recipient_list = [email]

            send_mail(subject, message, settings.EMAIL_HOST_USER, recipient_list)

            return Response({
                "message": "OTP resent successfully",
                "email": email
            }, status=status.HTTP_200_OK)

        except OTPVerifier.DoesNotExist:
            return Response({"error": "No OTP request found for this email. Please register first."},
                            status=status.HTTP_404_NOT_FOUND)

        
# --------------------------------------------------------------------------------------------------------------------------------

class LoginAPIView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        serializer = LoginSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data["user"]
            refresh = RefreshToken.for_user(user)
            return Response({
                "access_token": str(refresh.access_token),
                "refresh_token": str(refresh),
            }, status=status.HTTP_200_OK)
        else:
            # Return specific error messages
            return Response({
                "error": "Invalid email or password",
                "details": serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)


# --------------------------------------------------------------------------------------------------------------------------------

class LogoutAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            refresh_token = request.data.get("refresh_token")
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()  # Blacklists the refresh token
            return Response({"message": "Logged out successfully"}, status=200)
        except Exception as e:
            return Response({"error": "Invalid token"}, status=400)

# --------------------------------------------------------------------------------------------------------------------------------


class AddReviewView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, product_id):
        product = get_object_or_404(Product, id=product_id)
        user = request.user
        customer = Customer.objects.get(user=user)

        # Check if the customer has ordered the product
        has_ordered = Order.objects.filter(
            customer=customer,
            items__product=product
        ).exists()
        if not has_ordered:
            return Response(
                {"error": "You can only review products you have purchased."},
                status=status.HTTP_403_FORBIDDEN
            )

        # Check for existing review
        if Review.objects.filter(product=product, user=request.user).exists():
            return Response(
                {"error": "You have already reviewed this product."},
                status=status.HTTP_400_BAD_REQUEST
            )

        serializer = ReviewSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(product=product, user=request.user)
            return Response(
                {"success": "Review added successfully!"},
                status=status.HTTP_201_CREATED
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)




@api_view(['POST'])
def send_reset_email(request):
    email = request.data.get('email')
    try:
        user = User.objects.get(email=email)
        token = default_token_generator.make_token(user)
        uid = urlsafe_base64_encode(force_bytes(user.pk))
        reset_url = f"{settings.FRONTEND_URL}/reset-password/{uid}/{token}/"
        
        subject = "Password Reset Request"
        message = f"Click the link to reset your password: {reset_url}"
        
        send_mail(subject, message, settings.DEFAULT_FROM_EMAIL, [email])
        return Response({'message': 'Password reset link sent check your email'}, status=status.HTTP_200_OK)
    except User.DoesNotExist:
        return Response({'error': 'No account found with this email'}, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
def reset_password_api(request, uidb64, token):
    password = request.data.get('password')
    try:
        uid = force_str(urlsafe_base64_decode(uidb64))
        user = User.objects.get(pk=uid)
        if default_token_generator.check_token(user, token):
            user.set_password(password)
            user.save()
            return Response({'message': 'Password reset successful'}, status=status.HTTP_200_OK)
        else:
            return Response({'error': 'Invalid or expired token'}, status=status.HTTP_400_BAD_REQUEST)
    except User.DoesNotExist:
        return Response({'error': 'Invalid user'}, status=status.HTTP_400_BAD_REQUEST)

class SellerInterestAPIView(APIView):
    """
    API endpoint to log when a user expresses interest in becoming a seller.
    """
    permission_classes = [AllowAny] # Allows both authenticated and anonymous users

    def post(self, request, *args, **kwargs):
        user = request.user if request.user.is_authenticated else None
        user_id = user.id if user else 'anonymous'

        # Log the interest event
        logger.info(f"User '{user_id}' expressed interest in becoming a seller via /seller-interest/ endpoint.")

        # Optional: Add further actions like database storage, admin notifications, etc.
        # Example:
        # if user:
        #     SellerInterestLog.objects.create(user=user)
        # else:
        #     SellerInterestLog.objects.create(session_key=request.session.session_key) # Example for anonymous

        # Return a success response
        # Note: The 'contact_email' in the response isn't strictly needed by the current frontend
        # implementation, but might be useful for future variations.
        return Response({
            'success': True,
            # 'message': 'Interest noted. Our team will contact you soon.',
            'contact_email': '<EMAIL>' # Make sure this is your intended support email
        }, status=status.HTTP_200_OK)
    

def link_callback(uri, rel):
    """
    Convert HTML URIs to absolute system paths so xhtml2pdf can access those
    resources
    """
    result = finders.find(uri)
    if result:
        if not isinstance(result, (list, tuple)):
            result = [result]
        result = list(os.path.realpath(path) for path in result)
        path=result[0]
    else:
        sUrl = settings.STATIC_URL        # Typically /static/
        sRoot = settings.STATIC_ROOT      # Typically /home/<USER>/project_static/
        mUrl = settings.MEDIA_URL         # Typically /media/
        mRoot = settings.MEDIA_ROOT       # Typically /home/<USER>/project_media/

        if uri.startswith(mUrl):
            path = os.path.join(mRoot, uri.replace(mUrl, ""))
        elif uri.startswith(sUrl):
            path = os.path.join(sRoot, uri.replace(sUrl, ""))
        else:
            return uri

    # make sure that file exists
    if not os.path.isfile(path):
        raise Exception(
            'media URI must start with %s or %s' % (sUrl, mUrl)
        )
    return path

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def download_order_invoice(request, order_identifier):
    """Generate and return a PDF invoice for a given order."""
    try:
        # Fetch the order. Use request.user to ensure the user owns the order.
        # Assuming order_id is the field to look up
        order = get_object_or_404(Order, order_id=order_identifier, customer__user=request.user)

        # Prepare data for the template
        context = {
            'order': order, # Pass the full order object
            'invoice_date_formatted': order.created_datetime.strftime('%d %b, %Y'), # Format the date
            'customer_name': order.customer.user.get_full_name() or order.customer.user.username, # Get full name or username
            'customer_email': order.customer.user.email,
            'customer_phone': order.phone_number,
            # Use address fields directly from Order if present, or from related shipping_address
            'shipping_address_line1': order.address1 if hasattr(order, 'address1') and order.address1 else (order.address.address1 if order.address else ''),
            'shipping_address_line2_city_state_pincode': '',
            'payment_method_display': 'N/A',
            'payment_status_invoice': 'N/A',
            'payment_id_invoice': 'N/A',
            'stripe_status_invoice': 'N/A',
            'order_items': order.items.all(), # Assuming 'items' is the related name for OrderItem
            'subtotal_amount': order.sub_total if hasattr(order, 'sub_total') and order.sub_total is not None else Decimal(0),
            'discount_amount': order.offer if hasattr(order, 'offer') and order.offer is not None else Decimal(0),
            'delivery_charge_amount': order.delivery_charge if hasattr(order, 'delivery_charge') and order.delivery_charge is not None else Decimal(0),
            'grand_total_amount': order.total if hasattr(order, 'total') and order.total is not None else Decimal(0),
            'generation_timestamp': timezone.now().strftime('%d %b %Y at %H:%M:%S'),
        }

        # Construct shipping_address_line2_city_state_pincode
        address_parts = []
        # Prioritize order fields if they exist, otherwise use shipping_address fields
        city = order.city if hasattr(order, 'city') and order.city else (order.address.city if order.address else None)
        state = order.state if hasattr(order, 'state') and order.state else (order.address.state if order.address else None)
        pincode = order.pincode if hasattr(order, 'pincode') and order.pincode else (order.address.pincode if order.address else None)
        address2 = order.address2 if hasattr(order, 'address2') and order.address2 else (order.address.address2 if order.address else None)

        if address2: # Add address2 if available
            address_parts.append(address2)
        if city: # Add city if available
            address_parts.append(city)
        state_pincode = [] # Combine state and pincode
        if state: state_pincode.append(state)
        if pincode: state_pincode.append(pincode)
        if state_pincode: address_parts.append(' '.join(state_pincode)) # Add combined state and pincode

        context['shipping_address_line2_city_state_pincode'] = ', '.join(filter(None, address_parts))

        # Determine payment method display
        if hasattr(order, 'payment_method') and order.payment_method:
            if order.payment_method == 'COD':
                context['payment_method_display'] = 'Cash on Delivery'
                context['payment_status_invoice'] = 'To be Paid upon Delivery'
            else:
                # Assuming payment details are in a related 'payment' object
                context['payment_method_display'] = order.payment_method.name if hasattr(order.payment_method, 'name') else order.payment_method # Use name if available, else raw method
                context['payment_status_invoice'] = order.get_payment_status_display() if hasattr(order, 'get_payment_status_display') else order.payment_status

                if hasattr(order, 'payment') and order.payment:
                    context['payment_id_invoice'] = order.payment.payment_intent_id if hasattr(order.payment, 'payment_intent_id') and order.payment.payment_intent_id else 'N/A'
                    context['stripe_status_invoice'] = order.payment_status if hasattr(order.payment, 'status') and order.payment_status else 'N/A'

        # Add unit_price and line_total to each item in order_items
        # Create a new list to avoid modifying the original queryset during iteration
        context['order_items'] = []
        for item in order.items.all():
            item_data = {
                'product_name': item.product.name if hasattr(item, 'product') and hasattr(item.product, 'name') else 'N/A',
                'option_name': item.option.name if hasattr(item, 'option') and item.option and hasattr(item.option, 'name') else None,
                'quantity': item.quantity if hasattr(item, 'quantity') and item.quantity is not None else 0,
                'amount': item.amount if hasattr(item, 'amount') and item.amount is not None else Decimal(0), # Total amount for the line item
            }
            # Calculate unit price
            item_data['unit_price'] = (item_data['amount'] / item_data['quantity']) if item_data['quantity'] > 0 else Decimal(0)
            # Line total is already item.amount
            item_data['line_total'] = item_data['amount']
            context['order_items'].append(item_data)

        # Render the HTML template
        html_string = render_to_string('emails/invoice_template.html', context)

        # Create a file-like buffer to receive PDF data.
        buffer = io.BytesIO()
        
        # Create the PDF object, using the buffer as its "file.".
        # Assuming you want to use xhtml2pdf as indicated in the original query about the template.
        pisa_status = pisa.CreatePDF(html_string.encode('utf-8'), dest=buffer, encoding='utf-8', link_callback=link_callback)

        # If error then show some funy view
        if pisa_status.err:
            logger.error(f"Error generating PDF with xhtml2pdf for order {order_identifier}: {pisa_status.err}")
            return HttpResponse('We had some errors generating the PDF.', status=500)

        # File response
        response = HttpResponse(buffer.getvalue(), content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="invoice_{order_identifier}.pdf"'
        return response
        
    except Order.DoesNotExist:
        logger.error(f"Order not found for identifier {order_identifier} and user {request.user.id}")
        return HttpResponse('Order not found.', status=404)
    except Exception as e:
        logger.error(f"Error in download_order_invoice for order {order_identifier}: {e}", exc_info=True)
        return HttpResponse(f'An unexpected error occurred: {e}', status=500)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def verify_stripe_payment(request):
    try:
        session_id = request.data.get('session_id')
        if not session_id:
            return Response({'success': False, 'message': 'Session ID not provided.'}, status=status.HTTP_400_BAD_REQUEST)

        session = stripe.checkout.Session.retrieve(session_id)

        if session.payment_status == 'paid':
            order_id = session.metadata.get('order_id')
            if order_id:
                try:
                    Order.objects.get(order_id=order_id, customer__user=request.user)
                    return Response({
                        'success': True,
                        'order_id': order_id
                    }, status=status.HTTP_200_OK)
                except Order.DoesNotExist:
                    return Response({'success': False, 'message': 'Order not found.'}, status=status.HTTP_404_NOT_FOUND)
            else:
                return Response({'success': False, 'message': 'Order ID not found in session metadata.'}, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response({'success': False, 'message': 'Payment not successful.'}, status=status.HTTP_400_BAD_REQUEST)

    except stripe.error.StripeError as e:
        logger.error(f"Stripe API Error in verify_stripe_payment: {str(e)}")
        return Response({'success': False, 'message': str(e)}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Unexpected Error in verify_stripe_payment: {str(e)}")
        return Response({'success': False, 'message': 'An unexpected error occurred.'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def handle_successful_payment(request):
    try:
        payment_intent_id = request.data.get('payment_intent_id')
        order_id = request.data.get('order_id')

        if not payment_intent_id or not order_id:
            return Response({
                "success": False,
                "message": "Payment Intent ID and Order ID are required."
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get the payment and order to ensure they exist
        # We are not changing their status here; webhook will do that.
        payment = get_object_or_404(Payment, payment_intent_id=payment_intent_id)
        order = get_object_or_404(Order, order_id=order_id, customer__user=request.user)

        # Optional: You could update the order status to something like 'Awaiting Webhook Confirmation'
        # order.order_status = 'AWC' # Define 'AWC' in your ORDER_STATUS choices
        # order.save()

        # The primary job of this endpoint is now just to acknowledge client-side success.
        # The actual order processing (creating OrderItems, clearing cart, final status)
        # will be handled by the Stripe webhook.
        
        return Response({
            "success": True,
            "message": "Client-side payment success acknowledged. Awaiting final confirmation via webhook.",
            "order_id": order.order_id, # Return order_id for frontend navigation
        }, status=status.HTTP_200_OK)

    except Payment.DoesNotExist:
        return Response({
            "success": False,
            "message": "Payment record not found for the given payment intent ID."
        }, status=status.HTTP_404_NOT_FOUND)
    except Order.DoesNotExist:
        return Response({
            "success": False,
            "message": "Order not found for the given order ID and user."
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            "success": False,
            "message": f"An unexpected error occurred: {str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ProductSearchView(APIView):
    permission_classes = [permissions.AllowAny]

    def get(self, request):
        try:
            queryset = Product.objects.select_related('brand', 'category').prefetch_related('custom_specs').all()

            query = request.GET.get('q')
            category_param = request.GET.get('category')
            brand_param = request.GET.get('brand')
            sort_by = request.GET.get('sort')

            if query:
                queryset = queryset.filter(
                    Q(name__icontains=query) |
                    Q(description__icontains=query) |
                    Q(product_model__icontains=query) |
                    Q(sku__icontains=query) |
                    Q(brand__name__icontains=query) |
                    Q(category__name__icontains=query)
                ).distinct()

            if category_param:
                queryset = queryset.filter(category__name=category_param)

            if brand_param:
                queryset = queryset.filter(brand__name=brand_param)

            if sort_by == 'price_high_to_low':
                queryset = queryset.order_by('-sale_price')
            elif sort_by == 'price_low_to_high':
                queryset = queryset.order_by('sale_price')
            else:
                queryset = queryset.order_by('-created_datetime')

            product_serializer = ProductSerializer(queryset, many=True, context={'request': request})

            categories = Category.objects.filter(parent__isnull=True)
            category_serializer = CategorySerializer(categories, many=True, context={'request': request})

            brands = Brand.objects.all()
            brand_serializer = BrandSerializer(brands, many=True, context={'request': request})

            return Response({
                "title": "Search Results - Neumoon",
                "products": product_serializer.data,
                "categories": category_serializer.data,
                "brands": brand_serializer.data,
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error in ProductSearchView: {e}", exc_info=True)
            return Response({"error": "An unexpected error occurred during search."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SearchSuggestionsView(APIView):
    permission_classes = [permissions.AllowAny]

    def get(self, request):
        query = request.GET.get('q', '').strip()
        if len(query) >= 2:  # Changed from > 2 to >= 2 for better UX
            try:
                # Get unique product names and models that contain the query
                suggestions = list(
                    Product.objects.filter(
                        Q(name__icontains=query) | Q(product_model__icontains=query),
                        is_active=True  # Only show active products
                    ).values_list('name', flat=True)
                    .distinct()[:8]  # Increased from 5 to 8 suggestions
                )

                # Also include brand names that match
                brand_suggestions = list(
                    Brand.objects.filter(
                        name__icontains=query,
                        is_active=True
                    ).values_list('name', flat=True)[:3]
                )
                category_suggestions = list(
                    Category.objects.filter(
                        name__icontains=query,
                        is_active=True
                    ).values_list('name', flat=True)[:3]
                )

                # Combine and limit total suggestions
                all_suggestions = suggestions + brand_suggestions + category_suggestions
                return Response(all_suggestions[:10], status=status.HTTP_200_OK)

            except Exception as e:
                logger.error(f"Error in SearchSuggestionsView: {e}")
                return Response([], status=status.HTTP_200_OK)

        return Response([], status=status.HTTP_200_OK)








@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_addresses_checkout(request):
    user = request.user
    customer = Customer.objects.get(user=user)
    addresses = Address.objects.filter(customer=customer)
    serializer = AddressSerializer(addresses, many=True)
    return Response(serializer.data)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def add_address_checkout(request):
    """Add a new address"""
    customer = get_object_or_404(Customer, user=request.user)
    data = request.data.copy()
    data["customer"] = customer.id  # Associate address with the customer
    serializer = AddressSerializer(data=data)
    
    if serializer.is_valid():
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['PUT'])
@permission_classes([IsAuthenticated])
def edit_address_checkout(request, id):
    """Edit an existing address"""
    address = get_object_or_404(Address, id=id, customer__user=request.user)
    serializer = AddressSerializer(address, data=request.data, partial=True)
    
    if serializer.is_valid():
        serializer.save()
        return Response(serializer.data)
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)