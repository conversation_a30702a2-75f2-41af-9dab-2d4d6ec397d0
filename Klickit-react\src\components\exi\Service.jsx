import React, { useEffect, useState } from 'react';
import axios from 'axios';
import Header2 from '../includes/Header2';
import LoadingSpinner from '../includes/LoadingSpinner';
import { BASE_URL } from "../../utils/config";

const Service = () => {
    const [services, setServices] = useState([]);
    const [selectedService, setSelectedService] = useState(null);
    const [cartCount, setCartCount] = useState(0);
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        phone: '',
        service: '',
        details: ''
    });
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        axios.get(`${BASE_URL}/service/`)
            .then(res => {
                console.log("API Response:", res.data);
                if (res.data.title) {
                    document.title = res.data.title;
                }
                setServices(res.data.services || []);
                setCartCount(res.data.cart_count || 0);
                setLoading(false);
            })
            .catch(err => {
                console.error("Error fetching services:", err);
                setLoading(false);
            });
    }, []);

    if (loading) return <LoadingSpinner />;

    const handleOpenModal = (service) => {
        setSelectedService(service);
        setFormData(prev => ({ ...prev, service: service.id }));
        console.log("🛠 Selected Service:", service);
        setIsModalOpen(true);
        document.body.classList.add('body-no-scroll');
    };

    const handleCloseModal = () => {
        setIsModalOpen(false);
        document.body.classList.remove('body-no-scroll');
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsSubmitting(true);

        const payload = {
            name: formData.name,
            email: formData.email,
            phone: formData.phone,
            service_id: formData.service,
            details: formData.details,
        };

        console.log("📌 Sending Data:", payload);

        try {
            await axios.post(`${BASE_URL}/request/service/`, payload, {
                headers: { 'X-CSRFToken': getCsrfToken(), 'Content-Type': 'application/json' }
            });

            alert('Request submitted successfully!');
            setIsModalOpen(false);
        } catch (err) {
            console.error("❌ Submission Error:", err.response?.data || err.message);
            alert('Submission failed. Please try again.');
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div>
            <Header2 />
            <section className="py-10 md:py-14 mt-[70px]">
                <div className="wrapper">
                    <h2 className="text-3xl font-semibold mb-6 text-center">Our Services</h2>
                    <p className="text-lg text-gray-600 mb-10 text-center">
                        We offer a variety of services to meet your needs and help you achieve your goals.
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {services.length > 0 ? (
                            services.map(service => (
                                <div key={service.id} className="p-6 border border-gray-300 rounded-xl shadow-md">
                                    <img src={`${BASE_URL}${service.image}`} alt={service.title} className="w-full rounded-lg mb-4" />
                                    <h3 className="text-xl font-semibold mb-2">{service.title}</h3>
                                    <p className="text-gray-700 mb-4">{service.description}</p>
                                    <button 
                                        className="text-[#A41E11] font-semibold" 
                                        onClick={() => handleOpenModal(service)}
                                    >
                                        Request Service ↓
                                    </button>
                                </div>
                            ))
                        ) : (
                            <p>Loading services...</p>
                        )}
                    </div>
                </div>
            </section>

            {isModalOpen && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white p-6 rounded-lg shadow-lg w-11/12 sm:w-10/12 md:w-1/2 lg:w-1/3 max-h-[90vh] overflow-y-auto">
                        <h2 className="text-2xl font-semibold mb-4">Request Service: {selectedService?.title}</h2>
                        <form onSubmit={handleSubmit}>
                            <input type="text" name="name" required value={formData.name} onChange={(e) => setFormData({...formData, name: e.target.value})} className="w-full p-2 border rounded-lg mb-4" placeholder="Enter your name" />
                            <input type="email" name="email" value={formData.email} onChange={(e) => setFormData({...formData, email: e.target.value})} className="w-full p-2 border rounded-lg mb-4" placeholder="Enter your email" />
                            <input type="text" name="phone" required value={formData.phone} onChange={(e) => setFormData({...formData, phone: e.target.value})} className="w-full p-2 border rounded-lg mb-4" placeholder="Enter your phone number" />
                            <textarea name="details" required value={formData.details} onChange={(e) => setFormData({...formData, details: e.target.value})} className="w-full p-2 border rounded-lg mb-4" placeholder="Provide details about your request"></textarea>
                            <button type="submit" className="w-full bg-[#A41E11] text-white py-2 rounded-lg font-semibold">
                                {isSubmitting ? 'Submitting...' : 'Submit Request'}
                            </button>
                        </form>
                        <button onClick={handleCloseModal} className="mt-4 w-full bg-gray-200 text-gray-700 py-2 rounded-lg font-semibold">Close</button>
                    </div>
                </div>
            )}
        </div>
    );
};

const getCsrfToken = () => {
    return document.cookie.split('; ').find(row => row.startsWith('csrftoken='))?.split('=')[1];
};

export default Service;
