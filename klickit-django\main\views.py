from django.shortcuts import render
import stripe
from django.conf import settings
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from decimal import Decimal
from .models import Payment

stripe.api_key = settings.STRIPE_SECRET_KEY



from django.shortcuts import render, reverse, redirect,get_object_or_404, HttpResponse
from django.contrib.auth import authenticate, login as auth_login, logout as auth_logout, update_session_auth_hash
from rest_framework.views import APIView
from rest_framework.permissions import BasePermission
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view, permission_classes
from rest_framework import status, permissions
from rest_framework.response import Response
from django.db.models import Sum, Avg, Count, Q
from rest_framework.permissions import AllowAny, IsAuthenticated
from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.utils.encoding import force_bytes, force_str
from rest_framework_simplejwt.tokens import RefreshToken
from django.core.mail import send_mail
from django.utils.timezone import now
from datetime import timedelta
from django.http import JsonResponse
from collections import defaultdict
from django.db import IntegrityError
from users.models import User, OTPVerifier
from customers.models import *
from promos.models import *
from main.models import *
from users.serializers import *
from items.serializers import *
from customers.serializers import *
from customers.serializers import OrderSerializer
from promos.serializers import *
from decimal import Decimal
from main.decorators import allow_customer
import random
import logging
from django.conf import settings
import stripe
stripe.api_key = settings.STRIPE_SECRET_KEY
logger = logging.getLogger(__name__)
from django.db import transaction
from django.core.exceptions import ValidationError


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def create_payment_intent(request):
    try:
        data = request.data
        
        total_amount = Decimal(str(data.get('amount', 0)))
        vat_amount = Decimal('0.00')  
        
        
        amount_in_cents = int(total_amount * 100)
        currency = data.get('currency', 'AED')
        description = data.get('description', '')
        order_id = data.get('order_id', '')

        
        intent = stripe.PaymentIntent.create(
            amount=amount_in_cents,
            currency=currency,
            payment_method_types=['card', 'apple_pay', 'google_pay'],
            description=description,
            metadata={
                'user_id': request.user.id,
                'order_id': order_id,
                'vat_amount': '0.00'  
            }
        )

        
        payment = Payment.objects.create(
            amount=total_amount,  
            vat_amount=vat_amount,  
            total_amount=total_amount,  
            currency=currency,
            payment_intent_id=intent.id,
            status=intent.status,
            customer=request.user,
            description=description,
            metadata={
                'order_id': order_id,
                'vat_included': 'true',  
                'vat_rate': '0.00%'  
            }
        )

        return Response({
            'clientSecret': intent.client_secret,
            'payment_id': payment.id,
            'amount': float(total_amount),  
            'vat_amount': 0.00,  
            'total_amount': float(total_amount)  
        })
    except Exception as e:
        import traceback
        print(f"Error in create_payment_intent: {str(e)}\n{traceback.format_exc()}")
        return Response({'error': 'Failed to create payment intent'}, status=400)








class CreateStripeCheckoutSessionView(APIView):
    permission_classes = [IsAuthenticated]

    @transaction.atomic
    def post(self, request, *args, **kwargs):
        customer = get_object_or_404(Customer, user=request.user)
        cart_items = CartItem.objects.filter(customer=customer)
        cart_bill = get_object_or_404(CartTotal, customer=customer)
        
        
        address_id = request.data.get("address_id")
        if not address_id:
            return Response({"success": False, "message": "Address ID is required."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            address = Address.objects.get(id=address_id, customer=customer)
        except Address.DoesNotExist:
            return Response({"success": False, "message": "Invalid address selected."}, status=status.HTTP_400_BAD_REQUEST)

        
        for item in cart_items:
            stock_available = item.option.stock if item.option else item.product.stock
            if stock_available < item.quantity:
                product_name = f"{item.product.name} - {item.option.name}" if item.option else item.product.name
                return Response({"success": False, "message": f"Insufficient stock for {product_name}"}, status=status.HTTP_400_BAD_REQUEST)

        
        
        order = Order.objects.create(
            customer=customer,
            address=address,
            order_id=f"ORD{Order.objects.count() + 1}",
            sub_total=cart_bill.item_total,
            total=cart_bill.total,
            offer=cart_bill.offer,
            delivery_charge=cart_bill.delivery,
            first_name=request.data.get('first_name', customer.user.first_name),
            last_name=request.data.get('last_name', customer.user.last_name),
            email=request.data.get('email', customer.user.email),
            phone_number=request.data.get('phone_number', customer.user.phone_number),
            order_status="IN",  
            payment_status="PA", 
            payment_method="ONLINE",
            
            address1=address.address1,
            address2=address.address2,
            city=address.city,
            state=address.state,
            pincode=address.pincode,
        )

        
        line_items = []
        for item in cart_items:
            product_name = f"{item.product.name} ({item.option.name})" if item.option else item.product.name
            line_items.append({
                'price_data': {
                    'currency': 'aed',
                    'product_data': {
                        'name': product_name,
                    },
                    'unit_amount': int(item.price * 100),  
                },
                'quantity': item.quantity,
            })

        
        try:
            checkout_session = stripe.checkout.Session.create(
                payment_method_types=['card'],
                line_items=line_items,
                mode='payment',
                customer_email=request.user.email, 
                success_url=f"{settings.FRONTEND_URL}/order/success?session_id={{CHECKOUT_SESSION_ID}}",
                cancel_url=f"{settings.FRONTEND_URL}/checkout",
                metadata={
                    'order_id': order.order_id,
                    'customer_id': customer.id
                }
            )
            
            
            payment = Payment.objects.create(
                customer=request.user,
                amount=cart_bill.total,
                currency='AED',
                status='requires_payment_method', 
                payment_intent_id=checkout_session.id, 
                description=f"Payment for order {order.order_id}"
            )
            order.payment = payment
            order.save()

            return Response({'sessionId': checkout_session.id}, status=status.HTTP_200_OK)

        except Exception as e:
            
            order.delete()
            logger.error(f"Stripe Checkout Session Error: {str(e)}")
            return Response({'success': False, 'message': 'Could not initiate payment. Please try again.'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)








@csrf_exempt
def stripe_webhook(request):
    payload = request.body
    sig_header = request.headers.get('Stripe-Signature')
    endpoint_secret = settings.STRIPE_WEBHOOK_SECRET  

    try:
        event = stripe.Webhook.construct_event(
            payload, sig_header, endpoint_secret
        )
    except ValueError as e:
        
        return HttpResponse(status=400)
    except stripe.error.SignatureVerificationError as e:
        
        return HttpResponse(status=400)

    
    if event['type'] == 'checkout.session.completed':
        session = event['data']['object']
        
        
        if session.payment_status == 'paid':
            order_id = session.metadata.get('order_id')
            customer_id = session.metadata.get('customer_id')
            
            try:
                
                order = Order.objects.get(order_id=order_id, order_status='IN')
                customer = Customer.objects.get(id=customer_id)
                cart_items = CartItem.objects.filter(customer=customer)

                with transaction.atomic():
                    
                    order.order_status = 'PL'  
                    order.payment_status = 'SU' 
                    
                    
                    payment = order.payment
                    if payment:
                        payment.payment_intent_id = session.payment_intent
                        payment.status = 'succeeded'
                        payment.save()
                    
                    order.save()

                    
                    for item in cart_items:
                        order_item = OrderItem.objects.create(
                            customer=customer,
                            seller=item.seller,
                            product=item.product,
                            quantity=item.quantity,
                            amount=item.price,
                            option=item.option,
                        )
                        order.items.add(order_item)

                        if item.option:
                            item.option.stock -= item.quantity
                            item.option.save()
                        else:
                            item.product.stock -= item.quantity
                            item.product.save()

                    sellers_in_cart = list(cart_items.values_list("seller", flat=True).distinct())
                    valid_sellers = [s_id for s_id in sellers_in_cart if s_id is not None]
                    if valid_sellers:
                        order.sellers.add(*valid_sellers)
                    
                    
                    cart_items.delete()
                    CartTotal.objects.filter(customer=customer).delete()
                    
                    logger.info(f"SUCCESS: Order {order_id} fulfilled via webhook.")

            except Order.DoesNotExist:
                logger.error(f"Webhook Error: Order {order_id} not found or already processed.")
            except Exception as e:
                logger.error(f"Webhook Fulfillment Error for order {order_id}: {str(e)}")
                
                return HttpResponse(status=500)

    return HttpResponse(status=200)