# Generated by Django 5.2 on 2025-05-24 15:32

import datetime
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('customers', '0003_cartitem_seller_order_sellers_orderitem_seller'),
        ('main', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='order',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=datetime.datetime(2025, 5, 24, 15, 32, 54, 91291, tzinfo=datetime.timezone.utc)),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='order',
            name='payment',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='main.payment'),
        ),
        migrations.AddField(
            model_name='order',
            name='payment_status',
            field=models.CharField(choices=[('IN', 'Initiated'), ('PL', 'Placed'), ('IP', 'In progress'), ('DI', 'Dispatched'), ('CO', 'Completed'), ('CA', 'Cancelled'), ('PA', 'Payment Pending'), ('PF', 'Payment Failed'), ('PS', 'Payment Successful')], default='PA', max_length=2),
        ),
        migrations.AddField(
            model_name='order',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name='order',
            name='order_status',
            field=models.CharField(choices=[('IN', 'Initiated'), ('PL', 'Placed'), ('IP', 'In progress'), ('DI', 'Dispatched'), ('CO', 'Completed'), ('CA', 'Cancelled'), ('PA', 'Payment Pending'), ('PF', 'Payment Failed'), ('PS', 'Payment Successful')], default='IN', max_length=25),
        ),
    ]
