{% extends "base/admin-base.html" %} 
{% block container %} 
{% load static %}

{% include 'includes/admin-nav.html' %}

<!--**********************************
    Content body start
***********************************-->
<div class="content-body">
    <div class="row page-titles mx-0">
        <div class="col p-md-0">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="javascript:void(0)">{{sub_title}}</a></li>
                <li class="breadcrumb-item active"><a href="javascript:void(0)">{{name}}</a></li>
            </ol>
        </div>
    </div>
    <!-- row -->

    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h4 class="card-title">{{name}}</h4>
                        <div class="basic-form">
                            <form action="" method="post" class="ajax redirect">
                                {% csrf_token %}
                                <div class="form-row">
                                    <div class="form-group col-12 col-md-6">
                                        <label>Attribute Name (e.g., RAM, Storage, Processor)</label>
                                        {{ form.name }}
                                    </div>
                                    <button class="btn login-form__btn submit button" type="submit">Submit</button>
                                </div>
                                {% if error %}
                                    <p>{{ message }}</p>
                                {% endif %}
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- #/ container -->
</div>
<!--**********************************
    Content body end
***********************************-->

{% endblock %}
