{% extends "base/admin-base.html" %}
{% block container %}
{% load static %}

{% include 'includes/seller-nav.html' %}

<div class="content-body">
    <div class="row page-titles mx-0">
        <div class="col p-md-0">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="javascript:void(0)">{{sub_title}}</a></li>
                <li class="breadcrumb-item active"><a href="javascript:void(0)">{{name}}</a></li>
            </ol>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card" style="background-color: #b5c8eb;">
                    <div class="card-body">
                        <h4 class="card-title">{{name}}</h4>

                        {# Display messages #}
                        {% if messages %}
                            <div class="mt-3">
                            {% for message in messages %}
                                <div class="alert {% if message.tags %}alert-{{ message.tags }}{% else %}alert-info{% endif %}" role="alert">{{ message }}</div>
                            {% endfor %}
                            </div>
                        {% endif %}

                         <!-- Disclaimer -->
                         <div class="alert alert-warning" role="alert" style="font-weight: bold;">
                            ⚠️ Please add color options first before adding a variant. Make sure all required colors are created in the <a href="{% url 'seller:colors' %}">color management section</a>.
                        </div>

                        <div class="basic-form">
                            <form action="" method="post" enctype="multipart/form-data" class="ajax redirect">
                                {% csrf_token %}
                                <div class="form-row">
                                    <div class="form-group col-12 col-md-6">
                                        <label>Variant Name</label>
                                        {{ form.name }}
                                    </div>
                                    <div class="form-group col-12 col-md-6">
                                        <label>Product</label>
                                        {{ form.product }}
                                    </div>
                                    <div class="form-group col-12 col-md-6">
                                        <label>Color</label>
                                        {{ form.color }}
                                    </div>
                                    <div class="form-group col-12 col-md-6">
                                        <label>Product Model</label>
                                        {{ form.product_model }}
                                    </div>
                                    <div class="form-group col-12 col-md-6">
                                        <label>SKU</label>
                                        {{ form.sku }}
                                    </div>
                                    <div class="form-group col-12">
                                        <label>Description</label>
                                        {{ form.description }}
                                    </div>
                                    <div class="form-group col-12">
                                        <label>Details</label>
                                        {{ form.details }}
                                    </div>
                                    <div class="form-group col-12 col-md-6">
                                        <label>Main Image</label>
                                        {{ form.mainimage }}
                                    </div>
                                    <div class="form-group col-12 col-md-6">
                                        <label>Regular Price</label>
                                        {{ form.regular_price }}
                                        <small class="form-text text-danger">Enter price including VAT</small>
                                    </div>
                                    <div class="form-group col-12 col-md-6">
                                        <label>Sale Price</label>
                                        {{ form.sale_price }}
                                        <small class="form-text text-danger">Enter price including VAT</small>
                                    </div>
                                    <div class="form-group col-12 col-md-6">
                                        <label>Stock</label>
                                        {{ form.stock }}
                                    </div>
                                    <div class="form-group col-12 col-md-6">
                                        <label>Delivery Title</label>
                                        {{ form.delivery_title }}
                                    </div>
                                    <div class="form-group col-12 col-md-6">
                                        <label>Delivery Duration</label>
                                        {{ form.delivery_duration }}
                                    </div>
                                    <div class="form-group col-12 col-md-6">
                                        <label>Guarantee Title</label>
                                        {{ form.garantee_title }}
                                    </div>
                                    <div class="form-group col-12 col-md-6">
                                        <label>Guarantee Time</label>
                                        {{ form.garantee_time }}
                                    </div>
                                    <div class="form-group col-12 col-md-6">
                                        <label>Video URL</label>
                                        {{ form.video }}
                                    </div>
                                    <div class="form-group col-12">
                                        <button class="btn login-form__btn submit button" type="submit">Submit</button>
                                    </div>
                                </div>
                                {% if error %}
                                    <p>{{ message }}</p>
                                {% endif %}
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
