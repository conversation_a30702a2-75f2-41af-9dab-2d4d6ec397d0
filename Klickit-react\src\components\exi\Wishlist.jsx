import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import Header2 from "../includes/Header2";
import Footer from "../includes/Footer";
import LoadingSpinner from "../includes/LoadingSpinner";
import { BASE_URL } from "../../utils/config";

export default function Wishlist() {
    const [wishlistItems, setWishlistItems] = useState([]);
    const [loading, setLoading] = useState(true);
    const navigate = useNavigate();

    useEffect(() => {
        const fetchWishlistData = async () => {
            try {
                const token = localStorage.getItem("access_token");
                const response = await fetch(`${BASE_URL}/wishlist/`, {
                    headers: {
                        "Authorization": `Bearer ${token}`,
                        "Content-Type": "application/json",
                    },
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || "Failed to fetch wishlist");
                }

                const data = await response.json();
                setWishlistItems(data.wishlist_items || []);
            } catch (err) {
                console.error("Error fetching wishlist:", err.message);
                if (err.message === "Authentication credentials were not provided.") {
                    navigate("/login");
                }
            } finally {
                setLoading(false);
            }
        };
        fetchWishlistData();
    }, [navigate]);

    if (loading) return <LoadingSpinner />;

    const moveToCart = async (id) => {
        try {
            const token = localStorage.getItem("access_token");
            const response = await fetch(`${BASE_URL}/wishlist-to-cart/${id}/`, {
                method: "POST",
                headers: {
                    "Authorization": `Bearer ${token}`,
                    "Content-Type": "application/json",
                },
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || "Failed to move item to cart");
            }

            setWishlistItems(prevItems => prevItems.filter(item => item.product.id !== id));
        } catch (err) {
            console.error("Error moving item to cart:", err.message);
        }
    };

    const removeFromWishlist = async (id) => {
        try {
            const token = localStorage.getItem("access_token");
            const response = await fetch(`${BASE_URL}/wishlist/remove/${id}/`, {
                method: "DELETE",
                headers: {
                    "Authorization": `Bearer ${token}`,
                    "Content-Type": "application/json",
                },
            });

            if (!response.ok && response.status !== 204) {
                let result = {};
                try {
                    result = await response.json();
                } catch (e) {
                    // Handle empty or invalid JSON
                }
                throw new Error(result.error || "Failed to remove item from wishlist");
            }

            // ✅ FIX: filter by item.product.id
            setWishlistItems(prevItems => prevItems.filter(item => item.product.id !== id));
        } catch (err) {
            console.error("Error removing item from wishlist:", err.message);
        }
    };

    return (
        <div>
            <Header2 />
            <div className="container mx-auto mt-[70px] p-6">
                {wishlistItems.length > 0 ? (
                    <>
                        <p className="text-[18px] font-semibold mb-7 flex items-center">
                            <button
                                onClick={() => window.history.back()}
                                className="hover:text-purple-500 font-semibold text-[18px] flex items-center"
                            >
                                <i className="text-[20px] md:text-[20px] mr-2 font-bold bx bx-arrow-back"></i>
                            </button>
                            My Wishlist
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            {wishlistItems.map((item) => (
                                <div key={item.id} className="p-5 border rounded-lg mb-4 shadow-sm">
                                    <div className="flex gap-4 items-start">
                                        <button onClick={() => navigate(`/product/${item.product.id}`)} className="cursor-pointer">
                                            <img
                                                src={item.product?.mainImage ? `${BASE_URL}${item.product.mainImage}` : "/fallback-image.jpg"}
                                                alt={item.product.name}
                                                className="w-24 h-24 object-contain rounded-lg"
                                            />
                                        </button>
                                        <div className="flex-1">
                                            <h3 onClick={() => navigate(`/product/${item.product.id}`)}
                                                className="cursor-pointer text-lg font-semibold product-name-truncate"
                                                title={item.product.name}>{item.product.name}</h3>
                                            <p className="text-lg font-bold mt-2">
                                                {item.product.sale_price ? item.product.sale_price.toFixed(2) : "0.00"} AED
                                            </p>

                                            <div className="flex gap-2 mt-3">
                                                <button
                                                    className="px-4 py-2 cursor-pointer bg-red-500 text-white rounded-lg hover:bg-red-600"
                                                    onClick={() => removeFromWishlist(item.product.id)}
                                                >
                                                    Remove
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </>
                ) : (
                    <div className="text-center mt-20">
                        <h2 className="text-2xl font-semibold mb-2">Your Wishlist is Empty</h2>
                        <p className="text-gray-600 mb-6">Looks like you haven't added anything to your wishlist yet.</p>
                        <button
                            onClick={() => navigate("/")}
                            className="px-8 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                        >
                            Browse Products
                        </button>
                    </div>
                )}
            </div>
            <Footer />
        </div>
    );
}
