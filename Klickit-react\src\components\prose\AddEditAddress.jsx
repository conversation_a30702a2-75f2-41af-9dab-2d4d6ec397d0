import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import axios from "axios";
import LoadingSpinner from "../includes/LoadingSpinner"; // Ensure this exists
import { BASE_URL } from "../../utils/config";

const AddEditAddress = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const isEdit = Boolean(id);

    const [formData, setFormData] = useState({
        address1: "",
        address2: "",
        city: "",
        state: "",
        pincode: "",
        address_type: ""
    });

    const [loading, setLoading] = useState(true);

    useEffect(() => {
        if (isEdit) {
            axios.get(`${BASE_URL}/addresses/${id}/`, {
                headers: { Authorization: `Bearer ${localStorage.getItem("access_token")}` }
            })
            .then(response => {
                setFormData(response.data);
                setLoading(false);
            })
            .catch(error => {
                console.error("Error fetching address:", error);
                alert("Address not found! Redirecting to address list.");
                navigate("/addresses");
            });
        } else {
            setLoading(false);
        }
    }, [id, isEdit]);

    if (loading) return <LoadingSpinner />;

    const handleChange = (e) => {
        setFormData({ ...formData, [e.target.name]: e.target.value });
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        const token = localStorage.getItem("access_token");
        if (!token) {
            console.error("No authentication token found. Redirecting to login.");
            navigate("/login");
            return;
        }

        try {
            const config = { headers: { Authorization: `Bearer ${token}` } };
            if (isEdit) {
                await axios.put(`${BASE_URL}/addresses/${id}/edit/`, formData, config);
            } else {
                await axios.post(`${BASE_URL}/addresses/add/`, formData, config);
            }
            navigate("/addresses");
        } catch (error) {
            console.error("Error saving address:", error.response ? error.response.data : error);
        }
    };

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 py-10">
            <div className="bg-white shadow-lg rounded-lg p-8 max-w-lg w-full">
                <h2 className="text-2xl font-bold mb-6 text-gray-700">{isEdit ? "Edit" : "Add"} Address</h2>
                <form onSubmit={handleSubmit} className="space-y-4">
                    {Object.keys(formData).map((key) => {
                        if (key === 'id') {
                            return null;
                        }

                        return key !== "address_type" ? (
                            <div key={key}>
                                <label className="block text-sm font-medium text-gray-600 mb-1" htmlFor={key}>{key.replace("_", " ")}</label>
                                <input
                                    type="text"
                                    name={key}
                                    id={key}
                                    value={formData[key]}
                                    onChange={handleChange}
                                    className="w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                    required={key !== "address2"}
                                />
                            </div>
                        ) : (
                            <div key={key}>
                                <label className="block text-sm font-medium text-gray-600 mb-1" htmlFor={key}>Address Type</label>
                                <select
                                    name={key}
                                    id={key}
                                    value={formData[key]}
                                    onChange={handleChange}
                                    className="w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                    required
                                >
                                    <option value="" disabled>Select Address Type</option>
                                    <option value="HM">Home</option>
                                    <option value="WO">Work</option>
                                    <option value="OT">Other</option>
                                </select>
                            </div>
                        );
                    })}
                    <button type="submit" className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg shadow-lg hover:bg-blue-700 transition-all">
                        {isEdit ? "Update" : "Add"} Address
                    </button>
                </form>
            </div>
        </div>
    );
};

export default AddEditAddress;
