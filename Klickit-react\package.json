{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@tailwindcss/vite": "^4.0.3", "axios": "^1.7.9", "framer-motion": "^12.5.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.4.0", "react-router-dom": "^7.1.5", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1", "swiper": "^11.2.2", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.5.1", "tailwindcss": "^4.0.3", "vite": "^6.0.5"}}