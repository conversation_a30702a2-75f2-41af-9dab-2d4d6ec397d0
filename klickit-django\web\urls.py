from django.conf import settings
from django.conf.urls.static import static
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
)
from django.urls import path
from web import views
from web.views import *


app_name = 'web'

urlpatterns = [
    path('', views.index_api, name='index'),
    path('account/',views.get_account_data, name='get_account_data'),

    path("register/", views.register_api, name="register"),
    path("verify-otp/", views.verify_otp_api, name="verify-otp"),
    path("login/", views.login_api, name="login"),
    path("token/refresh/", TokenRefreshView.as_view(), name="token-refresh"),
    path("logout/", views.logout_api, name="logout"),
    path('send-reset-email/', send_reset_email),
    path('reset-password/<uidb64>/<token>/', reset_password_api),
    path('resend-otp/', views.resend_otp_api),
    path('seller-interest/', views.seller_interest_api, name='seller-interest'),

    path('product/<int:id>/',views.product, name='product'),
    path('product/<int:product_id>/reviews/', views.add_review_api, name='api_add_review'),


    path('category/<int:id>/', category_detail, name='api-category'),
    path('brand-page/<int:id>/', views.brand_page, name='brand-page'),

    path('orders/', orders_api, name='orders'),
    path('order/<int:id>/', order_detail, name='order-detail'),
    path('order/<str:order_identifier>/invoice/', download_order_invoice, name='order-invoice'),

    path("addresses/", get_addresses, name="get_addresses"),
    path("addresses/add/", add_address, name="add_address"),
    path("addresses/<int:id>/edit/", edit_address, name="edit_address"),
    path("addresses/<int:id>/delete/", delete_address, name="delete_address"),
    path("addresses/<int:id>/", get_address_by_id, name="get_address_by_id"),

    path("addresses-c/", get_addresses_checkout, name="get_addresses_checkout"),
    path("addresses-c/add/", add_address_checkout, name="add_address_checkout"),
    path("addresses-c/<int:id>/edit/", edit_address_checkout, name="edit_address_checkout"),
    

    path('service/', views.service_api, name='service'),
    path('request/service/', views.request_service_api, name='request_service'),

    path("cart/", views.cart_api, name="cart"),
    path('checkout/', views.checkout_api, name='checkout'),
    path('payment/success/', handle_successful_payment, name='payment-success'),
    path("add/", views.add_to_cart_api, name="add-to-cart"),
    path("update/<int:id>/", views.cart_update_api, name="update-cart"),
    path('cart/remove/<int:id>/', remove_from_cart, name='remove-from-cart'),
    path('cart/update/<int:id>/', views.cart_update_api, name='update-cart'),
    path('wishlist/', views.wishlist_api, name='wishlist'),
    path('wishlist/add/<int:id>/', views.add_to_wishlist_api, name='add-to-wishlist'),
    path('wishlist/remove/<int:id>/', views.remove_from_wishlist_api, name='remove-from-wishlist'),
    path('verify-payment/', verify_stripe_payment, name='verify-payment'),
    path('api/search/', views.product_search_api, name='product_search'),
    path('api/search-suggestions/', views.search_suggestions_api, name='search_suggestions'),
]



if settings.DEBUG:
    urlpatterns += (
        static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT) +
        static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    )