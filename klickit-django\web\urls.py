from django.conf import settings
from django.conf.urls.static import static
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
)
from django.urls import path
from web import views
from web.views import *
from rest_framework_simplejwt.views import TokenRefreshView

app_name = 'web'

urlpatterns = [
    path('', views.IndexAPIView.as_view(),name='index'),
    path('account/',views.get_account_data, name='get_account_data'),

    path("register/", views.RegisterAPIView.as_view(), name="register"),
    path("verify-otp/", views.VerifyOTPAPIView.as_view(), name="verify-otp"),
    path("login/", views.LoginAPIView.as_view(), name="login"),
    path("token/refresh/", TokenRefreshView.as_view(), name="token-refresh"),
    path("logout/", views.LogoutAPIView.as_view(), name="logout"),
    path('send-reset-email/', send_reset_email),
    path('reset-password/<uidb64>/<token>/', reset_password_api),
    path('resend-otp/', ResendOTPAPIView.as_view()),
    path('seller-interest/', views.SellerInterestAPIView.as_view(), name='seller-interest'),

    path('product/<int:id>/',views.product, name='product'),
    path('product/<int:product_id>/reviews/', AddReviewView.as_view(), name='api_add_review'),


    path('category/<int:id>/', category_detail, name='api-category'),
    path('brand-page/<int:id>/', views.brand_page, name='brand-page'),

    path('orders/', orders_api, name='orders'),
    path('order/<int:id>/', order_detail, name='order-detail'),
    path('order/<str:order_identifier>/invoice/', download_order_invoice, name='order-invoice'),

    path("addresses/", get_addresses, name="get_addresses"),
    path("addresses/add/", add_address, name="add_address"),
    path("addresses/<int:id>/edit/", edit_address, name="edit_address"),
    path("addresses/<int:id>/delete/", delete_address, name="delete_address"),
    path("addresses/<int:id>/", get_address_by_id, name="get_address_by_id"),
    

    path('service/', ServiceAPIView.as_view(), name='service'),
    path('request/service/', RequestServiceAPIView.as_view(), name='request_service'),

    path("cart/", CartAPIView.as_view(), name="cart"),
    path('checkout/', CheckoutAPIView.as_view(), name='checkout'),
    path('payment/success/', handle_successful_payment, name='payment-success'),
    path("add/", AddToCartAPIView.as_view(), name="add-to-cart"),
    path("update/<int:id>/", CartUpdateAPIView.as_view(), name="update-cart"),
    path('cart/remove/<int:id>/', remove_from_cart, name='remove-from-cart'),
    path('cart/update/<int:id>/', CartUpdateAPIView.as_view(), name='update-cart'),
    path('wishlist/', WishlistAPI.as_view(), name='wishlist'),
    path('wishlist/add/<int:id>/', AddToWishlistAPI.as_view(), name='add-to-wishlist'),
    path('wishlist/remove/<int:id>/', RemoveFromWishlistAPI.as_view(), name='remove-from-wishlist'),
    path('verify-payment/', verify_stripe_payment, name='verify-payment'),
    path('api/search/', views.ProductSearchView.as_view(), name='product_search'),
    path('api/search-suggestions/', views.SearchSuggestionsView.as_view(), name='search_suggestions'),
]



if settings.DEBUG:
    urlpatterns += (
        static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT) +
        static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    )