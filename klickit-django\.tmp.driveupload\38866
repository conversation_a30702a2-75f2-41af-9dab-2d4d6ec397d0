{% extends "base/admin-base.html" %}
{% block container %}
{% include 'includes/seller-nav.html' %}
<div class="content-body">
    <div class="row page-titles mx-0"><div class="col p-md-0"><ol class="breadcrumb"><li class="breadcrumb-item"><a href="javascript:void(0)">{{ sub_title }}</a></li><li class="breadcrumb-item active"><a href="javascript:void(0)">{{ name }}</a></li></ol></div></div>
    <div class="container-fluid"><div class="row"><div class="col-12"><div class="card"><div class="card-body">
        <div class="heading-line"><h4 class="card-title m-0 p-0">{{ name }} - Step 4</h4><div><span class="badge badge-info">Product: {{ product.name }}</span></div></div>
        {% if messages %}<div class="mt-3">{% for message in messages %}<div class="alert {% if message.tags %}alert-{{ message.tags }}{% else %}alert-info{% endif %}" role="alert">{{ message }}</div>{% endfor %}</div>{% endif %}
        <div class="mt-4"><h5>Images Added:</h5>{% if images_added %}<div class="table-responsive"><table class="table table-sm table-bordered"><thead><tr><th>Image</th><th>Alt Text</th><th>Linked Variant(s)</th><th>Action</th></tr></thead><tbody>{% for img in images_added %}<tr><td><img src="{{ img.image.url }}" alt="{{ img.alt_text|default:'Image' }}" height="40"></td><td>{{ img.alt_text|default:"-" }}</td><td>{% for var in img.variant.all %}{{ var.name|default:var.pk }}{% if not forloop.last %}, {% endif %}{% empty %}-{% endfor %}</td><td><a href="{% url 'managers:delete_wizard_image' product_id=product.id pk=img.pk %}" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure?');">Delete</a></td></tr>{% endfor %}</tbody></table></div>{% else %}<p>No images added yet.</p>{% endif %}</div><hr>
        <h5>Add / Update Images:</h5>
        <div class="alert alert-warning" role="alert"><strong>Note:</strong> You can link one variant per image during bulk upload. To link an image to multiple variants, please edit it later.</div>
        <form method="post" enctype="multipart/form-data" class="mt-3">
            {% csrf_token %}
            <div id="image-rows-container">
                <div class="form-row align-items-center mb-3 p-3 border rounded image-row">
                    <div class="form-group col-md-4"><label>Image File</label><input type="file" name="image" class="form-control-file" required></div>
                    <div class="form-group col-md-4"><label>Alt Text</label><input type="text" name="alt_text" class="form-control" placeholder="Describe the image"></div>
                    <div class="form-group col-md-3"><label>Link to Variant (Optional)</label><select name="variant" class="form-control"><option value="">-- No Variant --</option>{% for variant in product_variants %}<option value="{{ variant.id }}">{{ variant.name|default:variant.id }}</option>{% endfor %}</select></div>
                    <div class="form-group col-md-1 d-flex align-items-end"><button type="button" class="btn btn-danger btn-sm remove-image-row">Remove</button></div>
                </div>
            </div>
            <button type="button" id="add-image-row" class="btn btn-info mt-2"><i class="fa fa-plus"></i> Add Another Image</button>
            <hr>
            <div class="d-flex justify-content-between mt-4">
                <button type="submit" class="btn btn-success">Upload All Images</button>
                <a href="{% url 'managers:seller_add_product_wizard_step5_specs' product_id=product.id %}" class="btn btn-primary">Continue to Add Top Features</a>
            </div>
        </form>
    </div></div></div></div></div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    function cloneRow(containerId, rowClass) {
        const container = document.getElementById(containerId);
        const firstRow = container.querySelector(rowClass);
        if (!firstRow) return;
        const newRow = firstRow.cloneNode(true);
        newRow.querySelectorAll('input').forEach(input => {
            input.value = '';
        });
        newRow.querySelectorAll('select').forEach(select => {
            select.selectedIndex = 0;
        });
        container.appendChild(newRow);
    }
    document.getElementById('add-image-row').addEventListener('click', () => cloneRow('image-rows-container', '.image-row'));
    document.getElementById('image-rows-container').addEventListener('click', function(e) {
        if (e.target && e.target.classList.contains('remove-image-row')) {
            if (document.querySelectorAll('.image-row').length > 1) {
                e.target.closest('.image-row').remove();
            } else {
                alert("You can't remove the last row.");
            }
        }
    });
});
</script>
{% endblock %}