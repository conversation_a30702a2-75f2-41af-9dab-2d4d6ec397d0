// vite.config.js
import { defineConfig } from "file:///C:/Users/<USER>/Documents/work/neumoon/Klickit-react/node_modules/vite/dist/node/index.js";
import tailwindcss from "file:///C:/Users/<USER>/Documents/work/neumoon/Klickit-react/node_modules/@tailwindcss/vite/dist/index.mjs";
import react from "file:///C:/Users/<USER>/Documents/work/neumoon/Klickit-react/node_modules/@vitejs/plugin-react/dist/index.mjs";
var vite_config_default = defineConfig({
  plugins: [
    react(),
    tailwindcss()
  ],
  server: {
    host: "0.0.0.0",
    // Listen on all network interfaces
    port: 5173,
    // Optional: set port if needed
    strictPort: true
    // Fail if port is already taken
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcuanMiXSwKICAic291cmNlUm9vdCI6ICJDOlxcVXNlcnNcXGhwXFxEb2N1bWVudHNcXHdvcmtcXG5ldW1vb25cXEtsaWNraXQtcmVhY3RcXCIsCiAgInNvdXJjZXNDb250ZW50IjogWyJjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZGlybmFtZSA9IFwiQzpcXFxcVXNlcnNcXFxcaHBcXFxcRG9jdW1lbnRzXFxcXHdvcmtcXFxcbmV1bW9vblxcXFxLbGlja2l0LXJlYWN0XCI7Y29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2ZpbGVuYW1lID0gXCJDOlxcXFxVc2Vyc1xcXFxocFxcXFxEb2N1bWVudHNcXFxcd29ya1xcXFxuZXVtb29uXFxcXEtsaWNraXQtcmVhY3RcXFxcdml0ZS5jb25maWcuanNcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfaW1wb3J0X21ldGFfdXJsID0gXCJmaWxlOi8vL0M6L1VzZXJzL2hwL0RvY3VtZW50cy93b3JrL25ldW1vb24vS2xpY2tpdC1yZWFjdC92aXRlLmNvbmZpZy5qc1wiO2ltcG9ydCB7IGRlZmluZUNvbmZpZyB9IGZyb20gJ3ZpdGUnXHJcbmltcG9ydCB0YWlsd2luZGNzcyBmcm9tICdAdGFpbHdpbmRjc3Mvdml0ZSdcclxuaW1wb3J0IHJlYWN0IGZyb20gJ0B2aXRlanMvcGx1Z2luLXJlYWN0J1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZGVmaW5lQ29uZmlnKHtcclxuICBwbHVnaW5zOiBbXHJcbiAgICByZWFjdCgpLFxyXG4gICAgdGFpbHdpbmRjc3MoKSxcclxuICBdLFxyXG4gIHNlcnZlcjoge1xyXG4gICAgaG9zdDogJzAuMC4wLjAnLCAvLyBMaXN0ZW4gb24gYWxsIG5ldHdvcmsgaW50ZXJmYWNlc1xyXG4gICAgcG9ydDogNTE3MywgICAgICAvLyBPcHRpb25hbDogc2V0IHBvcnQgaWYgbmVlZGVkXHJcbiAgICBzdHJpY3RQb3J0OiB0cnVlIC8vIEZhaWwgaWYgcG9ydCBpcyBhbHJlYWR5IHRha2VuXHJcbiAgfVxyXG59KVxyXG4iXSwKICAibWFwcGluZ3MiOiAiO0FBQWdWLFNBQVMsb0JBQW9CO0FBQzdXLE9BQU8saUJBQWlCO0FBQ3hCLE9BQU8sV0FBVztBQUVsQixJQUFPLHNCQUFRLGFBQWE7QUFBQSxFQUMxQixTQUFTO0FBQUEsSUFDUCxNQUFNO0FBQUEsSUFDTixZQUFZO0FBQUEsRUFDZDtBQUFBLEVBQ0EsUUFBUTtBQUFBLElBQ04sTUFBTTtBQUFBO0FBQUEsSUFDTixNQUFNO0FBQUE7QUFBQSxJQUNOLFlBQVk7QUFBQTtBQUFBLEVBQ2Q7QUFDRixDQUFDOyIsCiAgIm5hbWVzIjogW10KfQo=
