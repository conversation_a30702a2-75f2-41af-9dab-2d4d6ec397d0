import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import LoadingSpinner from '../includes/LoadingSpinner';
import axios from 'axios';
import Header from '../includes/Header';
import '../../index.css';
import { BASE_URL } from "../../utils/config";

const Category = () => {
    const { id } = useParams();
    const [data, setData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [selectedSubcategoryId, setSelectedSubcategoryId] = useState(null);
    const navigate = useNavigate();

    useEffect(() => {
        const fetchData = async () => {
            setLoading(true);
            try {
                const token = localStorage.getItem('access_token');
                const headers = token ? { Authorization: `Bearer ${token}` } : {}; // Only send if token exists

                const response = await axios.get(`${BASE_URL}/category/${id}/`, { headers });
                if (response.data.title) {
                    document.title = response.data.title;
                }
                setData(response.data);
                setSelectedSubcategoryId(null);  // Reset subcategory selection on category change
            } catch (err) {
                setError(err.response?.data?.detail || 'Error loading category');
            } finally {
                setLoading(false);
            }
        };
        fetchData();
    }, [id]);


    if (loading) return <LoadingSpinner />;
    if (error) return <div>Error: {error}</div>;

    // Get subcategories of the currently selected category
    const currentSubcategories = data?.subcategories || [];

    // Find the selected subcategory object
    const selectedSubcategory = selectedSubcategoryId
        ? currentSubcategories.find(sub => sub.id === selectedSubcategoryId)
        : null;

    // Filter products based on selected subcategory
    const filterIds = selectedSubcategory
        ? [selectedSubcategory.id, ...(selectedSubcategory.descendant_ids || [])]
        : [];

    const filteredProducts = filterIds.length
        ? data.products.filter(product => product.category?.id && filterIds.includes(product.category.id))
        : data.products;

    const handleSubcategoryClick = (subcategoryId) => {
        navigate(`/category/${subcategoryId}`);
        setSelectedSubcategoryId(subcategoryId);
    };

    return (
        <div>
            <Header cartCount={data.cart_count} />

            <div className="md:mt-[100px] mt-[150px] ml-5">
                <p className="text-[18px] font-semibold mb-7 flex items-center">
                    <button
                        onClick={() => navigate("/")}
                        className="hover:text-purple-500 cursor-pointer font-semibold text-[18px] flex items-center"
                    >
                        <i className='text-[20px] md:text-[20px] mr-2 font-bold bx bx-arrow-back'></i>
                    </button>
                    Categories
                </p>
            </div>

            {/* Main Categories Carousel */}
            <div className="flex justify-start w-full overflow-x-auto scrollbar-hide">
                {data.categories.map(category => (
                    <div
                        key={category.id}
                        className="group categ duration-300 rounded-lg bg-white p-2 sm:p-2 md:p-3 lg:p-4 flex flex-col items-center min-w-[50px] sm:min-w-[65px] md:min-w-[90px] lg:min-w-[125px] mr-[20px]"
                    >
                        <button
                            onClick={() => navigate(`/category/${category.id}`)}
                            className="flex cursor-pointer flex-col items-center"
                        >
                            <div
                                className={`rounded-full p-[6px] sm:p-[10px] md:p-[10px] lg:p-[16px] mb-[6px] md:mb-2 lg:mb-3 border transition-transform duration-300 group-hover:scale-105
                                    ${parseInt(id) === category.id ? "border-2 border-gray-800" : "border border-gray-300"}`}
                            >
                                <img
                                    src={`${BASE_URL}${category.image}`}
                                    alt={category.name}
                                    className="w-[20px] imgg sm:w-[25px] md:w-[30px] lg:w-[40px] h-[20px] sm:h-[25px] md:h-[30px] lg:h-[40px] transition-transform duration-300"
                                />
                            </div>
                        </button>
                    </div>
                ))}
            </div>

            {/* Subcategories Section (Only for Selected Category) */}
            {currentSubcategories.length > 0 && currentSubcategories && (
                <div className="subcategories-section ml-10 mt-5">
                    <div className="flex flex-wrap gap-3">
                        {/* "All" button to reset filtering */}
                        <button
                            onClick={() => {
                                navigate(`/category/${id}`);
                                setSelectedSubcategoryId(null);
                            }}
                            className={`px-4 py-2 rounded ${
                                selectedSubcategoryId === null
                                    ? 'bg-blue-500 text-white'
                                    : 'bg-gray-200 hover:bg-gray-300'
                            }`}
                        >
                            All
                        </button>

                        {/* Subcategory Buttons */}
                        {currentSubcategories.map(subcategory => (
                            <button
                                key={subcategory.id}
                                onClick={() => handleSubcategoryClick(subcategory.id)}
                                className={`px-4 py-2 rounded ${
                                    selectedSubcategoryId === subcategory.id
                                        ? 'bg-blue-500 text-white'
                                        : 'bg-gray-200 hover:bg-gray-300'
                                }`}
                            >
                                {subcategory.name}
                            </button>
                        ))}
                    </div>
                </div>
            )}

            {/* Products Section */}
            <section className="py-6 md:py-14">
                <div className="wrapper">
                    {filteredProducts.length > 0 ? (
                        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-5 md:gap-10">
                            {filteredProducts.map(product => (
                                <div
                                    className="min-w-[150px] md:min-w-[200px] loalo px-[2px] max-w-[150px] md:max-w-[200px] shadow-lg rounded-xl relative snap-center"
                                    key={product.id}
                                >
                                    <a
                                        onClick={() => navigate(`/product/${product.id}`)}
                                        className="cursor-pointer block"
                                    >
                                        <img
                                            src={product.mainImage && product.mainImage.startsWith('http')
                                              ? product.mainImage
                                              : product.mainImage && product.mainImage.startsWith('/media')
                                                ? `${BASE_URL}${product.mainImage}`
                                                : '/placeholder-product.jpg'}
                                            alt={product.name}
                                            className="responsive-image"
                                            onError={(e) => {
                                                e.target.src = '/placeholder-product.jpg';
                                                e.target.classList.add('object-contain', 'p-4');
                                            }}
                                        />
                                    </a>
                                    <div className="p-3">
                                        <h3 className="text-[15px] font-normal mb-1 product-name-truncate">
                                            <a
                                                onClick={() => navigate(`/product/${product.id}`)}
                                                className="cursor-pointer hover:text-blue-600"
                                                title={product.name}
                                            >
                                                {product.name}
                                            </a>
                                        </h3>
                                        <div className="flex items-center gap-2">
                                            {product.regular_price && (
                                                <span className="text-[10px] line-through text-gray-500">
                                                    {product.regular_price}AED
                                                </span>
                                            )}
                                            <span className="text-[14px] font-semibold">
                                                {product.sale_price}AED
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <p className="text-center text-gray-500">No products available in this category.</p>
                    )}
                </div>
            </section>
        </div>
    );
};

export default Category;
