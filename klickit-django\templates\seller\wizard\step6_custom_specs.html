{% extends "base/admin-base.html" %}
{% load static %}

{% block container %}
{% include 'includes/seller-nav.html' %}

<div class="content-body">
    <div class="row page-titles mx-0">
        <div class="col p-md-0">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="javascript:void(0)">{{ sub_title }}</a></li>
                <li class="breadcrumb-item active"><a href="javascript:void(0)">{{ name }}</a></li>
            </ol>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="heading-line">
                            <h4 class="card-title m-0 p-0">{{ name }} - Step 6</h4>
                            <div><span class="badge badge-info">Product: {{ product.name }}</span></div>
                        </div>

                        {# Display messages #}
                        {% if messages %}
                            <div class="mt-3">
                            {% for message in messages %}
                                <div class="alert {% if message.tags %}alert-{{ message.tags }}{% else %}alert-info{% endif %}" role="alert">{{ message }}</div>
                            {% endfor %}
                            </div>
                        {% endif %}

                        {# --- List Already Added Custom Specs --- #}
                        <div class="mt-4">
                            <h5>Custom Specifications Added So Far:</h5>
                            {% if specs_added %}
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Key</th>
                                            <th>Value</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    {% for spec in specs_added %}
                                        <tr>
                                            <td>{{ spec.name|default:"-" }}</td>
                                            <td>{{ spec.key|default:"-" }}</td>
                                            <td>{{ spec.value|default:"-" }}</td>
                                            <td>
                                                <a href="{% url 'managers:delete_wizard_custom_spec' product_id=product.id pk=spec.pk %}"
                                                   class="btn btn-danger btn-sm"
                                                   onclick="return confirm('Are you sure you want to delete this specification?');">
                                                   Delete
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <p>No custom specifications added yet.</p>
                            {% endif %}
                        </div>
                        <hr>

                        {# --- Form to Add New Custom Spec --- #}
                        <h5>Add New Custom Specification:</h5>
                        <form method="post" class="mt-3">
                            {% csrf_token %}
                            <div class="form-row">
                                <div class="form-group col-md-4">
                                    <label for="key">Key (e.g., 'Color', 'Size'):</label>
                                    <input type="text" class="form-control" id="key" name="key" required>
                                </div>
                                <div class="form-group col-md-4">
                                    <label for="value">Value (e.g., 'Red', 'Large'):</label>
                                    <input type="text" class="form-control" id="value" name="value" required>
                                </div>
                                <div class="form-group col-md-4">
                                    <label for="name">Display Name (optional):</label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           placeholder="Leave blank to auto-generate">
                                    <small class="form-text text-muted">Optional: A user-friendly name for this spec</small>
                                </div>
                            </div>

                            <div class="form-group text-left mt-3">
                                <button type="submit" class="btn btn-success">Add Specification</button>
                            </div>
                        </form>
                        <hr>

                        {# --- Navigation Buttons --- #}
                        <div class="form-group text-right mt-4">
                            {# Link to final product list - uses GET #}
                            <a href="{% url 'seller:products' %}" class="btn btn-primary">Finish Adding Product</a>
                            {# Back button to Step 5 #}
                            <a href="{% url 'managers:seller_add_product_wizard_step5_specs' product_id=product.id %}" 
                               class="btn btn-outline-secondary ml-2">
                               Back to Top Features
                            </a>
                        </div>

                        <img src="{% static './images/admin-img1.png' %}" alt="" style="width: 50%;"> 

                    </div> {# End of card-body #}
                </div> {# End of card #}
            </div> {# End of col-12 #}
        </div> {# End of row #}
    </div> {# End of container-fluid #}
</div> {# End of content-body #}

{% endblock %}
