{% extends "base/admin-base.html" %}
{% load static %}

{% block container %}
{% include 'includes/seller-nav.html' %}

<div class="content-body">
    <div class="row page-titles mx-0">
        <div class="col p-md-0">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="javascript:void(0)">{{ sub_title }}</a></li>
                <li class="breadcrumb-item active"><a href="javascript:void(0)">{{ name }}</a></li>
            </ol>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="heading-line">
                            <h4 class="card-title m-0 p-0">{{ name }} - Step 6</h4>
                            <div><span class="badge badge-info">Product: {{ product.name }}</span></div>
                        </div>

                        {% if messages %}
                            <div class="mt-3">
                            {% for message in messages %}
                                <div class="alert {% if message.tags %}alert-{{ message.tags }}{% else %}alert-info{% endif %}" role="alert">{{ message }}</div>
                            {% endfor %}
                            </div>
                        {% endif %}

                        <div class="mt-4">
                            <h5>Specifications Added:</h5>
                            {% if specs_added %}
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Key</th>
                                            <th>Value</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    {% for spec in specs_added %}
                                        <tr>
                                            <td>{{ spec.key }}</td>
                                            <td>{{ spec.value|truncatechars:50 }}</td>
                                            <td>
                                                <a href="{% url 'managers:delete_wizard_custom_spec' product_id=product.id pk=spec.pk %}"
                                                   class="btn btn-danger btn-sm"
                                                   onclick="return confirm('Are you sure?');">
                                                   Delete
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <p>No specifications added yet for this product.</p>
                            {% endif %}
                        </div>
                        <hr>

                        <h5>Add / Update Specifications:</h5>
                        <form method="post" class="mt-3">
                            {% csrf_token %}
                            <div id="spec-rows-container">
                                <div class="form-row align-items-center mb-2 spec-row">
                                    <div class="form-group col-md-5">
                                        <label>Key</label>
                                        <input type="text" name="key" class="form-control" placeholder="e.g., Processor">
                                    </div>
                                    <div class="form-group col-md-5">
                                        <label>Value</label>
                                        <input type="text" name="value" class="form-control" placeholder="e.g., Snapdragon 8 Gen 2">
                                    </div>
                                    <div class="form-group col-md-2" style="margin-top: 30px;">
                                        <button type="button" class="btn btn-danger btn-sm remove-spec-row">Remove</button>
                                    </div>
                                </div>
                            </div>

                            <button type="button" id="add-spec-row" class="btn btn-info mt-2"><i class="fa fa-plus"></i> Add Another Specification</button>
                            <hr>
                            <div class="form-group text-right mt-4">
                                <button type="submit" class="btn btn-success">Save All Specifications</button>
                                <a href="{% url 'seller:products' %}" id="finish-button" class="btn btn-primary">Finish & View Products</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const container = document.getElementById('spec-rows-container');
    const addButton = document.getElementById('add-spec-row');
    const finishButton = document.getElementById('finish-button');

    // Function to add a new row
    addButton.addEventListener('click', function() {
        const firstRow = container.querySelector('.spec-row');
        const newRow = firstRow.cloneNode(true);
        newRow.querySelectorAll('input').forEach(input => input.value = '');
        container.appendChild(newRow);
    });

    // Function to remove a row
    container.addEventListener('click', function(e) {
        if (e.target && e.target.classList.contains('remove-spec-row')) {
            if (container.querySelectorAll('.spec-row').length > 1) {
                e.target.closest('.spec-row').remove();
            } else {
                alert("You can't remove the last row.");
            }
        }
    });

    // **NEW: Logic for the confirmation popup**
    finishButton.addEventListener('click', function(e) {
        let hasUnsavedChanges = false;
        const specRows = container.querySelectorAll('.spec-row');

        specRows.forEach(row => {
            const keyInput = row.querySelector('input[name="key"]');
            const valueInput = row.querySelector('input[name="value"]');
            if (keyInput.value.trim() !== '' || valueInput.value.trim() !== '') {
                hasUnsavedChanges = true;
            }
        });

        if (hasUnsavedChanges) {
            const confirmation = confirm("You have unsaved specifications. Are you sure you want to finish without saving them?");
            if (!confirmation) {
                e.preventDefault(); // Stop the link from navigating
            }
        }
    });
});
</script>
{% endblock %}
