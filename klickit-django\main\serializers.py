from rest_framework import serializers
from .models import Payment

class PaymentSerializer(serializers.ModelSerializer):
    """
    Serializer for the Payment model, providing access to payment details
    including status, amount, and related identifiers.
    """
    
    class Meta:
        model = Payment
        fields = [
            'id', 
            'payment_intent_id', 
            'status', 
            'currency', 
            'total_amount', 
            'created_datetime'
        ]
