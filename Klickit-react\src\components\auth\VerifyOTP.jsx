import { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import axios from 'axios';
import LoadingSpinner from '../includes/LoadingSpinner';
import { BASE_URL } from "../../utils/config";
import logo from "../../assets/images/logooo.png";

const VerifyOTP = ({ setIsLoggedIn }) => {
  const [otp, setOtp] = useState('');
  const [error, setError] = useState('');
  const [verificationData, setVerificationData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [resendDisabled, setResendDisabled] = useState(true); 
  const [timer, setTimer] = useState(60); 
  

  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (location.state?.email && location.state?.password && location.state?.phone_number) {
      setVerificationData(location.state);
    } else {
      navigate('/register');
    }
  }, [location, navigate]);

  // Countdown timer for resend button
  useEffect(() => {
    let countdown;
    if (resendDisabled && timer > 0) {
      countdown = setInterval(() => {
        setTimer((prev) => prev - 1);
      }, 1000);
    } else if (resendDisabled && timer === 0) {
      setResendDisabled(false);
      setTimer(60);
    }
  
    return () => clearInterval(countdown);
  }, [resendDisabled, timer]);
  

  // Handle OTP submission
  const handleVerifyOtp = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      const response = await axios.post(`${BASE_URL}/verify-otp/`, {
        email: verificationData.email,
        otp: otp,
        password: verificationData.password,
        phone_number: verificationData.phone_number
      });

      localStorage.setItem('access_token', response.data.access);
      localStorage.setItem('refresh_token', response.data.refresh);
      setIsLoggedIn(true);
      navigate('/');
    } catch (error) {
      setError(error.response?.data?.error || 'Verification failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Resend OTP API call + start timer
  const handleResendOtp = async () => {
    if (resendDisabled) return;

    setResendDisabled(true);
    setTimer(60);

    try {
      await axios.post(`${BASE_URL}/resend-otp/`, {
        email: verificationData.email
      });
      alert("OTP sent successfully!");
    } catch (error) {
      console.error(error);
      setError("Failed to resend OTP. Please try again later.");
      setResendDisabled(false); // allow retry if request fails
    }
  };

  if (loading) return <LoadingSpinner />;

  return (
    <section className="min-h-screen flex justify-center items-center bg-gray-200 px-4 py-12">
      <div className="bg-white shadow-md rounded-lg w-full max-w-md p-8 border border-gray-200">
        <div className="text-center mb-6">
          <img src={logo} alt="neumoon Logo" className="mx-auto w-16 mb-4" />
          <h2 className="text-2xl font-bold text-gray-800">neumoon</h2>
        </div>

        <div className="mb-6">
          <h3 className="text-xl font-bold text-gray-800 text-center">Verify Your Account</h3>
          <p className="text-sm text-gray-600 text-center mt-2">
            Enter the OTP sent to your email to complete verification.
          </p>
        </div>

        <form onSubmit={handleVerifyOtp} className="space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-2 rounded text-center text-sm">
              {error}
            </div>
          )}

          <div>
            <label htmlFor="otp" className="block text-sm font-medium text-gray-700 mb-1">
              One-Time Password (OTP)
            </label>
            <input
              id="otp"
              type="text"
              placeholder="Enter the 6-digit OTP"
              value={otp}
              onChange={(e) => setOtp(e.target.value.replace(/\D/g, '').slice(0, 6))}
              required
              className="w-full border border-gray-300 rounded-lg py-3 px-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <button
            type="submit"
            className="w-full py-3 px-4 text-white bg-blue-600 hover:bg-blue-700 rounded-lg font-medium transition-colors duration-200"
          >
            VERIFY
          </button>
        </form>

        <div className="text-center mt-6">
          <p className="text-sm text-gray-600">
            Didn't receive the OTP?{" "}
            <button
              type="button"
              onClick={handleResendOtp}
              className={`font-medium ${
                resendDisabled ? 'text-gray-400 cursor-not-allowed' : 'text-blue-600 hover:text-blue-800'
              }`}
              disabled={resendDisabled}
            >
              {resendDisabled ? `Resend OTP in ${timer}s` : 'Resend OTP'}
            </button>
          </p>
        </div>
      </div>
    </section>
  );
};

export default VerifyOTP;
