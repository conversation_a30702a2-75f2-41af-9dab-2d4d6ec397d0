{% extends "base/admin-base.html" %}
{% load static %}

{% block container %}
{% include 'includes/seller-nav.html' %}

<style>
.wizard-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.wizard-progress {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    position: relative;
}

.wizard-progress::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 0;
    right: 0;
    height: 2px;
    background: #e9ecef;
    z-index: 1;
}

.wizard-progress::after {
    content: '';
    position: absolute;
    top: 20px;
    left: 0;
    width: 0%; /* First step - no progress yet */
    height: 2px;
    background: #28a745;
    z-index: 2;
}

.wizard-step {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    position: relative;
    z-index: 3;
}

.wizard-step.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
}

.wizard-step-label {
    position: absolute;
    top: 50px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    white-space: nowrap;
}

.form-section {
    background: white;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-title {
    color: #495057;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
}

.form-control {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 10px 12px;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    width: 100%;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: 0;
}

.help-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

.required-field::after {
    content: ' *';
    color: #dc3545;
}

.wizard-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
}

.btn-primary {
    background: #007bff;
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 4px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    cursor: pointer;
}

.btn-outline-secondary {
    background: transparent;
    border: 1px solid #6c757d;
    color: #6c757d;
    padding: 12px 24px;
    border-radius: 4px;
    text-decoration: none;
    display: inline-block;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}
</style>

<div class="content-body">
    <div class="wizard-container">
        <!-- Progress Indicator -->
        <div class="wizard-progress">
            <div class="wizard-step active">
                <span>1</span>
                <div class="wizard-step-label">Product Details</div>
            </div>
            <div class="wizard-step">
                <span>2</span>
                <div class="wizard-step-label">Colors</div>
            </div>
            <div class="wizard-step">
                <span>3</span>
                <div class="wizard-step-label">Variants</div>
            </div>
            <div class="wizard-step">
                <span>4</span>
                <div class="wizard-step-label">Images</div>
            </div>
            <div class="wizard-step">
                <span>5</span>
                <div class="wizard-step-label">Specifications</div>
            </div>
        </div>

        <!-- Page Header -->
        <div class="text-center mb-4">
            <h2 class="mb-2">Add New Product</h2>
            <p class="text-muted">Start by entering the basic details of your product</p>
        </div>

        <!-- Messages -->
        {% if messages %}
            <div class="mb-4">
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <!-- Important Notice -->
        <div class="alert-warning">
            <strong><i class="fas fa-exclamation-triangle"></i> Important:</strong>
            Please ensure you have created the required <strong>brand</strong> and <strong>category</strong> before proceeding.
            <a href="{% url 'managers:brands' %}" target="_blank">Manage Brands</a> |
            <a href="{% url 'managers:categories' %}" target="_blank">Manage Categories</a>
        </div>
        <!-- Product Details Form -->
        <form method="post" enctype="multipart/form-data" id="product-form">
            {% csrf_token %}

            <!-- Basic Information Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-info-circle text-primary"></i>
                    Basic Information
                </h3>

                <div class="form-grid">
                    <div class="form-group">
                        <label class="required-field">Product Name</label>
                        {{ form.name }}
                        <div class="help-text">Enter a clear, descriptive name for your product</div>
                    </div>

                    <div class="form-group">
                        <label>Product Model</label>
                        {{ form.product_model }}
                        <div class="help-text">Model number or identifier (optional)</div>
                    </div>

                    <div class="form-group">
                        <label>SKU (Stock Keeping Unit)</label>
                        {{ form.sku }}
                        <div class="help-text">Unique identifier for inventory tracking (optional)</div>
                    </div>
                </div>

                <div class="form-group">
                    <label>Product Description</label>
                    {{ form.description }}
                    <div class="help-text">Provide a detailed description of your product's features and benefits</div>
                </div>

                <div class="form-group">
                    <label>Additional Details</label>
                    {{ form.details }}
                    <div class="help-text">Any additional information about the product (specifications, usage instructions, etc.)</div>
                </div>
            </div>

            <!-- Categorization Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-tags text-success"></i>
                    Categorization
                </h3>

                <div class="form-grid">
                    <div class="form-group">
                        <label class="required-field">Category</label>
                        {{ form.category }}
                        <div class="help-text">Select the most appropriate category for your product</div>
                    </div>

                    <div class="form-group">
                        <label>Brand</label>
                        {{ form.brand }}
                        <div class="help-text">Select the brand (optional)</div>
                    </div>
                </div>
            </div>

            <!-- Pricing Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-dollar-sign text-warning"></i>
                    Pricing Information
                </h3>

                <div class="alert-info">
                    <strong><i class="fas fa-info-circle"></i> Important:</strong>
                    Please enter prices <strong>including VAT</strong>. These will be the final prices shown to customers.
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label>Regular Price (AED)</label>
                        {{ form.regular_price }}
                        <div class="help-text">Original price including VAT (optional - used to show discounts)</div>
                    </div>

                    <div class="form-group">
                        <label class="required-field">Sale Price (AED)</label>
                        {{ form.sale_price }}
                        <div class="help-text">Current selling price including VAT</div>
                    </div>

                    <div class="form-group">
                        <label class="required-field">Stock Quantity</label>
                        {{ form.stock }}
                        <div class="help-text">Available quantity for sale</div>
                    </div>
                </div>
            </div>

            <!-- Media Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-image text-info"></i>
                    Product Media
                </h3>

                <div class="form-grid">
                    <div class="form-group">
                        <label>Main Product Image</label>
                        {{ form.mainimage }}
                        <div class="help-text">Upload the primary image for your product (JPG, PNG, max 5MB)</div>
                    </div>

                    <div class="form-group">
                        <label>Product Video URL</label>
                        {{ form.video }}
                        <div class="help-text">YouTube or Vimeo video URL (optional)</div>
                    </div>
                </div>
            </div>

            <!-- Delivery & Guarantee Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-truck text-success"></i>
                    Delivery & Guarantee Information
                </h3>

                <div class="alert-info">
                    <strong><i class="fas fa-eye"></i> Customer Display:</strong>
                    This information will be displayed to customers on the product page to build trust and confidence.
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label>Delivery Title</label>
                        {{ form.delivery_title }}
                        <div class="help-text">e.g., "Free Delivery", "Express Shipping"</div>
                    </div>

                    <div class="form-group">
                        <label>Delivery Duration</label>
                        {{ form.delivery_duration }}
                        <div class="help-text">e.g., "2-3 Business Days", "Same Day Delivery"</div>
                    </div>

                    <div class="form-group">
                        <label>Guarantee Title</label>
                        {{ form.garantee_title }}
                        <div class="help-text">e.g., "1 Year Warranty", "Money Back Guarantee"</div>
                    </div>

                    <div class="form-group">
                        <label>Guarantee Duration</label>
                        {{ form.garantee_time }}
                        <div class="help-text">e.g., "12 Months", "30 Days"</div>
                    </div>
                </div>
            </div>

            <!-- Additional Information Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-list-ul text-secondary"></i>
                    Additional Information
                </h3>

                <div class="form-group">
                    <label>Product Specifications</label>
                    {{ form.specifications }}
                    <div class="help-text">Technical specifications or key features (optional)</div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="wizard-actions">
                <div>
                    <a href="{% url 'managers:products' %}" class="btn-outline-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>

                <div>
                    <button type="submit" class="btn-primary">
                        Save & Continue to Colors <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    document.getElementById('product-form').addEventListener('submit', function(e) {
        const requiredFields = this.querySelectorAll('input[required], select[required], textarea[required]');
        let hasErrors = false;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.style.borderColor = '#dc3545';
                hasErrors = true;
            } else {
                field.style.borderColor = '#ced4da';
            }
        });

        if (hasErrors) {
            e.preventDefault();
            alert('Please fill in all required fields marked with *');
        }
    });

    // Real-time validation feedback
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.hasAttribute('required') && !this.value.trim()) {
                this.style.borderColor = '#dc3545';
            } else {
                this.style.borderColor = '#ced4da';
            }
        });

        input.addEventListener('input', function() {
            if (this.style.borderColor === 'rgb(220, 53, 69)' && this.value.trim()) {
                this.style.borderColor = '#ced4da';
            }
        });
    });
});
</script>

{% endblock %}