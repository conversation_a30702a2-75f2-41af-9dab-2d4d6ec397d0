# Generated by Django 5.2 on 2025-07-15 11:02

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Attribute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_datetime', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_datetime', models.DateTimeField(auto_now=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('name', models.CharField(help_text='e.g., RAM, Storage, Processor', max_length=100, unique=True)),
            ],
            options={
                'verbose_name': 'Attribute',
                'verbose_name_plural': 'Attributes',
                'db_table': 'items_attribute',
                'ordering': ('name',),
            },
        ),
        migrations.CreateModel(
            name='Brand',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_datetime', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_datetime', models.DateTimeField(auto_now=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=100)),
                ('image', models.ImageField(blank=True, null=True, upload_to='brand')),
            ],
            options={
                'verbose_name': 'brand',
                'verbose_name_plural': 'brands',
                'db_table': 'items_brand',
                'ordering': ('id',),
            },
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_datetime', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_datetime', models.DateTimeField(auto_now=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=100)),
                ('image', models.ImageField(blank=True, null=True, upload_to='category')),
            ],
            options={
                'verbose_name': 'category',
                'verbose_name_plural': 'categories',
                'db_table': 'items_category',
                'ordering': ('id',),
            },
        ),
        migrations.CreateModel(
            name='Color',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_datetime', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_datetime', models.DateTimeField(auto_now=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('color', models.CharField(max_length=50)),
                ('name', models.CharField(max_length=50)),
            ],
            options={
                'verbose_name': 'color',
                'verbose_name_plural': 'colors',
                'db_table': 'items_color',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='CustomSpecification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_datetime', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_datetime', models.DateTimeField(auto_now=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('name', models.CharField(blank=True, max_length=100, null=True)),
                ('key', models.CharField(max_length=100)),
                ('value', models.TextField()),
            ],
            options={
                'verbose_name': 'specification',
                'verbose_name_plural': 'specifications',
                'db_table': 'items_specification',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='IconImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_datetime', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_datetime', models.DateTimeField(auto_now=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('image', models.ImageField(upload_to='products')),
                ('name', models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                'verbose_name': 'iconimage',
                'verbose_name_plural': 'iconimages',
                'db_table': 'items_iconimages',
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_datetime', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_datetime', models.DateTimeField(auto_now=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=100)),
            ],
            options={
                'verbose_name': 'product',
                'verbose_name_plural': 'products',
                'db_table': 'items_product',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='ProductAttributeValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_datetime', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_datetime', models.DateTimeField(auto_now=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('value', models.CharField(help_text='e.g., 16GB, 512GB, Snapdragon 8 Gen 2', max_length=255)),
            ],
            options={
                'verbose_name': 'Product Attribute Value',
                'verbose_name_plural': 'Product Attribute Values',
                'db_table': 'items_product_attribute_value',
            },
        ),
        migrations.CreateModel(
            name='ProductImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_datetime', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_datetime', models.DateTimeField(auto_now=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('image', models.ImageField(upload_to='product_variant_images')),
                ('alt_text', models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                'verbose_name': 'productimage',
                'verbose_name_plural': 'productimages',
                'db_table': 'items_productimages',
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='ProductVariant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_datetime', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_datetime', models.DateTimeField(auto_now=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255)),
                ('sku', models.CharField(blank=True, max_length=100, null=True, unique=True)),
                ('product_model', models.CharField(blank=True, max_length=100, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('details', models.TextField(blank=True, null=True)),
                ('mainimage', models.ImageField(blank=True, null=True, upload_to='variant_images/')),
                ('video', models.URLField(blank=True, null=True)),
                ('regular_price', models.FloatField()),
                ('sale_price', models.FloatField(blank=True, null=True)),
                ('stock', models.IntegerField(default=0)),
                ('sales_count', models.IntegerField(blank=True, default=0, null=True)),
                ('delivery_title', models.CharField(blank=True, max_length=100, null=True)),
                ('delivery_duration', models.CharField(blank=True, max_length=100, null=True)),
                ('garantee_title', models.CharField(blank=True, max_length=100, null=True)),
                ('garantee_time', models.CharField(blank=True, max_length=100, null=True)),
            ],
            options={
                'verbose_name': 'Product Variant',
                'verbose_name_plural': 'Product Variants',
                'db_table': 'items_product_variant',
                'ordering': ('id',),
            },
        ),
        migrations.CreateModel(
            name='Spec',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_datetime', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_datetime', models.DateTimeField(auto_now=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('detail', models.CharField(blank=True, max_length=50, null=True)),
            ],
            options={
                'verbose_name': 'spec',
                'verbose_name_plural': 'specs',
                'db_table': 'items_specs',
                'ordering': ['id'],
            },
        ),
    ]
