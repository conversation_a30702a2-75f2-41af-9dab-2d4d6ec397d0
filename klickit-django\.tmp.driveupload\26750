import stripe
from django.conf import settings
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.core.mail import send_mail
from django.template.loader import render_to_string
from .models import Payment
from customers.models import Order, CartItem, OrderItem, Customer, CartTotal
from items.models import Product, ProductVariant
from django.db import transaction, DatabaseError
import logging
from django.core.mail import send_mail
from django.template.loader import render_to_string

logger = logging.getLogger(__name__)
stripe.api_key = settings.STRIPE_SECRET_KEY

def send_order_confirmation_email(order):
    """
    Sends a confirmation email to the user after an order is successfully placed.
    """
    try:
        subject = f"Your NeuMoon order is confirmed! (ID: {order.order_id})"
        context = {
            'order': order,
            'frontend_url': settings.FRONTEND_URL
        }
        html_message = render_to_string('emails/order_confirmation_email.html', context)
        
        # Create a plain text version for email clients that don't support HTML
        plain_message = f"Hi {order.first_name}, Your order {order.order_id} has been confirmed. Thank you for shopping with us!"

        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[order.email],
            html_message=html_message,
            fail_silently=False,
        )
        logger.info(f"Successfully sent order confirmation email for order {order.order_id}")
    except Exception as e:
        logger.error(f"Failed to send order confirmation email for order {order.order_id}: {str(e)}", exc_info=True)

def send_payment_notification(payment, status):
    try:
        subject = f"Payment {status} - Order {payment.metadata.get('order_id', 'N/A')}"
        template = 'emails/payment_status.html'
        context = {
            'payment': payment,
            'status': status,
            'order_id': payment.metadata.get('order_id', 'N/A'),
            'amount': payment.amount,
        }
        html_message = render_to_string(template, context)
        
        send_mail(
            subject=subject,
            message=f"Payment {status} for order {payment.metadata.get('order_id', 'N/A')}",
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[payment.customer.email],
            html_message=html_message,
            fail_silently=False,
        )
    except Exception as e:
        logger.error(f"Failed to send payment notification: {str(e)}")


def fulfill_order(order, payment_intent_id=None):
    """
    Helper function to process order fulfillment.
    Updates order status, processes cart items, updates stock, and clears cart.
    """
    logger.info(f"[fulfill_order] Starting order fulfillment for order {order.order_id}")
    
    try:
        # Update order status
        order.order_status = 'PL'  # Placed
        order.payment_status = 'PS'  # Payment Successful
        order.save()
        
        # Update payment record if it exists
        if hasattr(order, 'payment') and payment_intent_id:
            payment = order.payment
            payment.payment_intent_id = payment_intent_id
            payment.status = 'succeeded'
            payment.save()
        
        customer = order.customer
        
        # Process cart items
        cart_items = CartItem.objects.filter(customer=customer)
        logger.info(f"[fulfill_order] Found {cart_items.count()} cart items for customer {customer.id}")
        
        # Process each cart item
        for item in cart_items:
            try:
                # Create order item
                order_item = OrderItem.objects.create(
                    customer=customer,
                    seller=item.seller,
                    product=item.product,
                    quantity=item.quantity,
                    amount=item.price,
                    option=item.option
                )
                order.items.add(order_item)
                
                # Update stock
                if item.option:
                    option = item.option
                    option.stock -= item.quantity
                    option.save()
                    logger.debug(f"[fulfill_order] Updated stock for option {item.option.id}: -{item.quantity}")
                else:
                    product = item.product
                    product.stock -= item.quantity
                    product.save()
                    logger.debug(f"[fulfill_order] Updated stock for product {item.product.id}: -{item.quantity}")
                
            except Exception as item_error:
                logger.error(f"[fulfill_order] Error processing cart item {getattr(item, 'id', 'unknown')}: {str(item_error)}", exc_info=True)
                # Continue with next item even if one fails
        
        # Add sellers to order
        if cart_items.exists():
            unique_sellers = list(cart_items.values_list("seller", flat=True).distinct())
            sellers_to_add = [seller_id for seller_id in unique_sellers if seller_id is not None]
            if sellers_to_add:
                order.sellers.add(*sellers_to_add)
        
        # Clear the cart
        cart_items.delete()
        CartTotal.objects.filter(customer=customer).delete()
        
        # Send order confirmation email
        send_order_confirmation_email(order)
        
        # Send payment notification if payment exists
        if hasattr(order, 'payment'):
            send_payment_notification(order.payment, 'succeeded')
        
        logger.info(f"[fulfill_order] Successfully processed order {order.order_id}")
        return True
        
    except Exception as e:
        logger.error(f"[fulfill_order] Error processing order {order.order_id}: {str(e)}", exc_info=True)
        raise

@csrf_exempt
@transaction.atomic
def stripe_webhook(request):
    payload = request.body
    sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
    webhook_secret = settings.STRIPE_WEBHOOK_SECRET
    event = None

    try:
        event = stripe.Webhook.construct_event(
            payload, sig_header, webhook_secret
        )
    except ValueError as e:
        logger.error(f"Invalid payload: {str(e)}")
        return HttpResponse(status=400)
    except stripe.error.SignatureVerificationError as e:
        logger.error(f"Invalid signature: {str(e)}")
        return HttpResponse(status=400)
    
    # Process the event
    if event.type == 'checkout.session.completed':
        session = event['data']['object']
        logger.info(f"[Webhook] Checkout session completed: {session.id}")
        
        # Only process if payment was successful
        if session.payment_status == 'paid':
            order_id = session.metadata.get('order_id')
            if not order_id:
                logger.error("[Webhook] No order_id in session metadata")
                return HttpResponse("No order_id in session metadata", status=400)
                
            try:
                # Get the order and lock it for update
                order = Order.objects.select_for_update().get(
                    order_id=order_id,
                    order_status='IN'  # Only process if order is still 'IN' (Initial)
                )
                
                # Process the order using the helper function
                fulfill_order(order, session.payment_intent)
                logger.info(f"[Webhook] Successfully processed checkout.session.completed for order {order_id}")
                return HttpResponse(status=200)
                
            except Order.DoesNotExist:
                logger.error(f"[Webhook] Order not found or already processed: {order_id}")
                return HttpResponse(f"Order {order_id} not found or already processed", status=400)
            except Exception as e:
                logger.error(f"[Webhook] Error processing checkout.session.completed: {str(e)}", exc_info=True)
                return HttpResponse(f"Error processing checkout session: {str(e)}", status=500)
    
    elif event.type == 'payment_intent.succeeded':
        payment_intent = event['data']['object']
        logger.info(f"[Webhook] payment_intent.succeeded - PaymentIntent ID: {payment_intent.id}")
        
        try:
            # Get order_id from payment intent metadata
            order_id = payment_intent.metadata.get('order_id')
            if not order_id:
                error_msg = f"[Webhook] No order_id in payment intent metadata. PaymentIntent ID: {payment_intent.id}"
                logger.error(error_msg)
                return HttpResponse(error_msg, status=400)
            
            logger.info(f"[Webhook] Processing order {order_id} for PaymentIntent {payment_intent.id}")
            
            # Get the order and lock it for update
            order = Order.objects.select_for_update().get(
                order_id=order_id,
                order_status='IN'  # Only process if order is still 'IN' (Initial)
            )
            
            # Process the order using the helper function
            fulfill_order(order, payment_intent.id)
            
            logger.info(f"[Webhook] Successfully processed payment_intent.succeeded for order {order_id}")
            return HttpResponse(status=200)
            
        except Order.DoesNotExist:
            error_msg = f"[Webhook] Order not found or already processed: {order_id}"
            logger.error(error_msg)
            return HttpResponse(error_msg, status=404)
        except Exception as e:
            error_msg = f"[Webhook] Error processing payment_intent.succeeded: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return HttpResponse(error_msg, status=500)

    elif event.type == 'payment_intent.payment_failed':
        payment_intent = event.data.object
        logger.info(f"Webhook: PaymentIntent failed: {payment_intent.id}")
        try:
            payment = Payment.objects.select_for_update().get(payment_intent_id=payment_intent.id)
            
            # Idempotency check
            if payment.status == 'failed':
                logger.info(f"Webhook: Payment {payment_intent.id} already processed as 'failed'.")
                return HttpResponse(status=200)
                
            payment.status = 'failed'
            payment.save()

            order = Order.objects.select_for_update().get(order_id=payment.metadata.get('order_id'))
            order.payment_status = 'PF'  # Payment Failed
            order.save()

            # Restore stock if it was decremented and OrderItems exist
            logger.info(f"Webhook: Attempting to restore stock for failed order {order.order_id}")
            if order.order_status != 'CA': # If not already cancelled
                for item_in_order in order.items.all():
                    if item_in_order.option:
                        option_to_restore = Option.objects.select_for_update().get(id=item_in_order.option.id)
                        option_to_restore.stock += item_in_order.quantity
                        option_to_restore.save()
                        logger.info(f"Webhook: Restored stock for option {item_in_order.option.id} by {item_in_order.quantity}")
                    else:
                        product_to_restore = Product.objects.select_for_update().get(id=item_in_order.product.id)
                        product_to_restore.stock += item_in_order.quantity
                        product_to_restore.save()
                        logger.info(f"Webhook: Restored stock for product {item_in_order.product.id} by {item_in_order.quantity}")
                order.order_status = 'CA' # Mark as Cancelled due to payment failure
                order.save()

            # Send failure notification
            send_payment_notification(payment, 'failed')

        except Payment.DoesNotExist:
            logger.error(f"Webhook: Payment not found in DB: {payment_intent.id}")
            return HttpResponse(status=404)
        except Order.DoesNotExist:
            logger.error(f"Webhook: Order not found in DB: {payment.metadata.get('order_id') if payment else 'Unknown'}")
            return HttpResponse(status=404)
        except Exception as e_fail:
            logger.error(f"Webhook: Error processing payment_intent.payment_failed for PI {payment_intent.id}: {str(e_fail)}", exc_info=True)
            return HttpResponse(status=500)

    # Handle other event types
    else:
        logger.info(f"Webhook: Unhandled event type {event.type}")

    return HttpResponse(status=200)