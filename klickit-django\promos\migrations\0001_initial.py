# Generated by Django 5.2 on 2025-07-15 11:02

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Offer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_datetime', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_datetime', models.DateTimeField(auto_now=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255)),
                ('image', models.ImageField(upload_to='offers')),
                ('url', models.CharField(max_length=255)),
            ],
            options={
                'verbose_name': 'offer',
                'verbose_name_plural': 'offers',
                'db_table': 'promos_offers',
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='Offers',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_datetime', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_datetime', models.DateTimeField(auto_now=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255)),
                ('image1', models.ImageField(upload_to='offers')),
                ('url1', models.CharField(max_length=255)),
                ('image2', models.ImageField(upload_to='offers')),
                ('url2', models.CharField(max_length=255)),
                ('image3', models.ImageField(upload_to='offers')),
                ('url3', models.CharField(max_length=255)),
            ],
            options={
                'verbose_name': 'offers',
                'verbose_name_plural': 'offerses',
                'db_table': 'promos_offerses',
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='Slider',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_datetime', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_datetime', models.DateTimeField(auto_now=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255)),
                ('image', models.ImageField(upload_to='sliders')),
                ('url', models.CharField(max_length=255)),
            ],
            options={
                'verbose_name': 'slider',
                'verbose_name_plural': 'sliders',
                'db_table': 'promos_sliders',
                'ordering': ['-id'],
            },
        ),
    ]
