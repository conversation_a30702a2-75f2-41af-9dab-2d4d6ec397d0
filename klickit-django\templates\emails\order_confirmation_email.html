<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmed</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 20px auto; background-color: #ffffff; border-radius: 8px; overflow: hidden; border: 1px solid #e2e8f0; }
        .header { background-color: #1a202c; color: #ffffff; padding: 20px; text-align: center; }
        .header h1 { margin: 0; font-size: 24px; }
        .content { padding: 30px; }
        .content h2 { font-size: 20px; color: #2d3748; }
        .content p { color: #4a5568; line-height: 1.6; }
        .order-details { margin-top: 20px; width: 100%; border-collapse: collapse; }
        .order-details th, .order-details td { padding: 12px; border-bottom: 1px solid #e2e8f0; text-align: left; }
        .order-details th { color: #718096; text-transform: uppercase; font-size: 12px; }
        .order-details .total td { font-weight: bold; font-size: 18px; color: #2d3748; border-top: 2px solid #2d3748; }
        .button-container { text-align: center; margin-top: 30px; }
        .button { background-color: #48bb78; color: #ffffff; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block; }
        .footer { background-color: #edf2f7; padding: 20px; text-align: center; color: #718096; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>NeuMoon</h1> 
        </div>
        <div class="content">
            <h2>Your order is confirmed!</h2>
            <p>Hi {{ order.first_name|default:order.customer.user.first_name }},</p>
            <p>Great choice — thanks for shopping with NeuMoon! Your order, <strong>{{ order.order_id }}</strong>, is being processed and is in good hands. We'll email you once it's dispatched and on its way.</p>
            
            <div class="button-container">
                <a href="{{ frontend_url }}/order/{{ order.id }}" class="button">View Order Summary</a>
            </div>

            <h3 style="color: #2d3748; margin-top: 30px; border-bottom: 1px solid #e2e8f0; padding-bottom: 10px;">Order Summary</h3>
            
            <table class="order-details">
                <tr>
                    <th>Delivery Address</th>
                    <td>{{ order.address1 }}, {{ order.address2|default:'' }}<br>{{ order.city }}, {{ order.state }}<br>{{ order.pincode }}</td>
                </tr>
                <tr>
                    <th>Payment Method</th>
                    <td>{{ order.get_payment_method_display }}</td>
                </tr>
            </table>

            <h3 style="color: #2d3748; margin-top: 30px; border-bottom: 1px solid #e2e8f0; padding-bottom: 10px;">Items</h3>
            
            <table class="order-details">
                {% for item in order.items.all %}
                <tr>
                    <td>{{ item.product.name }} {% if item.option %}({{ item.option.name }}){% endif %} x {{ item.quantity }}</td>
                    <td style="text-align: right;">AED {{ item.amount|floatformat:2 }}</td>
                </tr>
                {% endfor %}
                <tr class="total">
                    <td>Subtotal</td>
                    <td style="text-align: right;">AED {{ order.sub_total|floatformat:2 }}</td>
                </tr>
                <tr>
                    <td>Shipping</td>
                    <td style="text-align: right;">AED {{ order.delivery_charge|floatformat:2 }}</td>
                </tr>
                {% if order.offer > 0 %}
                <tr>
                    <td>Discount</td>
                    <td style="text-align: right;">- AED {{ order.offer|floatformat:2 }}</td>
                </tr>
                {% endif %}
                <tr class="total">
                    <td>Total</td>
                    <td style="text-align: right;">AED {{ order.total|floatformat:2 }}</td>
                </tr>
            </table>
        </div>
        <div class="footer">
            <p>&copy; {% now "Y" %} NeuMoon. All rights reserved.</p>
        </div>
    </div>
</body>
</html>