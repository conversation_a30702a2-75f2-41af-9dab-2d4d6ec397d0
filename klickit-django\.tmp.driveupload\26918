from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.core.exceptions import FieldError
from promos.models import *
from items.models import *
from users.models import *
from customers.models import *

class CategoryForm(forms.ModelForm):
    class Meta:
        model = Category
        fields = ["name", "image", "parent"]
        widgets = {
            "name": forms.TextInput(attrs={"class": "form-control", "placeholder": "Category Name"}),
            "image": forms.FileInput(attrs={"class": "form-control"}),
            "parent": forms.Select(attrs={"class": "form-control"}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Only show categories with no parent (i.e., main categories)
        self.fields['parent'].queryset = Category.objects.filter(parent__isnull=True)
        self.fields['parent'].required = False


class BrandForm(forms.ModelForm):
    class Meta:
        model = Brand
        fields = ["name", "image"]
        widgets = {
            "name": forms.TextInput(attrs={"class": "form-control", "placeholder": "Brand Name"}),
            "image": forms.FileInput(attrs={"class": "form-control"}),
        }

class ColorForm(forms.ModelForm):
    class Meta:
        model = Color
        fields = ["color", "name"]
        widgets = {
            "color": forms.TextInput(attrs={"class": "form-control", "placeholder": "Color"}),
            "name": forms.TextInput(attrs={"class": "form-control", "placeholder": "Name"}),
        }

class CustomSpecificationForm(forms.ModelForm):
    class Meta:
        model = CustomSpecification
        fields = ["variant", "key", "value"]
        widgets = {
            "variant": forms.Select(attrs={"class": "form-control"}),
            "key": forms.TextInput(attrs={"class": "form-control", "placeholder": "Key (e.g., Processor)"}),
            "value": forms.Textarea(attrs={"class": "form-control", "placeholder": "Value (e.g., Snapdragon 8 Gen 2)"}),
        }

    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        if user:
            try:
                seller = user.seller
                self.fields['variant'].queryset = ProductVariant.objects.filter(product__seller=seller)
            except (AttributeError, Seller.DoesNotExist):
                self.fields['variant'].queryset = ProductVariant.objects.none()

class ProductForm(forms.ModelForm):
    """Form for creating parent Product groups (e.g., 'iPhone 16 Series')"""
    class Meta:
        model = Product
        fields = ["name", "category", "brand"]
        widgets = {
            "name": forms.TextInput(attrs={"class": "form-control", "placeholder": "Product Group Name (e.g., iPhone 16 Series)"}),
            "category": forms.Select(attrs={"class": "form-control"}),
            "brand": forms.Select(attrs={"class": "form-control"}),
        }

    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

class ProductVariantForm(forms.ModelForm):
    class Meta:
        model = ProductVariant
        fields = [
            "name", "product", "color", "product_model", "sku", "description", "details",
            "mainimage", "regular_price", "sale_price", "stock", "delivery_title",
            "delivery_duration", "garantee_title", "garantee_time", "video"
        ]
        widgets = {
            "name": forms.TextInput(attrs={"class": "form-control", "placeholder": "Variant Name"}),
            "product": forms.Select(attrs={"class": "form-control"}),
            "color": forms.Select(attrs={"class": "form-control"}),
            "product_model": forms.TextInput(attrs={"class": "form-control", "placeholder": "Product Model"}),
            "sku": forms.TextInput(attrs={"class": "form-control", "placeholder": "SKU"}),
            "description": forms.Textarea(attrs={"class": "form-control", "placeholder": "Description"}),
            "details": forms.Textarea(attrs={"class": "form-control", "placeholder": "Details"}),
            "mainimage": forms.FileInput(attrs={"class": "form-control"}),
            "regular_price": forms.NumberInput(attrs={"class": "form-control", "placeholder": "Regular Price (VAT included)"}),
            "sale_price": forms.NumberInput(attrs={"class": "form-control", "placeholder": "Sale Price (VAT included)"}),
            "stock": forms.NumberInput(attrs={"class": "form-control", "placeholder": "Stock"}),
            "delivery_title": forms.TextInput(attrs={"class": "form-control", "placeholder": "Delivery Title", "value": "Free Delivery"}),
            "delivery_duration": forms.TextInput(attrs={"class": "form-control", "placeholder": "Delivery Duration", "value": "1-2 day"}),
            "garantee_title": forms.TextInput(attrs={"class": "form-control", "placeholder": "Guarantee Title", "value": "Guaranteed"}),
            "garantee_time": forms.TextInput(attrs={"class": "form-control", "placeholder": "Guarantee Time", "value": "1 year"}),
            "video": forms.URLInput(attrs={"class": "form-control", "placeholder": "Product Video URL"}),
        }

    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        # Filter products by seller if user is provided
        if user:
            try:
                seller = user.seller
                self.fields['product'].queryset = Product.objects.filter(seller=seller)
            except (AttributeError, Seller.DoesNotExist):
                self.fields['product'].queryset = Product.objects.none()

            # Filter colors based on the user who created them
            try:
                self.fields['color'].queryset = Color.objects.filter(created_by=user)
            except FieldError:
                pass
        else:
            if 'color' in self.fields:
                self.fields['color'].queryset = Color.objects.none()



class ProductVariantFormWizard(forms.ModelForm):
    class Meta:
        model = ProductVariant
        fields = [
            "name", "product", "color", "product_model", "sku", "description", "details",
            "mainimage", "regular_price", "sale_price", "stock", "delivery_title",
            "delivery_duration", "garantee_title", "garantee_time", "video"
        ]
        widgets = {
            "name": forms.TextInput(attrs={"class": "form-control", "placeholder": "Variant Name"}),
            "product": forms.Select(attrs={"class": "form-control"}),
            "color": forms.Select(attrs={"class": "form-control"}),
            "product_model": forms.TextInput(attrs={"class": "form-control", "placeholder": "Product Model"}),
            "sku": forms.TextInput(attrs={"class": "form-control", "placeholder": "SKU"}),
            "description": forms.Textarea(attrs={"class": "form-control", "placeholder": "Description"}),
            "details": forms.Textarea(attrs={"class": "form-control", "placeholder": "Details"}),
            "mainimage": forms.FileInput(attrs={"class": "form-control"}),
            "regular_price": forms.NumberInput(attrs={"class": "form-control", "placeholder": "Regular Price (VAT included)"}),
            "sale_price": forms.NumberInput(attrs={"class": "form-control", "placeholder": "Sale Price (VAT included)"}),
            "stock": forms.NumberInput(attrs={"class": "form-control", "placeholder": "Stock"}),
            "delivery_title": forms.TextInput(attrs={"class": "form-control", "placeholder": "Delivery Title", "value": "Free Delivery"}),
            "delivery_duration": forms.TextInput(attrs={"class": "form-control", "placeholder": "Delivery Duration", "value": "1-2 day"}),
            "garantee_title": forms.TextInput(attrs={"class": "form-control", "placeholder": "Guarantee Title", "value": "Guaranteed"}),
            "garantee_time": forms.TextInput(attrs={"class": "form-control", "placeholder": "Guarantee Time", "value": "1 year"}),
            "video": forms.URLInput(attrs={"class": "form-control", "placeholder": "Product Video URL"}),
        }

    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        if 'product' in self.fields:
            self.fields['product'].required = False

        if user:
            try:
                # Filter colors based on the user who created them
                self.fields['color'].queryset = Color.objects.filter(created_by=user)
            except FieldError:
                pass
        else:
             if 'color' in self.fields:
                self.fields['color'].queryset = Color.objects.none()

class ProductImageForm(forms.ModelForm):
    class Meta:
        model = ProductImage
        fields = ["product", "variant", "image", "alt_text"]
        widgets = {
            "product": forms.Select(attrs={"class": "form-control"}),
            "variant": forms.SelectMultiple(attrs={"class": "form-control select2-widget"}),
            "image": forms.FileInput(attrs={"class": "form-control"}),
            "alt_text": forms.TextInput(attrs={"class": "form-control", "placeholder": "Alt Text"}),
        }

    def __init__(self, *args, **kwargs):

        user = kwargs.pop('user', None)

        super().__init__(*args, **kwargs)


        if user:
            try:
                seller = user.seller
                self.fields['product'].queryset = Product.objects.filter(seller=seller)
            except AttributeError:
                 self.fields['product'].queryset = Product.objects.none()


            self.fields['variant'].queryset = ProductVariant.objects.filter(product__seller=user.seller)


class ProductImageFormWizard(forms.ModelForm):
    class Meta:
        model = ProductImage
        fields = ["product", "variant", "image", "alt_text"]
        widgets = {
            "product": forms.Select(attrs={"class": "form-control"}),
            "variant": forms.SelectMultiple(attrs={"class": "form-control select2-widget"}),
            "image": forms.FileInput(attrs={"class": "form-control"}),
            "alt_text": forms.TextInput(attrs={"class": "form-control", "placeholder": "Alt Text"}),
        }


    def __init__(self, *args, **kwargs):

        super().__init__(*args, **kwargs)





        if 'variant' in self.fields:
             self.fields['variant'].queryset = ProductVariant.objects.none()
        if 'product' in self.fields:
             self.fields['product'].queryset = Product.objects.none()
             self.fields['product'].required = False



class AttributeForm(forms.ModelForm):
    class Meta:
        model = Attribute
        fields = ["name"]
        widgets = {
            "name": forms.TextInput(attrs={"class": "form-control", "placeholder": "Attribute Name (e.g., RAM, Storage)"}),
        }

class ProductAttributeValueForm(forms.ModelForm):
    class Meta:
        model = ProductAttributeValue
        fields = ["variant", "attribute", "value"]
        widgets = {
            "variant": forms.Select(attrs={"class": "form-control"}),
            "attribute": forms.Select(attrs={"class": "form-control"}),
            "value": forms.TextInput(attrs={"class": "form-control", "placeholder": "Attribute Value (e.g., 16GB, 512GB)"}),
        }

    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        if user:
            try:
                seller = user.seller
                self.fields['variant'].queryset = ProductVariant.objects.filter(product__seller=seller)
            except (AttributeError, Seller.DoesNotExist):
                self.fields['variant'].queryset = ProductVariant.objects.none()

class IconImageForm(forms.ModelForm):
    class Meta:
        model = IconImage
        fields = ["image", "name"]
        widgets = {
            "image": forms.FileInput(attrs={"class": "form-control"}),
            "name": forms.TextInput(attrs={"class": "form-control", "placeholder": "Name"}),
        }

class SpecForm(forms.ModelForm):
    class Meta:
        model = Spec
        fields = ["variant", "image", "detail"]
        widgets = {
            "variant": forms.Select(attrs={"class": "form-control"}),
            "image": forms.Select(attrs={"class": "form-control"}),
            "detail": forms.TextInput(attrs={"class": "form-control", "placeholder": "Detail"}),
        }

    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        if user:
            try:
                seller = user.seller
                self.fields['variant'].queryset = ProductVariant.objects.filter(product__seller=seller)
            except (AttributeError, Seller.DoesNotExist):
                 self.fields['variant'].queryset = ProductVariant.objects.none()


class CustomerForm(forms.ModelForm):
    class Meta:
        model = Customer
        fields = ["customer_id", "user"]
        widgets = {
            "customer_id": forms.TextInput(attrs={"class": "form-control", "placeholder": "Customer ID"}),
            "user": forms.Select(attrs={"class": "form-control"}),
        }

class CartItemForm(forms.ModelForm):
    class Meta:
        model = CartItem
        fields = ["customer", "variant", "quantity", "price"]
        widgets = {
            "customer": forms.Select(attrs={"class": "form-control"}),
            "variant": forms.Select(attrs={"class": "form-control"}),
            "quantity": forms.NumberInput(attrs={"class": "form-control"}),
            "price": forms.NumberInput(attrs={"class": "form-control"}),
        }

class WishlistForm(forms.ModelForm):
    class Meta:
        model = Wishlist
        fields = ["customer", "variant"]  # Exclude 'created_datetime'
        widgets = {
            "customer": forms.Select(attrs={"class": "form-control"}),
            "variant": forms.Select(attrs={"class": "form-control"}),
        }

class ServiceForm(forms.ModelForm):
    class Meta:
        model = Service
        fields = ["title", "description", "image"]  # Include 'image' as it's part of the model
        widgets = {
            "title": forms.TextInput(attrs={"class": "form-control", "placeholder": "Service Title"}),
            "description": forms.Textarea(attrs={"class": "form-control", "placeholder": "Description"}),
            "image": forms.FileInput(attrs={"class": "form-control"}),  # For image upload
        }


class ServiceRequestForm(forms.ModelForm):
    class Meta:
        model = ServiceRequest
        fields = ["name", "email", "phone", "service", "details"]  # Align with the model
        widgets = {
            "name": forms.TextInput(attrs={"class": "form-control", "placeholder": "Your Name"}),
            "email": forms.EmailInput(attrs={"class": "form-control", "placeholder": "Your Email"}),
            "phone": forms.TextInput(attrs={"class": "form-control", "placeholder": "Your Phone Number"}),
            "service": forms.Select(attrs={"class": "form-control"}),  # Dropdown for selecting service
            "details": forms.Textarea(attrs={"class": "form-control", "placeholder": "Details about your request"}),
        }


class CouponForm(forms.ModelForm):
    class Meta:
        model = Coupon
        fields = [
            "code",
            "description",
            "is_Percentage",
            "discount_value",
            "active",
            "valid_from",
            "valid_until",
            "is_onece_peruser",
        ]
        widgets = {
            "code": forms.TextInput(attrs={"class": "form-control", "placeholder": "Coupon Code"}),
            "description": forms.TextInput(attrs={"class": "form-control", "placeholder": "Description"}),
            "is_Percentage": forms.CheckboxInput(attrs={"class": "form-check-input"}),
            "discount_value": forms.NumberInput(attrs={"class": "form-control", "placeholder": "Discount Value"}),
            "active": forms.CheckboxInput(attrs={"class": "form-check-input"}),
            "valid_from": forms.DateTimeInput(attrs={"class": "form-control", "type": "datetime-local"}),
            "valid_until": forms.DateTimeInput(attrs={"class": "form-control", "type": "datetime-local"}),
            # "is_onece_peruser": forms.CheckboxInput(attrs={"class": "form-check-input"}),
        }


class AddressForm(forms.ModelForm):
    class Meta:
        model = Address
        fields = [
            "customer",
            "address1",
            "address2",
            "city",
            "state",
            "pincode",
            "address_type",
            "is_default",
        ]
        widgets = {
            "customer": forms.Select(attrs={"class": "form-control"}),
            "address1": forms.TextInput(attrs={"class": "form-control", "placeholder": "Address Line 1"}),
            "address2": forms.TextInput(attrs={"class": "form-control", "placeholder": "Address Line 2"}),
            "city": forms.TextInput(attrs={"class": "form-control", "placeholder": "City"}),
            "state": forms.TextInput(attrs={"class": "form-control", "placeholder": "State"}),
            "pincode": forms.TextInput(attrs={"class": "form-control", "placeholder": "Pincode"}),
            "address_type": forms.Select(attrs={"class": "form-control"}),
            "is_default": forms.CheckboxInput(attrs={"class": "form-check-input"}),
        }

class CartTotalForm(forms.ModelForm):
    class Meta:
        model = CartTotal
        fields = ["item_total", "total", "offer", "delivery", "customer"]
        widgets = {
            "item_total": forms.NumberInput(attrs={"class": "form-control", "placeholder": "Item Total"}),
            "total": forms.NumberInput(attrs={"class": "form-control", "placeholder": "Total"}),
            "offer": forms.NumberInput(attrs={"class": "form-control", "placeholder": "Offer"}),
            "delivery": forms.NumberInput(attrs={"class": "form-control", "placeholder": "Delivery Charge"}),
            "customer": forms.Select(attrs={"class": "form-control"}),
        }

class OrderItemForm(forms.ModelForm):
    class Meta:
        model = OrderItem
        fields = ["customer", "variant", "quantity", "amount"]
        widgets = {
            "customer": forms.Select(attrs={"class": "form-control"}),
            "variant": forms.Select(attrs={"class": "form-control"}),
            "quantity": forms.NumberInput(attrs={"class": "form-control", "placeholder": "Quantity"}),
            "amount": forms.NumberInput(attrs={"class": "form-control", "placeholder": "Amount"}),
        }

class OrderForm(forms.ModelForm):
    class Meta:
        model = Order
        fields = [
            "customer",
            "address",
            "order_id",
            "items",
            "sub_total",
            "delivery_charge",
            "offer",
            "total",
            "first_name",
            "last_name",
            "email",
            "phone_number",
            "order_status",
        ]
        widgets = {
            "customer": forms.Select(attrs={"class": "form-control"}),
            "address": forms.Select(attrs={"class": "form-control"}),
            "order_id": forms.TextInput(attrs={"class": "form-control", "placeholder": "Order ID"}),
            "items": forms.SelectMultiple(attrs={"class": "form-control"}),
            "sub_total": forms.NumberInput(attrs={"class": "form-control", "placeholder": "Sub Total"}),
            "delivery_charge": forms.NumberInput(attrs={"class": "form-control", "placeholder": "Delivery Charge"}),
            "offer": forms.NumberInput(attrs={"class": "form-control", "placeholder": "Offer"}),
            "total": forms.NumberInput(attrs={"class": "form-control", "placeholder": "Total"}),
            "first_name": forms.TextInput(attrs={"class": "form-control", "placeholder": "First Name"}),
            "last_name": forms.TextInput(attrs={"class": "form-control", "placeholder": "Last Name"}),
            "email": forms.EmailInput(attrs={"class": "form-control", "placeholder": "Email"}),
            "phone_number": forms.NumberInput(attrs={"class": "form-control", "placeholder": "Phone Number"}),
            "order_status": forms.Select(attrs={"class": "form-control"}),
        }


class ReviewForm(forms.ModelForm):
    class Meta:
        model = Review
        fields = ["product", "user", "rating", "comment"]
        widgets = {
            "product": forms.Select(attrs={"class": "form-control"}),
            "user": forms.Select(attrs={"class": "form-control"}),
            "rating": forms.NumberInput(attrs={"class": "form-control", "placeholder": "Rating (1-5)"}),
            "comment": forms.Textarea(attrs={"class": "form-control", "placeholder": "Write your review here..."}),
        }


class UserForm(UserCreationForm):
    class Meta:
        model = User
        fields = [
            "first_name",
            "last_name",
            "email",
            "phone_number",
            "password1",  # For the password field from UserCreationForm
            "password2",  # For password confirmation
            "is_manager",
            "is_customer",
        ]
        widgets = {
            "first_name": forms.TextInput(attrs={"class": "form-control", "placeholder": "First Name"}),
            "last_name": forms.TextInput(attrs={"class": "form-control", "placeholder": "Last Name"}),
            "email": forms.EmailInput(attrs={"class": "form-control", "placeholder": "Email"}),
            "phone_number": forms.TextInput(attrs={"class": "form-control", "placeholder": "Phone Number"}),
            "is_manager": forms.CheckboxInput(attrs={"class": "form-check-input nono"}),
            "is_customer": forms.CheckboxInput(attrs={"class": "form-check-input nono"}),
        }



class SliderForm(forms.ModelForm):
    class Meta:
        model = Slider
        fields = ["name", "image", "url"]
        widgets = {
            "name": forms.TextInput(attrs={"class": "form-control", "placeholder": "Name"}),
            "image": forms.FileInput(attrs={"class": "form-control"}),
            "url": forms.TextInput(attrs={"class": "form-control", "placeholder": "URL"}),
        }


class OfferForm(forms.ModelForm):
    class Meta:
        model = Offer
        fields = ["name", "image", "url"]
        widgets = {
            "name": forms.TextInput(attrs={"class": "form-control", "placeholder": "Name"}),
            "image": forms.FileInput(attrs={"class": "form-control"}),
            "url": forms.TextInput(attrs={"class": "form-control", "placeholder": "URL"}),
        }


class OffersForm(forms.ModelForm):
    class Meta:
        model = Offers
        fields = ["name", "image1", "url1", "image2", "url2", "image3", "url3"]
        widgets = {
            "name": forms.TextInput(attrs={"class": "form-control", "placeholder": "Name"}),
            "image1": forms.FileInput(attrs={"class": "form-control"}),
            "url1": forms.TextInput(attrs={"class": "form-control", "placeholder": "URL1"}),
            "image2": forms.FileInput(attrs={"class": "form-control"}),
            "url2": forms.TextInput(attrs={"class": "form-control", "placeholder": "URL2"}),
            "image3": forms.FileInput(attrs={"class": "form-control"}),
            "url3": forms.TextInput(attrs={"class": "form-control", "placeholder": "URL3"}),
        }


class AssignSellerByEmailForm(forms.Form):
    """
    Form to assign seller role based on user email.
    """
    email = forms.EmailField(
        label="User Email Address",
        widget=forms.EmailInput(attrs={
            "class": "form-control",
            "placeholder": "Enter the email address of the user to make a seller"
        }),
        required=True
    )