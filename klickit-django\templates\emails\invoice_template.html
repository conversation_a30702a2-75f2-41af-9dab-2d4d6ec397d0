<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>Invoice {{ order.order_id }}</title>
    <style>
        @page {
            size: a4;
            margin: 1cm;
        }
        body {
            font-family: sans-serif;
            font-size: 10pt;
            color: #333;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            vertical-align: top;
            padding: 5px;
        }
        .header-table td {
            padding: 0;
        }
        .header-table tr {
            text-align: center;
        }
        .text-right {
            text-align: right;
        }
        .text-left {
            text-align: left;
        }
        .text-center {
            text-align: center;
        }
        .bold {
            font-weight: bold;
        }
        .company-name {
            font-size: 24pt;
            font-weight: bold;
        }
        .section-title {
            font-weight: bold;
            font-size: 8pt;
            text-transform: uppercase;
            color: #6c757d;
            border-bottom: 1px solid #eee;
            padding-bottom: 4px;
            margin-bottom: 8px;
        }
        .items-table th {
            border-bottom: 2px solid #333;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 8pt;
            padding: 10px 5px;
        }
        .items-table td {
            border-bottom: 1px solid #dee2e6;
            padding: 10px 5px;
        }
        .item-description {
            font-size: 9pt;
            color: #6c757d;
        }
        .totals-table td {
            padding: 6px 0;
        }
        .grand-total {
            font-size: 14pt;
            font-weight: bold;
            border-top: 2px solid #333;
            padding-top: 10px !important;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            font-size: 9pt;
            color: #6c757d;
            text-align: center;
        }
    </style>
</head>
<body>
    <table class="header-table">
        <tr>
            <!-- <td style="width: 50%;">
                <img src="images/logooo.png" style="width: 120px; height: auto;" alt="Neumoon Logo">
            </td> -->
            <td style="width: 50%;" class="text-right">
                <h1 style="font-size: 28pt; margin: 0;">INVOICE</h1>
                <p style="margin: 0;"><strong>Invoice #:</strong> {{ order.order_id }}</p>
                <p style="margin: 0;"><strong>Date:</strong> {{ invoice_date_formatted }}</p>
            </td>
        </tr>
    </table>

    <hr style="border: 0; border-top: 2px solid #333; margin: 20px 0;">

    <table style="margin-bottom: 30px;">
        <tr>
            <td style="width: 40%;">
                <div class="section-title">Bill To</div>
                <p>
                    <strong class="bold">{{ customer_name }}</strong><br>
                    {{ order.address.address1 }}<br>
                    {% if order.address.address2 %}{{ order.address.address2 }}<br>{% endif %}
                    {{ order.address.city }}, {{ order.address.state }}<br>
                    {{ order.address.pincode }}<br>
                    {{ customer_email }}<br>
                    {{ customer_phone }}
                </p>
            </td>
            <td style="width: 60%;">
                <div class="section-title">Payment Details</div>
                <p>
                    <strong>Payment Method:</strong> {{ order.get_payment_method_display }}<br>
                    <strong>Status:</strong> <span class="bold">{{ payment_status_invoice }}</span><br>
                    {% if payment_id_invoice and payment_id_invoice != 'N/A' %}
                        <strong>Payment ID:</strong> {{ payment_id_invoice }}
                    {% endif %}
                </p>
            </td>
        </tr>
    </table>

    <table class="items-table">
        <thead>
            <tr>
                <th class="text-left">Item</th>
                <th class="text-center">Qty</th>
                <th class="text-right">Price</th>
                <th class="text-right">Amount</th>
            </tr>
        </thead>
        <tbody>
            {% for item in order_items %}
            <tr>
                <td>
                    <div class="bold">{{ item.product_name }}</div>
                    {% if item.option_name %}<div class="item-description">({{ item.option_name }})</div>{% endif %}
                </td>
                <td class="text-center">{{ item.quantity }}</td>
                <td class="text-right">{{ item.unit_price|floatformat:2 }} AED</td>
                <td class="text-right">{{ item.line_total|floatformat:2 }} AED</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <table style="margin-top: 30px;">
        <tr>
            <td style="width: 60%;"></td>
            <td style="width: 40%;">
                <table class="totals-table">
                    <tr>
                        <td>Subtotal</td>
                        <td class="text-right">{{ subtotal_amount|floatformat:2 }} AED</td>
                    </tr>
                    {% if discount_amount > 0 %}
                    <tr>
                        <td>Discount</td>
                        <td class="text-right">- {{ discount_amount|floatformat:2 }} AED</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td>Delivery</td>
                        <td class="text-right">{{ delivery_charge_amount|floatformat:2 }} AED</td>
                    </tr>
                    <tr class="grand-total">
                        <td><strong>Total Due</strong></td>
                        <td class="text-right"><strong>{{ grand_total_amount|floatformat:2 }} AED</strong></td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>

    <div class="footer">
        <p>Thank you for your business!</p>
        <p>If you have any questions, please contact <NAME_EMAIL>.</p>
    </div>
</body>
</html>