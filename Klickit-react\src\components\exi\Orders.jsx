import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import Header2 from "../includes/Header2";
import Footer from "../includes/Footer";
import LoadingSpinner from "../includes/LoadingSpinner";
import { BASE_URL } from "../../utils/config";

export default function Orders() {
    const [orders, setOrders] = useState([]);
    const [loading, setLoading] = useState(true);
    const navigate = useNavigate();

    useEffect(() => {
        const fetchOrders = async () => {
            try {
                const token = localStorage.getItem("access_token");
                const response = await fetch(`${BASE_URL}/orders/`, {
                    headers: {
                        "Authorization": `Bearer ${token}`,
                        "Content-Type": "application/json",
                    },
                });

                if (!response.ok) {
                    throw new Error("Failed to fetch orders");
                }

                const data = await response.json();
                if (data.title) {
                    document.title = data.title;
                }
                setOrders(data.orders || []);
                setLoading(false);
            } catch (err) {
                console.error("Error fetching orders:", err.message);
                navigate("/login");
            }
        };
        fetchOrders();
    }, [navigate]);

    if (loading) return <LoadingSpinner />;

    const statusMapping = {
        'IN': 'Initiated',
        'PL': 'Placed',
        'IP': 'In progress',
        'DI': 'Dispatched',
        'CO': 'Completed',
        'CA': 'Cancelled'
    };

    function getStatusFullForm(orderStatus) {
        return statusMapping[orderStatus] || 'Unknown Status';
    }

    return (
        <div>
            <Header2 />
            <section className="py-10 md:py-14 mt-[25px]">
                <div className="container mx-auto p-6">
                    <p className="text-[18px] font-semibold mb-7 flex items-center">
                        <button onClick={() => navigate('/')} className="hover:text-purple-500 font-semibold text-[18px] flex items-center">
                            <i className="text-[20px] md:text-[20px] mr-2 font-bold bx bx-arrow-back"></i>
                        </button>
                        My Orders
                    </p>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-7">
                        {orders.length > 0 ? (
                            orders.map((order) => (
                                <div key={order.id} className="col-span-1 p-4 rounded-xl border shadow-md border-[#A41E11]">
                                    <a onClick={() => navigate(`/order/${order.id}`)} className="cursor-pointer  flex justify-between items-center">
                                        <div className="w-[65%]">
                                            <h3 className="text-[16px] font-semibold text-gray-700 mb-2">{order.order_id}</h3>
                                            <p className="text-[14px] font-normal mb-2">
                                                {order.items.length > 0 ? order.items[0].product.name : "No items"}
                                                {order.items.length > 1 ? ` + ${order.items.length - 1}` : ""}
                                            </p>
                                            <p className="text-[14px] font-normal mb-2 text-[#A41E11]">
                                                status - {getStatusFullForm(order.order_status)}
                                            </p>
                                        </div>
                                    </a>
                                </div>
                            ))
                        ) : (
                            <p className="text-center text-gray-600">No orders found.</p>
                        )}
                    </div>
                </div>
            </section>
            <Footer />
        </div>
    );
}
