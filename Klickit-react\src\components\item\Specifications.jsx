import React, { useState } from "react";

const Specifications = ({ specifications = [], details }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isDetailsExpanded, setIsDetailsExpanded] = useState(false);

  const toggleSpecifications = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <section className="lg:px-[60px] sm:px-[40px] px-[16px] bg-[#FAFAFA] flex justify-center items-center">
      <div className="bg-[#FFFFFF] rounded-[8px] py-[48px] sm:px-[32px] px-[18px] md:my-[80px] w-full max-w-[1200px]">
        
        {/* Details Section */}
        <div>
          <h1 className="text-[24px] font-[500] leading-[32px] mb-[32px]">
            Details
          </h1>
          <p
            className={`text-[14px] font-[500] leading-[24px] text-[#9D9D9D] mb-[32px] cursor-pointer ${
              isDetailsExpanded ? 'details-text-expanded' : 'details-text-truncate'
            }`}
            onClick={() => setIsDetailsExpanded(!isDetailsExpanded)}
          >
            {details}
          </p>
        </div>

        {/* Specifications Section */}
        <div>
          <h3 className="text-[20px] font-[500] leading-[24px] mb-[16px]">
            Specifications
          </h3>
          <ul className="mb-[32px]">
            {(isExpanded ? specifications : specifications.slice(0, 10)).map(
              (spec, index) => (
                <li
                  key={index}
                  className="flex justify-between pb-[8px]"
                  style={{ borderBottom: "0.5px solid #CDCDCD" }}
                >
                  <p className="text-[16px] font-[400] text-[#333333]">
                    {spec.key}
                  </p>
                  <span className="text-[15px] font-[400] text-[#666666]">
                    {spec.value}
                  </span>
                </li>
              )
            )}
          </ul>

          {/* Show "View More/Less" button only if there are more than 10 specifications */}
          {specifications.length > 10 && (
            <div
              onClick={toggleSpecifications}
              className="flex justify-center items-center py-[12px] px-[56px] rounded-[8px] w-[308px] m-auto cursor-pointer hover:bg-gray-100 transition-all"
              style={{ border: "1px solid #545454" }}
            >
              <span className="text-[14px] font-[500] leading-[24px] text-[#545454]">
                {isExpanded ? "View Less" : "View More"}
              </span>
              <i
                className={`bx ${
                  isExpanded ? "bxs-chevron-up" : "bxs-chevron-down"
                } text-[24px] ml-2 text-[#545454]`}
              ></i>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default Specifications;
