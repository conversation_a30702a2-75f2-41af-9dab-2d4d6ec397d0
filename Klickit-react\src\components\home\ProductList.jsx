import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useLocation, useNavigate } from 'react-router-dom';
import Header from '../includes/Header';
import LoadingSpinner from '../includes/LoadingSpinner';
import { BASE_URL } from "../../utils/config";
import '../../index.css';

const ProductCard = ({ product }) => {
    const navigate = useNavigate();

    // Handle different possible image field names from the API
    const getImageUrl = () => {
        // Check for different image field variations
        if (product.mainImage) {
            // If it's already a full URL, use it
            if (product.mainImage.startsWith('http')) {
                return product.mainImage;
            }
            // If it's a relative path, build the full URL
            return `${BASE_URL}${product.mainImage}`;
        }
        if (product.mainimage) {
            if (product.mainimage.startsWith('http')) {
                return product.mainimage;
            }
            return `${BASE_URL}${product.mainimage}`;
        }
        // Return a data URL for a simple placeholder
        return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik02MCA2MEgxNDBWMTQwSDYwVjYwWiIgZmlsbD0iI0Q1RDlERCIvPgo8cGF0aCBkPSJNODAgODBIMTIwVjEyMEg4MFY4MFoiIGZpbGw9IiNBN0I0QkQiLz4KPC9zdmc+';
    };

    // Calculate discount percentage
    const getDiscountPercentage = () => {
        if (product.regular_price && product.sale_price && product.regular_price > product.sale_price) {
            return Math.round(((product.regular_price - product.sale_price) / product.regular_price) * 100);
        }
        return 0;
    };

    return (
        <div onClick={() => navigate(`/product/${product.id}`)} className="cursor-pointer bg-white border border-gray-200 rounded-lg shadow-sm group overflow-hidden transition-all duration-300 hover:shadow-md">
            <div className="w-full aspect-square bg-white overflow-hidden p-2 relative">
                <img
                    src={getImageUrl()}
                    alt={product.name || 'Product'}
                    className="w-full h-full object-contain transition-transform duration-300 group-hover:scale-105"
                    loading="lazy"
                    onError={(e) => {
                        e.target.onerror = null;
                        e.target.src = '/placeholder-product.jpg';
                    }}
                />
                {getDiscountPercentage() > 0 && (
                    <div className="absolute top-2 left-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
                        -{getDiscountPercentage()}%
                    </div>
                )}
            </div>
            <div className="p-2 md:p-3 text-left">
                {/* Product Name - Responsive text size */}
                <p className="text-xs md:text-sm font-semibold text-gray-800 line-clamp-2 h-8 md:h-10 mb-1 md:mb-2">
                    {product.name}
                </p>

                {/* Rating - Hide on very small screens, show on md+ */}
                <div className="hidden md:flex items-center mb-2">
                    <div className="flex items-center">
                        {[1, 2, 3, 4, 5].map((star) => (
                            <svg
                                key={star}
                                className={`w-3 h-3 md:w-4 md:h-4 ${
                                    star <= (product.rating || 0) ? 'text-yellow-400' : 'text-gray-300'
                                }`}
                                fill="currentColor"
                                viewBox="0 0 20 20"
                            >
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                        ))}
                        <span className="text-xs md:text-sm text-gray-600 ml-1">
                            ({product.rating ? product.rating.toFixed(1) : '0.0'})
                        </span>
                    </div>
                </div>

                {/* Mobile Rating - Compact version */}
                <div className="flex md:hidden items-center mb-1">
                    <span className="bg-green-600 text-white text-xs font-bold px-1.5 py-0.5 rounded-sm flex items-center">
                        {product.rating ? product.rating.toFixed(1) : '0.0'}
                        <svg className="w-2.5 h-2.5 ml-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                    </span>
                </div>

                {/* Brand - Hide on mobile */}
                {product.brand && (
                    <p className="hidden md:block text-xs text-gray-500 mb-2">by {product.brand.name}</p>
                )}

                {/* Price - Responsive sizing */}
                <div className="mt-1 md:mt-2">
                    <div className="flex items-center gap-1 md:gap-2">
                        <span className="text-sm md:text-lg font-bold text-gray-900">{product.sale_price} AED</span>
                        {product.regular_price > product.sale_price && (
                            <span className="text-xs md:text-sm text-gray-500 line-through">{product.regular_price} AED</span>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

const ProductList = () => {
    const [products, setProducts] = useState([]);
    const [categories, setCategories] = useState([]);
    const [brands, setBrands] = useState([]);
    const [loading, setLoading] = useState(true);

    const location = useLocation();
    const navigate = useNavigate();

    const searchParams = new URLSearchParams(location.search);
    const query = searchParams.get('q') || '';
    const [selectedCategory, setSelectedCategory] = useState(searchParams.get('category') || '');
    const [selectedBrand, setSelectedBrand] = useState(searchParams.get('brand') || '');
    const [sortBy, setSortBy] = useState(searchParams.get('sort') || '');

    useEffect(() => {
        const fetchSearchData = async () => {
            setLoading(true);
            try {
                const params = { q: query, category: selectedCategory, brand: selectedBrand, sort: sortBy };
                const response = await axios.get(`${BASE_URL}/search/`, { params });
                if (response.data.title) {
                    document.title = response.data.title;
                }
                setProducts(response.data.products || []);
                setCategories(response.data.categories || []);
                setBrands(response.data.brands || []);
            } catch (error) {
                console.error('Error fetching search data:', error);
            } finally {
                setLoading(false);
            }
        };
        fetchSearchData();

        const newSearch = new URLSearchParams();
        if (query) newSearch.set('q', query);
        if (selectedCategory) newSearch.set('category', selectedCategory);
        if (selectedBrand) newSearch.set('brand', selectedBrand);
        if (sortBy) newSearch.set('sort', sortBy);
        navigate(`${location.pathname}?${newSearch.toString()}`, { replace: true });

    }, [query, selectedCategory, selectedBrand, sortBy]);

    if (loading) return <LoadingSpinner />;

    return (
        <>
            <Header />
            <div className="bg-gray-50 mt-[30px] md:mt-0">
            <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-28 pb-12">
                <h1 className="text-2xl font-bold text-gray-900 mb-6">
                    {query ? `Search Results for "${query}"` : "Products"}
                </h1>
                {/* Mobile Filters */}
                <div className="lg:hidden mb-6">
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 bg-white p-4 rounded-lg shadow-sm border">
                        <div>
                            <label htmlFor="mobile-category" className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                            <select id="mobile-category" value={selectedCategory} onChange={(e) => setSelectedCategory(e.target.value)} className="w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                <option value="">All Categories</option>
                                {categories.map(cat => <option key={cat.id} value={cat.name}>{cat.name}</option>)}
                            </select>
                        </div>
                        <div>
                            <label htmlFor="mobile-brand" className="block text-sm font-medium text-gray-700 mb-1">Brand</label>
                            <select id="mobile-brand" value={selectedBrand} onChange={(e) => setSelectedBrand(e.target.value)} className="w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                <option value="">All Brands</option>
                                {brands.map(b => <option key={b.id} value={b.name}>{b.name}</option>)}
                            </select>
                        </div>
                        <div>
                            <label htmlFor="mobile-sort" className="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                            <select id="mobile-sort" value={sortBy} onChange={(e) => setSortBy(e.target.value)} className="w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                <option value="">Default</option>
                                <option value="price_high_to_low">Price: High to Low</option>
                                <option value="price_low_to_high">Price: Low to High</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-4 gap-x-8 gap-y-10">
                    {/* Desktop Filters */}
                    <aside className="hidden lg:block">
                        <h2 className="sr-only">Filters</h2>
                        <div className="space-y-6 bg-white p-4 rounded-lg shadow-sm border">
                            <div>
                                <label htmlFor="category" className="block text-sm font-medium text-gray-700">Category</label>
                                <select id="category" value={selectedCategory} onChange={(e) => setSelectedCategory(e.target.value)} className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                    <option value="">All</option>
                                    {categories.map(cat => <option key={cat.id} value={cat.name}>{cat.name}</option>)}
                                </select>
                            </div>
                            <div>
                                <label htmlFor="brand" className="block text-sm font-medium text-gray-700">Brand</label>
                                <select id="brand" value={selectedBrand} onChange={(e) => setSelectedBrand(e.target.value)} className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                    <option value="">All</option>
                                    {brands.map(b => <option key={b.id} value={b.name}>{b.name}</option>)}
                                </select>
                            </div>
                            <div>
                                <label htmlFor="sort" className="block text-sm font-medium text-gray-700">Sort By</label>
                                <select id="sort" value={sortBy} onChange={(e) => setSortBy(e.target.value)} className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                    <option value="">Default</option>
                                    <option value="price_high_to_low">Price: High to Low</option>
                                    <option value="price_low_to_high">Price: Low to High</option>
                                </select>
                            </div>
                        </div>
                    </aside>

                    <div className="lg:col-span-3">
                        {loading ? (
                            <div className="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
                                {[...Array(8)].map((_, index) => (
                                    <div key={index} className="bg-white border border-gray-200 rounded-lg shadow-sm animate-pulse">
                                        <div className="w-full aspect-square bg-gray-200"></div>
                                        <div className="p-3">
                                            <div className="h-4 bg-gray-200 rounded mb-2"></div>
                                            <div className="h-3 bg-gray-200 rounded mb-2 w-3/4"></div>
                                            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : products.length > 0 ? (
                            <div className="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
                                {products.map((product) => (
                                    <ProductCard key={product.id} product={product} />
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-16 bg-white rounded-lg shadow-sm border">
                                <div className="max-w-md mx-auto">
                                    <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                    <h3 className="mt-4 text-lg font-medium text-gray-900">No products found</h3>
                                    <p className="mt-2 text-sm text-gray-500">
                                        {query ? `No results for "${query}"` : 'No products match your current filters'}
                                    </p>
                                    <p className="mt-1 text-sm text-gray-500">Try adjusting your search or filters.</p>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </main>
            </div>
        </>
    );
};

export default ProductList;