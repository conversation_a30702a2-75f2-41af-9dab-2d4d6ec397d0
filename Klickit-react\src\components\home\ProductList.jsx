import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useLocation, useNavigate } from 'react-router-dom';
import Header from '../includes/Header';
import LoadingSpinner from '../includes/LoadingSpinner';
import { BASE_URL } from "../../utils/config";

const ProductCard = ({ product }) => {
    const navigate = useNavigate();
    return (
        <div onClick={() => navigate(`/product/${product.id}`)} className="cursor-pointer bg-white border border-gray-200 rounded-lg shadow-sm group overflow-hidden transition-all duration-300 hover:shadow-md">
            <div className="w-full aspect-square bg-white overflow-hidden p-2">
                <img
                    src={product.mainImage || '/placeholder-product.jpg'}
                    alt={product.name}
                    className="w-full h-full object-contain transition-transform duration-300 group-hover:scale-105"
                    loading="lazy"
                />
            </div>
            <div className="p-3 text-left">
                <p className="text-sm font-semibold text-gray-800 line-clamp-2 h-10">{product.name}</p>
                <div className="flex items-center mt-2">
                    <span className="bg-green-600 text-white text-xs font-bold px-2 py-0.5 rounded-sm flex items-center">
                        {product.rating ? product.rating.toFixed(1) : '0.0'}
                        <svg className="w-3 h-3 ml-0.5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" /></svg>
                    </span>
                </div>
                <div className="mt-3">
                    <span className="text-lg font-bold text-gray-900">{product.sale_price} AED</span>
                    {product.regular_price > product.sale_price && (
                        <span className="text-xs text-gray-500 line-through ml-2">{product.regular_price} AED</span>
                    )}
                </div>
            </div>
        </div>
    );
};

const ProductList = () => {
    const [products, setProducts] = useState([]);
    const [categories, setCategories] = useState([]);
    const [brands, setBrands] = useState([]);
    const [loading, setLoading] = useState(true);

    const location = useLocation();
    const navigate = useNavigate();

    const searchParams = new URLSearchParams(location.search);
    const query = searchParams.get('q') || '';
    const [selectedCategory, setSelectedCategory] = useState(searchParams.get('category') || '');
    const [selectedBrand, setSelectedBrand] = useState(searchParams.get('brand') || '');
    const [sortBy, setSortBy] = useState(searchParams.get('sort') || '');

    useEffect(() => {
        const fetchSearchData = async () => {
            setLoading(true);
            try {
                const params = { q: query, category: selectedCategory, brand: selectedBrand, sort: sortBy };
                const response = await axios.get(`${BASE_URL}/api/search/`, { params });
                setProducts(response.data.products || []);
                setCategories(response.data.categories || []);
                setBrands(response.data.brands || []);
            } catch (error) {
                console.error('Error fetching search data:', error);
            } finally {
                setLoading(false);
            }
        };
        fetchSearchData();

        const newSearch = new URLSearchParams();
        if (query) newSearch.set('q', query);
        if (selectedCategory) newSearch.set('category', selectedCategory);
        if (selectedBrand) newSearch.set('brand', selectedBrand);
        if (sortBy) newSearch.set('sort', sortBy);
        navigate(`${location.pathname}?${newSearch.toString()}`, { replace: true });

    }, [query, selectedCategory, selectedBrand, sortBy]);

    if (loading) return <LoadingSpinner />;

    return (
        <>
            <Header />
            <div className="bg-gray-50">
            <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-28 pb-12">
                <h1 className="text-2xl font-bold text-gray-900 mb-6">
                    {query ? `Search Results for "${query}"` : "Products"}
                </h1>
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-x-8 gap-y-10">
                    <aside className="hidden lg:block">
                        <h2 className="sr-only">Filters</h2>
                        <div className="space-y-6 bg-white p-4 rounded-lg shadow-sm border">
                            <div>
                                <label htmlFor="category" className="block text-sm font-medium text-gray-700">Category</label>
                                <select id="category" value={selectedCategory} onChange={(e) => setSelectedCategory(e.target.value)} className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                    <option value="">All</option>
                                    {categories.map(cat => <option key={cat.id} value={cat.name}>{cat.name}</option>)}
                                </select>
                            </div>
                            <div>
                                <label htmlFor="brand" className="block text-sm font-medium text-gray-700">Brand</label>
                                <select id="brand" value={selectedBrand} onChange={(e) => setSelectedBrand(e.target.value)} className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                    <option value="">All</option>
                                    {brands.map(b => <option key={b.id} value={b.name}>{b.name}</option>)}
                                </select>
                            </div>
                            <div>
                                <label htmlFor="sort" className="block text-sm font-medium text-gray-700">Sort By</label>
                                <select id="sort" value={sortBy} onChange={(e) => setSortBy(e.target.value)} className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                    <option value="">Default</option>
                                    <option value="price_high_to_low">Price: High to Low</option>
                                    <option value="price_low_to_high">Price: Low to High</option>
                                </select>
                            </div>
                        </div>
                    </aside>

                    <div className="lg:col-span-3">
                        {products.length > 0 ? (
                            <div className="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-6">
                                {products.map((product) => (
                                    <ProductCard key={product.id} product={product} />
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-16 bg-white rounded-lg shadow-sm border">
                                <h3 className="text-lg font-medium text-gray-900">No products found</h3>
                                <p className="mt-1 text-sm text-gray-500">Try adjusting your search or filters.</p>
                            </div>
                        )}
                    </div>
                </div>
            </main>
            </div>
        </>
    );
};

export default ProductList;