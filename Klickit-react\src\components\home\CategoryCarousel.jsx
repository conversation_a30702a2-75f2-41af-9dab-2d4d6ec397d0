import React, { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import '../../index.css';
import AttractiveSectionLoader from "../includes/AttractiveSectionLoader";
import { BASE_URL } from "../../utils/config";

const CategoryCarousel = () => {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const sliderRef = useRef(null);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch(`${BASE_URL}/`);
        if (!response.ok) {
          throw new Error('Failed to fetch categories');
        }
        const data = await response.json();
        console.log('API Response:', data);

        setCategories(data.categories || data.category || []);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching categories:', error);
        setCategories([]);
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  const slideLeft = () => {
    if (sliderRef.current) {
      sliderRef.current.scrollBy({ left: -200, behavior: "smooth" });
    }
  };

  const slideRight = () => {
    if (sliderRef.current) {
      sliderRef.current.scrollBy({ left: 200, behavior: "smooth" });
    }
  };

  if (loading) return <AttractiveSectionLoader />;

  return (
    <section className="bg-gray-100 py-2 md:py-5">
      <div className="relative flex items-center wrapper">
        {/* Left Button */}
        <button
          onClick={slideLeft}
          className="absolute left-0 z-10 bg-gray-200 p-[2px] sm:p-1 md:p-2 rounded-full shadow-lg focus:outline-none"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 lg:h-6 w-2 md:w-4 lg:w-6 text-gray-600"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>

        {/* Scrollable Categories */}
        <div
          id="sliderthree"
          ref={sliderRef}
          className="flex swip-container justify-start w-full overflow-x-auto scrollbar-hide"
        >
          {categories.length > 0 ? (
            categories.map((category, index) => (
              <div
                className="group hover:shadow-md transition-shadow duration-300 rounded-lg bg-white p-2 sm:p-2 md:p-3 lg:p-4 flex flex-col items-center min-w-[50px] sm:min-w-[65px] md:min-w-[90px] lg:min-w-[125px] max-w-[50px] sm:max-w-[65px] md:max-w-[90px] lg:max-w-[125px] mr-[20px]"
                key={index}
              >
                <a onClick={() => navigate(`/category/${category.id}`)} className="flex cursor-pointer flex-col items-center">
                  <div className="rounded-full p-[6px] sm:p-[10px] md:p-[10px] lg:p-[16px] mb-[6px] md:mb-2 lg:mb-3 border border-gray-300 group-hover:scale-105 transform transition duration-300">
                    <img
                      src={category.image ? `${BASE_URL}${category.image}` : "/fallback-image.jpg"}
                      alt={category.name}
                      className="w-[20px] sm:w-[25px] md:w-[30px] lg:w-[40px] h-[20px] sm:h-[25px] md:h-[30px] lg:h-[40px]"
                    />
                  </div>
                  <p className="text-[6px] sm:text-[8px] leading-[8px] md:leading-[14px] md:text-xs lg:text-sm font-medium text-gray-800 group-hover:text-gray-600 transition duration-300">
                    {category.name}
                  </p>
                </a>
              </div>
            ))
          ) : (
            <p className="text-gray-500 text-sm">No categories available</p>
          )}
        </div>

        {/* Right Button */}
        <button
          onClick={slideRight}
          className="absolute right-0 z-10 bg-gray-200 p-[2px] sm:p-1 md:p-2 rounded-full shadow-lg focus:outline-none"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 lg:h-6 w-2 md:w-4 lg:w-6 text-gray-600"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M9 5l7 7-7 7"
            />
          </svg>
        </button>
      </div>
    </section>
  );
};

export default CategoryCarousel;
