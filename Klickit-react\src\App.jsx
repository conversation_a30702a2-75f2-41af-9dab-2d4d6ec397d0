import { useState, useEffect } from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import './App.css';
import './index.css';
import Login from './components/auth/Login';
import Register from './components/auth/Register';
import VerifyOTP from './components/auth/VerifyOTP';
import IndexPage from './components/home/<USER>';
import Product from './components/item/Product';
import Cart from './components/prose/Cart';
import Checkout from './components/prose/Checkout';
import Category from './components/exi/Category';
import Brand from './components/exi/Brand';
import Service from './components/exi/Service';
import Wishlist from './components/exi/Wishlist';
import Orders from './components/exi/Orders';
import OrderDetails from './components/exi/OrderDetails';
import AddressList from './components/prose/AddressList';
import AddEditAddress from './components/prose/AddEditAddress';
import Account from './components/exi/Account';
import AddReview from './components/exi/AddReview';
import ProductList from './components/home/<USER>';
import ForgotPassword from './components/auth/ForgotPassword';
import ResetPassword from './components/auth/ResetPassword';
import BecomeSeller from './components/exi/BecomeSeller';
import axios from 'axios';
import { refreshToken } from './utils/auth';  
import PaymentForm from './components/prose/PaymentForm';
import OrderVerification from './pages/OrderVerification';
import OrderSuccess from './pages/OrderSuccess';

function App() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAuthStatus = async () => {
      let token = localStorage.getItem("access_token");

      if (!token) {
        token = await refreshToken(); 
      }

      if (token) {
        setIsLoggedIn(true);
        axios.defaults.headers.common["Authorization"] = `Bearer ${token}`;
      } else {
        setIsLoggedIn(false);
      }
      setLoading(false);
    };

    checkAuthStatus();
  }, []);

  if (loading) {
    return <p className="text-center mt-10">Checking authentication...</p>; 
  }

  return (
    <BrowserRouter>
      <Routes>
        <Route path="/login" element={<Login setIsLoggedIn={setIsLoggedIn} />} />
        <Route path="/register" element={<Register setIsLoggedIn={setIsLoggedIn} />} />
        <Route path="/verify-otp" element={<VerifyOTP email="<EMAIL>" setIsLoggedIn={setIsLoggedIn} />} />
        <Route path="/forgot-password" element={<ForgotPassword />} />
        <Route path="/reset-password/:uidb64/:token/" element={<ResetPassword />} />
        
        <Route path="/" element={<IndexPage />} />
        <Route path="/search" element={<ProductList />} />
        <Route path="/product/:id" element={<Product />} />
        <Route path="/cart" element={isLoggedIn ? <Cart key={location.pathname + location.search} /> : <Navigate to="/login" />} />
        <Route path="/checkout" element={isLoggedIn ? <Checkout /> : <Navigate to="/login" />} />
        <Route path="/category/:id" element={<Category />} />
        <Route path="/brand-page/:id/" element={<Brand />} />
        <Route path="/service/" element={<Service />} />
        <Route path="/wishlist" element={isLoggedIn ? <Wishlist /> : <Navigate to="/login" />} />
        <Route path="/orders" element={isLoggedIn ? <Orders /> : <Navigate to="/login" />} />
        <Route path="/order/:id/" element={isLoggedIn ? <OrderDetails /> : <Navigate to="/login" />} />
        <Route path="/addresses" element={isLoggedIn ? <AddressList /> : <Navigate to="/login" />} />
        <Route path="/addresses/:id/edit/" element={isLoggedIn ? <AddEditAddress /> : <Navigate to="/login" />} />
        <Route path="/addresses/add/" element={isLoggedIn ? <AddEditAddress /> : <Navigate to="/login" />} />
        <Route path="/account" element={isLoggedIn ? <Account /> : <Navigate to="/login" />} />
        <Route path="/product/:id/reviews/" element={isLoggedIn ? <AddReview /> : <Navigate to="/login" />} />
        <Route path="/become-seller" element={isLoggedIn ? <BecomeSeller /> : <Navigate to="/login" />} />
        <Route path="/payment" element={<PaymentForm />} />
        <Route path="/order/success" element={isLoggedIn ? <OrderVerification /> : <Navigate to="/login" />} />
        <Route path="/order-success/:orderId" element={isLoggedIn ? <OrderSuccess /> : <Navigate to="/login" />} />
      </Routes>
    </BrowserRouter>
  );
}

export default App;
